#!/bin/bash

echo "========================================"
echo "InStock 历史数据回补工具"
echo "========================================"
echo

show_menu() {
    echo "请选择回补方式："
    echo "1. 回补最近一个月数据 (推荐首次使用)"
    echo "2. 回补指定日期区间"
    echo "3. 回补指定日期"
    echo "4. 回补多个指定日期"
    echo "5. 查看数据回补状态"
    echo "6. 退出"
    echo
}

recent_month() {
    echo
    echo "正在回补最近一个月数据..."
    echo "开始时间: 2024-07-19"
    echo "结束时间: 2024-08-19"
    echo "预计耗时: 10-15分钟"
    echo
    read -p "确认开始回补? (y/n): " confirm
    if [[ $confirm == [yY] ]]; then
        docker exec InStock python /data/InStock/instock/job/execute_daily_job.py 2024-07-19 2024-08-19
        echo "✅ 数据回补完成！"
    fi
}

date_range() {
    echo
    read -p "请输入开始日期 (格式: 2024-07-01): " start_date
    read -p "请输入结束日期 (格式: 2024-08-19): " end_date
    echo
    echo "正在回补 $start_date 到 $end_date 的数据..."
    docker exec InStock python /data/InStock/instock/job/execute_daily_job.py $start_date $end_date
    echo "✅ 数据回补完成！"
}

single_date() {
    echo
    read -p "请输入日期 (格式: 2024-08-19): " single_date
    echo
    echo "正在回补 $single_date 的数据..."
    docker exec InStock python /data/InStock/instock/job/execute_daily_job.py $single_date
    echo "✅ 数据回补完成！"
}

multiple_dates() {
    echo
    read -p "请输入多个日期，用逗号分隔 (格式: 2024-08-01,2024-08-05,2024-08-19): " multiple_dates
    echo
    echo "正在回补指定日期的数据..."
    docker exec InStock python /data/InStock/instock/job/execute_daily_job.py $multiple_dates
    echo "✅ 数据回补完成！"
}

check_status() {
    echo
    echo "📊 检查容器状态..."
    docker ps | grep InStock
    echo
    echo "📋 检查最近的处理日志..."
    docker logs InStock --tail 20
    echo
    echo "💾 检查数据库状态..."
    docker exec InStockDbService mysql -uroot -proot -e "USE instockdb; SHOW TABLES;" | wc -l
    echo
    read -p "按回车键继续..."
}

while true; do
    show_menu
    read -p "请输入选择 (1-6): " choice
    
    case $choice in
        1) recent_month ;;
        2) date_range ;;
        3) single_date ;;
        4) multiple_dates ;;
        5) check_status ;;
        6) echo "退出数据回补工具"; exit 0 ;;
        *) echo "❌ 无效选择，请重新输入" ;;
    esac
    echo
done
