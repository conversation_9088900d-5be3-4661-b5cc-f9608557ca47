@echo off
echo ========================================
echo InStock Docker 部署脚本
echo ========================================

echo 1. 检查Docker状态...
docker --version
if %errorlevel% neq 0 (
    echo 错误: Docker未安装或未启动
    pause
    exit /b 1
)

echo 2. 检查PostgreSQL数据库连接...
docker exec seat-postgres psql -U seat -d instockdb -c "SELECT 1;" >nul 2>&1
if %errorlevel% neq 0 (
    echo 警告: PostgreSQL数据库连接失败，请确保数据库正在运行
)

echo 3. 构建InStock镜像...
docker build -f Dockerfile.local -t instock:local .
if %errorlevel% neq 0 (
    echo 错误: 镜像构建失败
    pause
    exit /b 1
)

echo 4. 停止现有容器...
docker stop InStock 2>nul
docker rm InStock 2>nul

echo 5. 启动InStock容器...
docker run -d ^
    --name InStock ^
    -p 9988:9988 ^
    -e db_type=postgresql ^
    -e db_host=host.docker.internal ^
    -e db_user=seat ^
    -e db_password=seat ^
    -e db_database=instockdb ^
    -e db_port=5432 ^
    --add-host host.docker.internal:host-gateway ^
    instock:local

if %errorlevel% eq 0 (
    echo ========================================
    echo ✅ InStock 部署成功！
    echo 🌐 访问地址: http://localhost:9988
    echo ========================================
    echo 正在等待服务启动...
    timeout /t 10 /nobreak >nul
    start http://localhost:9988
) else (
    echo ❌ 部署失败
)

pause
