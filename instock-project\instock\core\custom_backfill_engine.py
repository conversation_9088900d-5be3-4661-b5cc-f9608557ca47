#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
个性化按需回补引擎
基于官方回补代码，提供个性化按需回补能力
既复用官方回补的必要顺序，也允许进行个性化按需回补
"""

import time
import datetime
import concurrent.futures
import logging
import os.path
import sys
from typing import List, Dict, Set, Optional
from enum import Enum

# 添加项目路径
cpath_current = os.path.dirname(os.path.dirname(__file__))
cpath = os.path.abspath(os.path.join(cpath_current, os.pardir))
sys.path.append(cpath)

__author__ = 'InStock Team'
__date__ = '2025/8/19'


class BackfillModule(Enum):
    """回补模块枚举"""
    INIT = "init_job"                           # 数据库初始化
    BASIC_DATA = "basic_data_daily_job"         # 基础股票数据
    SELECTION_DATA = "selection_data_daily_job" # 综合选股数据
    OTHER_DATA = "basic_data_other_daily_job"   # 其他基础数据
    INDICATORS = "indicators_data_daily_job"    # 技术指标数据
    KLINE_PATTERN = "klinepattern_data_daily_job" # K线形态数据
    STRATEGY = "strategy_data_daily_job"        # 策略数据
    BACKTEST = "backtest_data_daily_job"        # 回测数据
    AFTER_CLOSE = "basic_data_after_close_daily_job" # 闭盘后数据


class ModuleDependency:
    """模块依赖关系管理"""
    
    # 模块依赖关系定义
    DEPENDENCIES = {
        BackfillModule.INIT: set(),  # 无依赖
        BackfillModule.BASIC_DATA: {BackfillModule.INIT},
        BackfillModule.SELECTION_DATA: {BackfillModule.INIT, BackfillModule.BASIC_DATA},
        BackfillModule.OTHER_DATA: {BackfillModule.INIT, BackfillModule.BASIC_DATA},
        BackfillModule.INDICATORS: {BackfillModule.INIT, BackfillModule.BASIC_DATA},
        BackfillModule.KLINE_PATTERN: {BackfillModule.INIT, BackfillModule.BASIC_DATA},
        BackfillModule.STRATEGY: {BackfillModule.INIT, BackfillModule.BASIC_DATA, BackfillModule.INDICATORS},
        BackfillModule.BACKTEST: {BackfillModule.INIT, BackfillModule.BASIC_DATA, BackfillModule.STRATEGY},
        BackfillModule.AFTER_CLOSE: {BackfillModule.INIT, BackfillModule.BASIC_DATA},
    }
    
    # 模块信息
    MODULE_INFO = {
        BackfillModule.INIT: {
            "name": "数据库初始化",
            "description": "创建数据库和基础表结构",
            "estimated_time": 5,
            "category": "基础设施",
            "required": True
        },
        BackfillModule.BASIC_DATA: {
            "name": "股票基础数据",
            "description": "股票实时行情、ETF数据",
            "estimated_time": 300,
            "category": "基础数据",
            "required": True
        },
        BackfillModule.SELECTION_DATA: {
            "name": "综合选股数据",
            "description": "综合选股结果数据",
            "estimated_time": 180,
            "category": "选股数据",
            "required": False
        },
        BackfillModule.OTHER_DATA: {
            "name": "其他基础数据",
            "description": "资金流向、龙虎榜、分红配送等",
            "estimated_time": 240,
            "category": "基础数据",
            "required": False
        },
        BackfillModule.INDICATORS: {
            "name": "技术指标数据",
            "description": "MACD、KDJ、RSI等70+技术指标",
            "estimated_time": 600,
            "category": "技术分析",
            "required": False
        },
        BackfillModule.KLINE_PATTERN: {
            "name": "K线形态数据",
            "description": "61种K线形态识别",
            "estimated_time": 300,
            "category": "技术分析",
            "required": False
        },
        BackfillModule.STRATEGY: {
            "name": "策略数据",
            "description": "放量上涨、均线多头等策略",
            "estimated_time": 420,
            "category": "交易策略",
            "required": False
        },
        BackfillModule.BACKTEST: {
            "name": "回测数据",
            "description": "策略回测和收益分析",
            "estimated_time": 900,
            "category": "分析结果",
            "required": False
        },
        BackfillModule.AFTER_CLOSE: {
            "name": "闭盘后数据",
            "description": "大宗交易等闭盘后数据",
            "estimated_time": 120,
            "category": "基础数据",
            "required": False
        }
    }
    
    # 并发执行组
    CONCURRENT_GROUPS = [
        {BackfillModule.OTHER_DATA, BackfillModule.INDICATORS, 
         BackfillModule.KLINE_PATTERN, BackfillModule.STRATEGY}
    ]
    
    @classmethod
    def get_dependencies(cls, modules: Set[BackfillModule]) -> Set[BackfillModule]:
        """获取模块的所有依赖（递归）"""
        all_deps = set()
        
        def add_deps(module):
            if module in all_deps:
                return
            all_deps.add(module)
            for dep in cls.DEPENDENCIES.get(module, set()):
                add_deps(dep)
        
        for module in modules:
            add_deps(module)
        
        return all_deps
    
    @classmethod
    def get_execution_plan(cls, selected_modules: Set[BackfillModule]) -> List[List[BackfillModule]]:
        """生成执行计划（按依赖关系排序，支持并发）"""
        # 获取所有需要执行的模块（包括依赖）
        all_modules = cls.get_dependencies(selected_modules)
        
        # 按依赖关系分层
        execution_plan = []
        remaining = all_modules.copy()
        
        while remaining:
            # 找到当前可以执行的模块（依赖都已满足）
            current_layer = []
            for module in remaining.copy():
                deps = cls.DEPENDENCIES.get(module, set())
                if deps.issubset(all_modules - remaining):
                    current_layer.append(module)
                    remaining.remove(module)
            
            if not current_layer:
                # 防止死循环
                break
            
            # 检查是否可以并发执行
            concurrent_layer = []
            for group in cls.CONCURRENT_GROUPS:
                group_modules = [m for m in current_layer if m in group]
                if len(group_modules) > 1:
                    concurrent_layer.extend(group_modules)
                    for m in group_modules:
                        current_layer.remove(m)
            
            # 添加串行执行的模块
            for module in current_layer:
                execution_plan.append([module])
            
            # 添加并发执行的模块
            if concurrent_layer:
                execution_plan.append(concurrent_layer)
        
        return execution_plan


class CustomBackfillEngine:
    """个性化按需回补引擎"""
    
    def __init__(self):
        self.dependency_manager = ModuleDependency()
        self.current_task = None
        self.task_logs = []
    
    def analyze_selection(self, selected_modules: List[str]) -> Dict:
        """分析用户选择，返回执行计划"""
        # 转换为枚举
        modules = set()
        for module_name in selected_modules:
            try:
                module = BackfillModule(module_name)
                modules.add(module)
            except ValueError:
                continue
        
        # 获取完整依赖
        all_modules = self.dependency_manager.get_dependencies(modules)
        
        # 生成执行计划
        execution_plan = self.dependency_manager.get_execution_plan(modules)
        
        # 计算统计信息
        total_time = sum(
            self.dependency_manager.MODULE_INFO[module]["estimated_time"]
            for module in all_modules
        )
        
        # 分析新增的依赖模块
        auto_added = all_modules - modules
        
        return {
            "selected_modules": list(modules),
            "all_modules": list(all_modules),
            "auto_added_dependencies": list(auto_added),
            "execution_plan": execution_plan,
            "total_modules": len(all_modules),
            "estimated_time": total_time,
            "module_info": {
                module.value: self.dependency_manager.MODULE_INFO[module]
                for module in all_modules
            }
        }
    
    def execute_backfill(self, selected_modules: List[str], start_date=None, end_date=None, 
                        progress_callback=None) -> bool:
        """执行个性化回补"""
        try:
            # 分析执行计划
            plan = self.analyze_selection(selected_modules)
            execution_plan = plan["execution_plan"]
            
            start_time = time.time()
            total_steps = len(execution_plan)
            
            self._log(f"开始执行个性化回补，共{total_steps}个阶段")
            
            # 按计划执行
            for step_idx, step_modules in enumerate(execution_plan):
                step_progress = int((step_idx / total_steps) * 100)
                
                if len(step_modules) == 1:
                    # 串行执行
                    module = step_modules[0]
                    self._log(f"执行模块: {self.dependency_manager.MODULE_INFO[module]['name']}")
                    
                    if progress_callback:
                        progress_callback(step_progress, f"执行: {module.value}")
                    
                    success = self._execute_single_module(module, start_date, end_date)
                    if not success:
                        self._log(f"模块 {module.value} 执行失败")
                        return False
                else:
                    # 并发执行
                    self._log(f"并发执行 {len(step_modules)} 个模块")
                    
                    if progress_callback:
                        progress_callback(step_progress, f"并发执行 {len(step_modules)} 个模块")
                    
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        futures = []
                        for module in step_modules:
                            future = executor.submit(
                                self._execute_single_module, module, start_date, end_date
                            )
                            futures.append((module, future))
                        
                        # 等待所有任务完成
                        for module, future in futures:
                            success = future.result()
                            if not success:
                                self._log(f"模块 {module.value} 执行失败")
                                return False
            
            total_time = time.time() - start_time
            self._log(f"个性化回补完成，总耗时: {total_time:.2f} 秒")
            
            if progress_callback:
                progress_callback(100, "回补完成")
            
            return True
            
        except Exception as e:
            self._log(f"个性化回补执行异常: {e}")
            return False
    
    def _execute_single_module(self, module: BackfillModule, start_date=None, end_date=None) -> bool:
        """执行单个模块"""
        try:
            # 动态导入模块
            module_name = module.value
            job_module = __import__(f"instock.job.{module_name}", fromlist=[module_name])
            
            # 执行模块的main函数
            if hasattr(job_module, 'main'):
                job_module.main()
                self._log(f"✅ {module.value} 执行成功")
                return True
            else:
                self._log(f"❌ {module.value} 缺少main函数")
                return False
                
        except Exception as e:
            self._log(f"❌ {module.value} 执行异常: {e}")
            return False
    
    def _log(self, message: str):
        """记录日志"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.task_logs.append(log_entry)
        logging.info(log_entry)
    
    def get_module_categories(self) -> Dict:
        """获取模块分类信息"""
        categories = {}
        for module, info in self.dependency_manager.MODULE_INFO.items():
            category = info["category"]
            if category not in categories:
                categories[category] = []
            categories[category].append({
                "module": module.value,
                "name": info["name"],
                "description": info["description"],
                "estimated_time": info["estimated_time"],
                "required": info["required"]
            })
        return categories


# 全局实例
custom_backfill_engine = CustomBackfillEngine()
