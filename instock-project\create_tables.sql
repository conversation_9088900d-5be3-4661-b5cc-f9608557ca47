-- 创建股票基础数据表
USE instockdb;

CREATE TABLE IF NOT EXISTS `cn_stock_spot` (
  `date` DATE NOT NULL,
  `code` VARCHAR(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `name` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `new_price` FLOAT NULL,
  `change_rate` FLOAT NULL,
  `ups_downs` FLOAT NULL,
  `volume` BIGINT NULL,
  `deal_amount` BIGINT NULL,
  `amplitude` FLOAT NULL,
  `turnoverrate` FLOAT NULL,
  `volume_ratio` FLOAT NULL,
  `open_price` FLOAT NULL,
  `high_price` FLOAT NULL,
  `low_price` FLOAT NULL,
  `pre_close_price` FLOAT NULL,
  `total_market_cap` BIGINT NULL,
  `free_cap` BIGINT NULL,
  `industry` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `listing_date` DATE NULL,
  PRIMARY KEY (`date`, `code`) USING BTREE,
  INDEX `INIX_CODE`(`code`) USING BTREE,
  INDEX `INIX_DATE`(`date`) USING BTREE
) CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- 创建ETF数据表
CREATE TABLE IF NOT EXISTS `cn_etf_spot` (
  `date` DATE NOT NULL,
  `code` VARCHAR(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `name` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `new_price` FLOAT NULL,
  `change_rate` FLOAT NULL,
  `ups_downs` FLOAT NULL,
  `volume` BIGINT NULL,
  `deal_amount` BIGINT NULL,
  `open_price` FLOAT NULL,
  `high_price` FLOAT NULL,
  `low_price` FLOAT NULL,
  `pre_close_price` FLOAT NULL,
  PRIMARY KEY (`date`, `code`) USING BTREE,
  INDEX `INIX_CODE`(`code`) USING BTREE,
  INDEX `INIX_DATE`(`date`) USING BTREE
) CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- 插入一些示例数据
INSERT INTO `cn_stock_spot` (`date`, `code`, `name`, `new_price`, `change_rate`, `ups_downs`, `volume`, `deal_amount`, `amplitude`, `turnoverrate`, `volume_ratio`, `open_price`, `high_price`, `low_price`, `pre_close_price`, `total_market_cap`, `free_cap`, `industry`, `listing_date`) VALUES
('2025-08-19', '000001', '平安银行', 12.50, 2.45, 0.30, 15000000, 187500000, 3.2, 1.8, 1.2, 12.20, 12.60, 12.15, 12.20, 24200000000, 24200000000, '银行', '1991-04-03'),
('2025-08-19', '000002', '万科A', 8.95, -1.32, -0.12, 25000000, 223750000, 2.8, 2.1, 0.9, 9.07, 9.10, 8.90, 9.07, 98500000000, 98500000000, '房地产', '1991-01-29'),
('2025-08-19', '600000', '浦发银行', 7.85, 1.29, 0.10, 18000000, 141300000, 2.5, 1.5, 1.1, 7.75, 7.90, 7.70, 7.75, 23000000000, 23000000000, '银行', '1999-11-10'),
('2025-08-19', '600036', '招商银行', 35.20, 0.86, 0.30, 12000000, 422400000, 1.8, 0.8, 1.0, 34.90, 35.50, 34.80, 34.90, 89000000000, 89000000000, '银行', '2002-04-09'),
('2025-08-19', '000858', '五粮液', 128.50, -0.77, -1.00, 8000000, 1028000000, 2.1, 0.6, 0.8, 129.50, 130.00, 127.80, 129.50, 49800000000, 38500000000, '食品饮料', '1998-04-27');

INSERT INTO `cn_etf_spot` (`date`, `code`, `name`, `new_price`, `change_rate`, `ups_downs`, `volume`, `deal_amount`, `open_price`, `high_price`, `low_price`, `pre_close_price`) VALUES
('2025-08-19', '510050', '50ETF', 2.856, 1.42, 0.040, 45000000, 128520000, 2.816, 2.860, 2.810, 2.816),
('2025-08-19', '510300', '300ETF', 4.125, 0.98, 0.040, 35000000, 144375000, 4.085, 4.130, 4.080, 4.085),
('2025-08-19', '159919', '300ETF', 1.456, 0.83, 0.012, 28000000, 40768000, 1.444, 1.460, 1.440, 1.444),
('2025-08-19', '512880', '证券ETF', 0.892, 2.18, 0.019, 52000000, 46384000, 0.873, 0.895, 0.870, 0.873);
