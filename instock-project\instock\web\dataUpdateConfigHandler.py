#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据更新配置管理Handler
提供数据模块配置的Web接口
"""

import json
import logging
import subprocess
import threading
import time
from abc import ABC
from datetime import datetime, timedelta
from tornado import gen
import instock.web.base as webBase
import instock.core.data_update_config as duc

__author__ = 'InStock Team'
__date__ = '2025/8/19'

# 任务管理器
task_manager = {
    'current_task': None,
    'task_history': [],
    'task_logs': []
}


class DataUpdateConfigHandler(webBase.BaseHandler, ABC):
    """数据更新配置管理页面"""
    
    @gen.coroutine
    def get(self):
        self.render("data_update_config.html",
                    leftMenu=webBase.GetLeftMenu(self.request.uri))


class DataUpdateConfigApiHandler(webBase.BaseHandler, ABC):
    """数据更新配置API接口"""
    
    @gen.coroutine
    def post(self):
        action = self.get_argument('action', '')
        
        if action == 'get_configs':
            self._get_configs()
        elif action == 'update_config':
            self._update_config()
        elif action == 'get_categories':
            self._get_categories()
        elif action == 'get_execution_plan':
            self._get_execution_plan()
        elif action == 'start_update':
            self._start_update()
        elif action == 'stop_update':
            self._stop_update()
        elif action == 'get_status':
            self._get_task_status()
        elif action == 'batch_update':
            self._batch_update()
        else:
            self._send_error("未知操作")
    
    def _get_configs(self):
        """获取所有模块配置"""
        try:
            configs_by_category = duc.config_manager.get_modules_by_category()
            categories = duc.DATA_CATEGORIES
            
            self._send_success({
                'configs': configs_by_category,
                'categories': categories
            })
        except Exception as e:
            logging.error(f"获取配置失败: {e}")
            self._send_error(f"获取配置失败: {str(e)}")
    
    def _update_config(self):
        """更新单个模块配置"""
        try:
            module_id = self.get_argument('module_id', '')
            enabled = self.get_argument('enabled', None)
            priority = self.get_argument('priority', None)
            update_frequency = self.get_argument('update_frequency', None)
            
            if not module_id:
                self._send_error("模块ID不能为空")
                return
            
            # 转换参数类型
            if enabled is not None:
                enabled = enabled.lower() == 'true'
            if priority is not None:
                priority = int(priority)
            
            success = duc.config_manager.update_module_config(
                module_id=module_id,
                enabled=enabled,
                priority=priority,
                update_frequency=update_frequency
            )
            
            if success:
                self._send_success("配置更新成功")
            else:
                self._send_error("配置更新失败")
                
        except Exception as e:
            logging.error(f"更新配置失败: {e}")
            self._send_error(f"更新配置失败: {str(e)}")
    
    def _batch_update(self):
        """批量更新配置"""
        try:
            configs_json = self.get_argument('configs', '[]')
            configs = json.loads(configs_json)
            
            success_count = 0
            for config in configs:
                module_id = config.get('module_id')
                enabled = config.get('enabled')
                priority = config.get('priority')
                update_frequency = config.get('update_frequency')
                
                if duc.config_manager.update_module_config(
                    module_id=module_id,
                    enabled=enabled,
                    priority=priority,
                    update_frequency=update_frequency
                ):
                    success_count += 1
            
            self._send_success(f"成功更新 {success_count}/{len(configs)} 个配置")
            
        except Exception as e:
            logging.error(f"批量更新配置失败: {e}")
            self._send_error(f"批量更新配置失败: {str(e)}")
    
    def _get_categories(self):
        """获取数据类别信息"""
        self._send_success(duc.DATA_CATEGORIES)
    
    def _get_execution_plan(self):
        """获取执行计划"""
        try:
            execution_plan = duc.config_manager.get_execution_plan()
            
            # 计算总预估时间
            total_time = sum(module.get('estimated_time', 0) for module in execution_plan)
            
            self._send_success({
                'execution_plan': execution_plan,
                'total_modules': len(execution_plan),
                'total_estimated_time': total_time
            })
            
        except Exception as e:
            logging.error(f"获取执行计划失败: {e}")
            self._send_error(f"获取执行计划失败: {str(e)}")
    
    def _start_update(self):
        """启动数据更新任务"""
        try:
            if task_manager['current_task'] is not None:
                self._send_error("已有任务在运行中")
                return
            
            # 获取选中的模块
            selected_modules_json = self.get_argument('modules', '[]')
            selected_modules = json.loads(selected_modules_json)
            
            if not selected_modules:
                # 如果没有指定模块，使用所有启用的模块
                execution_plan = duc.config_manager.get_execution_plan()
                selected_modules = [module['module_id'] for module in execution_plan]
            
            # 创建任务信息
            task_id = f"task_{int(time.time())}"
            task_info = {
                'id': task_id,
                'modules': selected_modules,
                'status': 'running',
                'start_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'progress': 0,
                'current_module': None,
                'logs': []
            }
            
            task_manager['current_task'] = task_info
            
            # 在后台线程中执行任务
            thread = threading.Thread(target=self._execute_update_task, args=(task_info,))
            thread.daemon = True
            thread.start()
            
            self._send_success({
                'task_id': task_id,
                'message': '数据更新任务已启动'
            })
            
        except Exception as e:
            logging.error(f"启动更新任务失败: {e}")
            self._send_error(f"启动更新任务失败: {str(e)}")
    
    def _execute_update_task(self, task_info):
        """执行数据更新任务"""
        try:
            selected_modules = task_info['modules']
            execution_plan = duc.config_manager.get_execution_plan()
            
            # 过滤出选中的模块
            modules_to_execute = [
                module for module in execution_plan 
                if module['module_id'] in selected_modules
            ]
            
            total_modules = len(modules_to_execute)
            
            for i, module in enumerate(modules_to_execute):
                if task_manager['current_task'] is None:
                    # 任务被取消
                    break
                
                module_id = module['module_id']
                module_name = module['name']
                
                # 更新任务状态
                task_info['current_module'] = module_name
                task_info['progress'] = int((i / total_modules) * 100)
                
                # 添加日志
                log_entry = {
                    'time': datetime.now().strftime("%H:%M:%S"),
                    'message': f"开始执行: {module_name}"
                }
                task_info['logs'].append(log_entry)
                
                # 执行模块
                success = self._execute_module(module, task_info)
                
                if success:
                    log_entry = {
                        'time': datetime.now().strftime("%H:%M:%S"),
                        'message': f"✅ {module_name} 执行成功"
                    }
                else:
                    log_entry = {
                        'time': datetime.now().strftime("%H:%M:%S"),
                        'message': f"❌ {module_name} 执行失败"
                    }
                
                task_info['logs'].append(log_entry)
            
            # 任务完成
            task_info['status'] = 'completed'
            task_info['progress'] = 100
            task_info['end_time'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            task_info['current_module'] = None
            
            # 移动到历史记录
            task_manager['task_history'].append(task_info.copy())
            task_manager['current_task'] = None
            
        except Exception as e:
            logging.error(f"执行更新任务失败: {e}")
            task_info['status'] = 'failed'
            task_info['end_time'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            task_info['logs'].append({
                'time': datetime.now().strftime("%H:%M:%S"),
                'message': f"任务执行失败: {str(e)}"
            })
            
            # 移动到历史记录
            task_manager['task_history'].append(task_info.copy())
            task_manager['current_task'] = None
    
    def _execute_module(self, module, task_info):
        """执行单个数据模块"""
        try:
            job_module = module['job_module']
            job_function = module['job_function']
            
            # 构建Python执行命令
            python_path = "/usr/local/bin/python3"
            script_path = f"/data/InStock/instock/job/{job_module}.py"
            
            # 设置环境变量
            import os
            env = os.environ.copy()
            env.update({
                'PYTHONPATH': '/data/InStock',
                'PATH': '/usr/local/bin:/usr/bin:/bin',
                'db_host': os.environ.get('db_host', 'InStockDbService'),
                'db_user': os.environ.get('db_user', 'root'),
                'db_password': os.environ.get('db_password', 'root'),
                'db_database': os.environ.get('db_database', 'instockdb'),
                'db_port': os.environ.get('db_port', '3306'),
            })
            
            # 执行命令
            process = subprocess.Popen(
                [python_path, script_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                env=env,
                cwd='/data/InStock/instock/job'
            )
            
            # 等待执行完成
            stdout, _ = process.communicate(timeout=module.get('estimated_time', 300))
            
            if process.returncode == 0:
                return True
            else:
                # 记录错误日志
                error_log = {
                    'time': datetime.now().strftime("%H:%M:%S"),
                    'message': f"执行错误: {stdout[-200:] if stdout else '未知错误'}"
                }
                task_info['logs'].append(error_log)
                return False
                
        except subprocess.TimeoutExpired:
            process.kill()
            task_info['logs'].append({
                'time': datetime.now().strftime("%H:%M:%S"),
                'message': f"执行超时: {module['name']}"
            })
            return False
        except Exception as e:
            task_info['logs'].append({
                'time': datetime.now().strftime("%H:%M:%S"),
                'message': f"执行异常: {str(e)}"
            })
            return False
    
    def _stop_update(self):
        """停止数据更新任务"""
        try:
            if task_manager['current_task'] is None:
                self._send_error("没有正在运行的任务")
                return
            
            # 标记任务为停止状态
            task_manager['current_task']['status'] = 'stopped'
            task_manager['current_task']['end_time'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # 移动到历史记录
            task_manager['task_history'].append(task_manager['current_task'].copy())
            task_manager['current_task'] = None
            
            self._send_success("任务已停止")
            
        except Exception as e:
            logging.error(f"停止任务失败: {e}")
            self._send_error(f"停止任务失败: {str(e)}")
    
    def _get_task_status(self):
        """获取任务状态"""
        try:
            current = task_manager['current_task']
            history = task_manager['task_history'][-10:]  # 最近10个任务
            
            # 清理不能序列化的对象
            current_clean = None
            if current:
                current_clean = {k: v for k, v in current.items() if k != 'process'}
            
            history_clean = []
            for task in history:
                task_clean = {k: v for k, v in task.items() if k != 'process'}
                history_clean.append(task_clean)
            
            self._send_success({
                'current_task': current_clean,
                'task_history': history_clean
            })
            
        except Exception as e:
            logging.error(f"获取任务状态失败: {e}")
            self._send_error(f"获取任务状态失败: {str(e)}")
    
    def _send_success(self, data):
        """发送成功响应"""
        self.write(json.dumps({
            'success': True,
            'data': data
        }, ensure_ascii=False))
        self.finish()
    
    def _send_error(self, message):
        """发送错误响应"""
        self.write(json.dumps({
            'success': False,
            'message': message
        }, ensure_ascii=False))
        self.finish()
