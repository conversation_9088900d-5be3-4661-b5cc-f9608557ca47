# InStock 项目文档

本目录包含 InStock 项目的各种流程图和技术文档。

## 📁 目录结构

```
docs/
├── README.md                           # 本文件
├── flowcharts/                         # 流程图目录
│   ├── README.md                       # 流程图索引和使用说明
│   ├── official-backfill-process.mmd   # 官方数据回补流程
│   ├── custom-backfill-process.mmd     # 我们开发的图形界面回补流程
│   └── personalized-backfill-arch.mmd  # 个性化按需回补架构设计
├── analysis/                           # 分析文档
│   ├── backfill-comparison.md          # 数据回补方案对比分析
│   └── feasibility-assessment.md       # 个性化按需回补可行性评估
├── specifications/                     # 技术规范
│   └── custom-backfill-engine.md       # 个性化回补引擎规范
└── knowledge/                          # 知识库
    ├── README.md                       # 知识库索引
    ├── problem-solving-methodology.md  # 问题排查与解决方法论
    └── mermaid-accessibility/          # Mermaid无障碍设计知识
        ├── color-contrast-standards.md # 颜色对比度标准
        ├── accessibility-guidelines.md # 无障碍设计指南
        └── troubleshooting-guide.md    # 问题排查指南
```

## 🎯 流程图说明

### 1. 官方数据回补流程 (official-backfill-process.mmd)
- 展示官方提供的两种回补方案
- Shell脚本工具和直接Python脚本执行
- 完整的7步执行流程

### 2. 图形界面回补流程 (custom-backfill-process.mmd)  
- 我们开发的Web界面回补方案
- AJAX异步处理和实时监控
- 任务状态管理和日志查看

### 3. 个性化按需回补架构 (personalized-backfill-arch.mmd)
- 智能依赖关系分析
- 模块化执行引擎
- 用户界面集成方案

## 📊 如何查看流程图

### 方法一：使用Mermaid在线编辑器
1. 访问 https://mermaid.live/
2. 复制对应的.mmd文件内容
3. 粘贴到编辑器中查看

### 方法二：使用支持Mermaid的编辑器
- VS Code + Mermaid Preview 插件
- Typora
- GitLab/GitHub (原生支持)

### 方法三：使用命令行工具
```bash
# 安装mermaid-cli
npm install -g @mermaid-js/mermaid-cli

# 生成PNG图片
mmdc -i flowcharts/official-backfill-process.mmd -o official-backfill-process.png
```

## 📚 知识库亮点

### Mermaid 无障碍设计
- **颜色对比度标准**: 基于 WCAG 2.1 的专业配色方案
- **问题排查指南**: 系统性的可读性问题解决流程
- **无障碍设计指南**: 完整的无障碍设计规范和最佳实践

### 问题解决方法论
- **标准化流程**: 问题识别、根因分析、方案设计、实施验证
- **工具箱**: 诊断工具、测试工具、质量保证工具
- **经验沉淀**: 可复用的解决方案模板和知识管理体系

## 🔄 更新说明

- 创建日期：2025-08-19
- 最后更新：2025-08-19
- 维护者：InStock开发团队
- 知识库建立：2025-08-19

## 📝 贡献指南

如需添加新的流程图或文档：
1. 在对应目录下创建文件
2. 更新本README.md的目录结构
3. 确保流程图语法正确
4. 添加必要的说明文档
