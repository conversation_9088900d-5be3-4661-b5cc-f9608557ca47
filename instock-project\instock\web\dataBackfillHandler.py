#!/usr/local/bin/python3
# -*- coding: utf-8 -*-

import json
import logging
import subprocess
import threading
import time
from abc import ABC
from tornado import gen
import tornado.web
import instock.web.base as webBase
import instock.core.custom_backfill_engine as cbe

__author__ = 'Assistant'
__date__ = '2024/8/19'

# 全局任务状态管理
task_manager = {
    'current_task': None,
    'task_history': [],
    'task_logs': []
}

class DataBackfillHandler(webBase.BaseHandler, ABC):
    """数据回补管理页面"""
    
    @gen.coroutine
    def get(self):
        self.render("data_backfill.html",
                    leftMenu=webBase.GetLeftMenu(self.request.uri))

class DataBackfillApiHandler(webBase.BaseHandler, ABC):
    """数据回补API接口"""
    
    def post(self):
        """执行数据回补任务"""
        try:
            action = self.get_argument("action", default=None)
            
            if action == "start_backfill":
                self._start_backfill_task()
            elif action == "get_status":
                self._get_task_status()
            elif action == "get_logs":
                self._get_task_logs()
            elif action == "stop_task":
                self._stop_current_task()
            elif action == "analyze_selection":
                self._analyze_module_selection()
            elif action == "get_module_categories":
                self._get_module_categories()
            else:
                self._send_error("未知操作")
                
        except Exception as e:
            logging.error(f"DataBackfillApiHandler处理异常：{e}")
            self._send_error(str(e))

    def _analyze_module_selection(self):
        """分析模块选择，返回执行计划"""
        try:
            selected_modules_json = self.get_argument("selected_modules", default="[]")
            selected_modules = json.loads(selected_modules_json)

            # 使用个性化回补引擎分析
            analysis = cbe.custom_backfill_engine.analyze_selection(selected_modules)

            self._send_success(analysis)

        except Exception as e:
            logging.error(f"分析模块选择失败: {e}")
            self._send_error(f"分析失败: {str(e)}")

    def _get_module_categories(self):
        """获取模块分类信息"""
        try:
            categories = cbe.custom_backfill_engine.get_module_categories()
            self._send_success(categories)

        except Exception as e:
            logging.error(f"获取模块分类失败: {e}")
            self._send_error(f"获取分类失败: {str(e)}")

    def _start_backfill_task(self):
        """启动数据回补任务"""
        if task_manager['current_task'] and task_manager['current_task']['status'] == 'running':
            self._send_error("已有任务正在运行")
            return
        
        # 获取参数
        task_type = self.get_argument("task_type", default="full")
        start_date = self.get_argument("start_date", default="")
        end_date = self.get_argument("end_date", default="")
        dates = self.get_argument("dates", default="")
        selected_modules_json = self.get_argument("selected_modules", default="[]")
        use_custom_engine = self.get_argument("use_custom_engine", default="false").lower() == "true"
        
        # 构建命令 - 直接在容器内执行，不使用docker exec
        python_path = "/usr/local/bin/python3"
        script_path = "/data/InStock/instock/job/execute_daily_job.py"

        if task_type == "full":
            cmd = [python_path, script_path]
        elif task_type == "range" and start_date and end_date:
            cmd = [python_path, script_path, start_date, end_date]
        elif task_type == "single" and start_date:
            cmd = [python_path, script_path, start_date]
        elif task_type == "multiple" and dates:
            cmd = [python_path, script_path, dates]
        else:
            self._send_error("参数错误")
            return
        
        # 创建任务
        task_id = f"task_{int(time.time())}"
        task_info = {
            'id': task_id,
            'type': task_type,
            'cmd': ' '.join(cmd),
            'status': 'running',
            'start_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'progress': 0,
            'logs': []
        }
        
        task_manager['current_task'] = task_info
        task_manager['task_history'].append(task_info)
        
        # 启动后台任务
        thread = threading.Thread(target=self._execute_task, args=(cmd, task_info))
        thread.daemon = True
        thread.start()
        
        self._send_success({"task_id": task_id, "message": "任务已启动"})
    
    def _execute_task(self, cmd, task_info):
        """执行数据回补任务"""
        try:
            # 直接在容器内执行命令，设置正确的环境变量
            import os
            env = os.environ.copy()  # 继承所有环境变量
            env.update({
                'PYTHONPATH': '/data/InStock',
                'PATH': '/usr/local/bin:/usr/bin:/bin',
                # 确保数据库环境变量被传递
                'db_host': os.environ.get('db_host', 'InStockDbService'),
                'db_user': os.environ.get('db_user', 'root'),
                'db_password': os.environ.get('db_password', 'root'),
                'db_database': os.environ.get('db_database', 'instockdb'),
                'db_port': os.environ.get('db_port', '3306'),
            })

            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1,
                env=env,
                cwd='/data/InStock/instock/job'
            )
            
            task_info['process'] = process
            
            # 读取输出
            for line in iter(process.stdout.readline, ''):
                if line:
                    task_info['logs'].append({
                        'time': time.strftime('%H:%M:%S'),
                        'message': line.strip()
                    })
                    # 保持最近100条日志
                    if len(task_info['logs']) > 100:
                        task_info['logs'] = task_info['logs'][-100:]
            
            # 等待进程完成
            return_code = process.wait()
            
            if return_code == 0:
                task_info['status'] = 'completed'
                task_info['progress'] = 100
            else:
                task_info['status'] = 'failed'
                task_info['logs'].append({
                    'time': time.strftime('%H:%M:%S'),
                    'message': f'任务失败，退出码: {return_code}'
                })
            
        except Exception as e:
            task_info['status'] = 'failed'
            task_info['logs'].append({
                'time': time.strftime('%H:%M:%S'),
                'message': f'执行异常: {str(e)}'
            })
            logging.error(f"执行任务异常：{e}")
        
        finally:
            task_info['end_time'] = time.strftime('%Y-%m-%d %H:%M:%S')
            if task_manager['current_task'] and task_manager['current_task']['id'] == task_info['id']:
                task_manager['current_task'] = None
    
    def _get_task_status(self):
        """获取任务状态"""
        current = task_manager['current_task']
        history = task_manager['task_history'][-10:]  # 最近10个任务

        # 清理不能序列化的对象
        current_clean = None
        if current:
            current_clean = {k: v for k, v in current.items() if k != 'process'}

        history_clean = []
        for task in history:
            task_clean = {k: v for k, v in task.items() if k != 'process'}
            history_clean.append(task_clean)

        self._send_success({
            'current_task': current_clean,
            'task_history': history_clean
        })
    
    def _get_task_logs(self):
        """获取任务日志"""
        task_id = self.get_argument("task_id", default="")
        
        if task_manager['current_task'] and task_manager['current_task']['id'] == task_id:
            logs = task_manager['current_task']['logs']
        else:
            # 从历史任务中查找
            logs = []
            for task in task_manager['task_history']:
                if task['id'] == task_id:
                    logs = task.get('logs', [])
                    break
        
        self._send_success({'logs': logs})
    
    def _stop_current_task(self):
        """停止当前任务"""
        if not task_manager['current_task']:
            self._send_error("没有正在运行的任务")
            return
        
        task = task_manager['current_task']
        if 'process' in task:
            try:
                task['process'].terminate()
                task['status'] = 'stopped'
                task['end_time'] = time.strftime('%Y-%m-%d %H:%M:%S')
                task_manager['current_task'] = None
                self._send_success({"message": "任务已停止"})
            except Exception as e:
                self._send_error(f"停止任务失败: {str(e)}")
        else:
            self._send_error("无法停止任务")
    
    def _send_success(self, data):
        """发送成功响应"""
        self.set_header('Content-Type', 'application/json;charset=UTF-8')
        self.write(json.dumps({
            'success': True,
            'data': data
        }, ensure_ascii=False))
    
    def _send_error(self, message):
        """发送错误响应"""
        self.set_header('Content-Type', 'application/json;charset=UTF-8')
        self.write(json.dumps({
            'success': False,
            'message': message
        }, ensure_ascii=False))
