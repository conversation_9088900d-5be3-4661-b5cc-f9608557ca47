{% extends "layout/default.html" %}

{% block main_content %}
<div class="page-content">
    <h3 class="header smaller lighter">数据回补管理</h3>
    
    <!-- 任务控制面板 -->
    <div class="row">
        <div class="col-xs-12">
            <div class="widget-box">
                <div class="widget-header">
                    <h4 class="widget-title">
                        <i class="ace-icon fa fa-cogs"></i>
                        数据回补任务
                    </h4>
                </div>
                <div class="widget-body">
                    <div class="widget-main">
                        <form id="backfillForm" class="form-horizontal">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">回补类型</label>
                                <div class="col-sm-4">
                                    <select id="taskType" class="form-control">
                                        <option value="full">完整回补(当前时间)</option>
                                        <option value="range">日期区间回补</option>
                                        <option value="single">单日回补</option>
                                        <option value="multiple">多日回补</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-group" id="dateRangeGroup" style="display:none;">
                                <label class="col-sm-2 control-label">日期区间</label>
                                <div class="col-sm-3">
                                    <input type="date" id="startDate" class="form-control" placeholder="开始日期">
                                </div>
                                <div class="col-sm-1 text-center">至</div>
                                <div class="col-sm-3">
                                    <input type="date" id="endDate" class="form-control" placeholder="结束日期">
                                </div>
                            </div>
                            
                            <div class="form-group" id="singleDateGroup" style="display:none;">
                                <label class="col-sm-2 control-label">选择日期</label>
                                <div class="col-sm-4">
                                    <input type="date" id="singleDate" class="form-control">
                                </div>
                            </div>
                            
                            <div class="form-group" id="multipleDatesGroup" style="display:none;">
                                <label class="col-sm-2 control-label">多个日期</label>
                                <div class="col-sm-6">
                                    <input type="text" id="multipleDates" class="form-control" 
                                           placeholder="格式: 2024-08-01,2024-08-05,2024-08-19">
                                    <span class="help-block">多个日期用逗号分隔</span>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <div class="col-sm-offset-2 col-sm-6">
                                    <button type="button" id="startBtn" class="btn btn-primary">
                                        <i class="ace-icon fa fa-play"></i> 开始回补
                                    </button>
                                    <button type="button" id="stopBtn" class="btn btn-danger" disabled>
                                        <i class="ace-icon fa fa-stop"></i> 停止任务
                                    </button>
                                    <button type="button" id="refreshBtn" class="btn btn-info">
                                        <i class="ace-icon fa fa-refresh"></i> 刷新状态
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 任务状态面板 -->
    <div class="row">
        <div class="col-xs-6">
            <div class="widget-box">
                <div class="widget-header">
                    <h4 class="widget-title">
                        <i class="ace-icon fa fa-tasks"></i>
                        当前任务状态
                    </h4>
                </div>
                <div class="widget-body">
                    <div class="widget-main">
                        <div id="currentTaskStatus">
                            <p class="text-muted">暂无运行中的任务</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xs-6">
            <div class="widget-box">
                <div class="widget-header">
                    <h4 class="widget-title">
                        <i class="ace-icon fa fa-history"></i>
                        任务历史
                    </h4>
                </div>
                <div class="widget-body">
                    <div class="widget-main">
                        <div id="taskHistory" style="max-height: 300px; overflow-y: auto;">
                            <p class="text-muted">暂无历史任务</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 任务日志面板 -->
    <div class="row">
        <div class="col-xs-12">
            <div class="widget-box">
                <div class="widget-header">
                    <h4 class="widget-title">
                        <i class="ace-icon fa fa-file-text-o"></i>
                        任务日志
                    </h4>
                </div>
                <div class="widget-body">
                    <div class="widget-main">
                        <div id="taskLogs" style="height: 300px; overflow-y: auto; background: #f5f5f5; padding: 10px; font-family: monospace; font-size: 12px;">
                            <p class="text-muted">暂无日志信息</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    var currentTaskId = null;
    var refreshInterval = null;

    console.log('页面加载完成，JavaScript初始化');

    // 任务类型切换
    $('#taskType').change(function() {
        var type = $(this).val();
        console.log('任务类型切换:', type);
        $('.form-group[id$="Group"]').hide();

        if (type === 'range') {
            $('#dateRangeGroup').show();
        } else if (type === 'single') {
            $('#singleDateGroup').show();
        } else if (type === 'multiple') {
            $('#multipleDatesGroup').show();
        }
    });
    
    // 开始任务
    $('#startBtn').click(function() {
        console.log('开始回补按钮被点击');

        var taskType = $('#taskType').val();
        console.log('任务类型:', taskType);

        var data = {
            action: 'start_backfill',
            task_type: taskType
        };

        if (taskType === 'range') {
            data.start_date = $('#startDate').val();
            data.end_date = $('#endDate').val();
            if (!data.start_date || !data.end_date) {
                alert('请选择开始和结束日期');
                return;
            }
        } else if (taskType === 'single') {
            data.start_date = $('#singleDate').val();
            if (!data.start_date) {
                alert('请选择日期');
                return;
            }
        } else if (taskType === 'multiple') {
            data.dates = $('#multipleDates').val();
            if (!data.dates) {
                alert('请输入日期');
                return;
            }
        }

        console.log('发送数据:', data);

        $.ajax({
            url: '/instock/admin/backfill/api',
            type: 'POST',
            data: data,
            dataType: 'json',
            success: function(response) {
                console.log('响应:', response);
                if (response.success) {
                    currentTaskId = response.data.task_id;
                    $('#startBtn').prop('disabled', true);
                    $('#stopBtn').prop('disabled', false);
                    startRefresh();
                    alert('任务已启动: ' + response.data.task_id);
                } else {
                    alert('启动失败: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                console.log('AJAX错误:', xhr, status, error);
                alert('请求失败: ' + error);
            }
        });
    });
    
    // 停止任务
    $('#stopBtn').click(function() {
        $.post('/instock/admin/backfill/api', {action: 'stop_task'}, function(response) {
            if (response.success) {
                $('#startBtn').prop('disabled', false);
                $('#stopBtn').prop('disabled', true);
                stopRefresh();
                alert('任务已停止');
            } else {
                alert('停止失败: ' + response.message);
            }
        });
    });
    
    // 刷新状态
    $('#refreshBtn').click(function() {
        refreshStatus();
    });
    
    // 刷新状态
    function refreshStatus() {
        $.post('/instock/admin/backfill/api', {action: 'get_status'}, function(response) {
            if (response.success) {
                updateTaskStatus(response.data);
                if (currentTaskId) {
                    refreshLogs();
                }
            }
        });
    }
    
    // 刷新日志
    function refreshLogs() {
        if (!currentTaskId) return;
        
        $.post('/instock/admin/backfill/api', {
            action: 'get_logs',
            task_id: currentTaskId
        }, function(response) {
            if (response.success) {
                updateLogs(response.data.logs);
            }
        });
    }
    
    // 更新任务状态显示
    function updateTaskStatus(data) {
        const current = data.current_task;
        const history = data.task_history;
        
        // 更新当前任务
        if (current) {
            const statusClass = current.status === 'running' ? 'text-info' : 
                               current.status === 'completed' ? 'text-success' : 'text-danger';
            $('#currentTaskStatus').html(`
                <p><strong>任务ID:</strong> ${current.id}</p>
                <p><strong>类型:</strong> ${current.type}</p>
                <p><strong>状态:</strong> <span class="${statusClass}">${current.status}</span></p>
                <p><strong>开始时间:</strong> ${current.start_time}</p>
                <p><strong>命令:</strong> <code>${current.cmd}</code></p>
            `);
            
            if (current.status === 'running') {
                $('#startBtn').prop('disabled', true);
                $('#stopBtn').prop('disabled', false);
                currentTaskId = current.id;
                if (!refreshInterval) startRefresh();
            } else {
                $('#startBtn').prop('disabled', false);
                $('#stopBtn').prop('disabled', true);
                stopRefresh();
            }
        } else {
            $('#currentTaskStatus').html('<p class="text-muted">暂无运行中的任务</p>');
            $('#startBtn').prop('disabled', false);
            $('#stopBtn').prop('disabled', true);
            stopRefresh();
        }
        
        // 更新历史任务
        if (history && history.length > 0) {
            let historyHtml = '';
            history.forEach(function(task) {
                const statusClass = task.status === 'completed' ? 'text-success' : 
                                   task.status === 'failed' ? 'text-danger' : 'text-warning';
                historyHtml += `
                    <div class="task-item" style="border-bottom: 1px solid #eee; padding: 5px 0;">
                        <small><strong>${task.id}</strong> - ${task.type}</small><br>
                        <small class="${statusClass}">${task.status}</small>
                        <small class="pull-right">${task.start_time}</small>
                    </div>
                `;
            });
            $('#taskHistory').html(historyHtml);
        }
    }
    
    // 更新日志显示
    function updateLogs(logs) {
        if (logs && logs.length > 0) {
            let logsHtml = '';
            logs.forEach(function(log) {
                logsHtml += `<div>[${log.time}] ${log.message}</div>`;
            });
            $('#taskLogs').html(logsHtml);
            $('#taskLogs').scrollTop($('#taskLogs')[0].scrollHeight);
        }
    }
    
    // 开始自动刷新
    function startRefresh() {
        if (refreshInterval) return;
        refreshInterval = setInterval(function() {
            refreshStatus();
        }, 3000); // 每3秒刷新一次
    }
    
    // 停止自动刷新
    function stopRefresh() {
        if (refreshInterval) {
            clearInterval(refreshInterval);
            refreshInterval = null;
        }
    }
    
    // 页面加载时刷新一次状态
    refreshStatus();
});
</script>
{% end %}
