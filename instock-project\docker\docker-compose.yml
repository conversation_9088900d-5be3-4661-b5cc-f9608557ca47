version: '3.8'

services:
  instockdbservice:
    image: mysql:8.0
    container_name: InStockDbService
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: instockdb
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - instock_network
    ports:
      - "3306:3306"

  instock:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: InStock
    ports:
      - "9988:9988"
    environment:
      db_host: InStockDbService
      db_user: root
      db_password: root
      db_database: instockdb
      db_port: 3306
    depends_on:
      - instockdbservice
    networks:
      - instock_network

volumes:
  mysql_data:

networks:
  instock_network:
    driver: bridge

