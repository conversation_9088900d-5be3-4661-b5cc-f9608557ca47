version: '3.8'

services:
  instock:
    build:
      context: .
      dockerfile: Dockerfile.local
    container_name: InStock
    ports:
      - "9988:9988"
    environment:
      db_type: postgresql
      db_host: host.docker.internal
      db_user: seat
      db_password: seat
      db_database: instockdb
      db_port: 5432
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - instock_network

networks:
  instock_network:
    driver: bridge
