#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据更新配置模块
用于管理各个数据采集模块的配置信息
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional
import instock.lib.database as mdb

__author__ = 'InStock Team'
__date__ = '2025/8/19'

# 数据模块配置定义
DATA_MODULES = {
    'basic_stock_data': {
        'name': '股票基础数据',
        'description': '股票实时行情、价格、成交量等基础数据',
        'table': 'cn_stock_spot',
        'job_module': 'basic_data_daily_job',
        'job_function': 'save_nph_stock_spot_data',
        'priority': 1,
        'dependencies': [],
        'estimated_time': 300,  # 预估执行时间（秒）
        'data_size': 'large',   # 数据量大小：small/medium/large
        'update_frequency': 'daily',  # 更新频率：daily/weekly/monthly
        'category': 'basic'
    },
    'etf_data': {
        'name': 'ETF基金数据',
        'description': 'ETF基金实时行情数据',
        'table': 'cn_etf_spot',
        'job_module': 'basic_data_daily_job',
        'job_function': 'save_nph_etf_spot_data',
        'priority': 2,
        'dependencies': [],
        'estimated_time': 120,
        'data_size': 'medium',
        'update_frequency': 'daily',
        'category': 'basic'
    },
    'stock_top_data': {
        'name': '龙虎榜数据',
        'description': '股票龙虎榜、上榜统计数据',
        'table': 'cn_stock_top',
        'job_module': 'basic_data_other_daily_job',
        'job_function': 'save_nph_stock_top_data',
        'priority': 3,
        'dependencies': ['basic_stock_data'],
        'estimated_time': 180,
        'data_size': 'small',
        'update_frequency': 'daily',
        'category': 'market'
    },
    'fund_flow_data': {
        'name': '资金流向数据',
        'description': '主力资金、大单、中单、小单流向数据',
        'table': 'cn_stock_fund_flow',
        'job_module': 'basic_data_other_daily_job',
        'job_function': 'save_nph_stock_fund_flow_data',
        'priority': 4,
        'dependencies': ['basic_stock_data'],
        'estimated_time': 600,
        'data_size': 'large',
        'update_frequency': 'daily',
        'category': 'market'
    },
    'sector_fund_flow': {
        'name': '行业资金流向',
        'description': '行业和概念板块资金流向数据',
        'table': 'cn_stock_fund_flow_industry',
        'job_module': 'basic_data_other_daily_job',
        'job_function': 'save_nph_stock_sector_fund_flow_data',
        'priority': 5,
        'dependencies': ['fund_flow_data'],
        'estimated_time': 240,
        'data_size': 'medium',
        'update_frequency': 'daily',
        'category': 'market'
    },
    'bonus_data': {
        'name': '分红送股数据',
        'description': '股票分红、送股、配股等数据',
        'table': 'cn_stock_bonus',
        'job_module': 'basic_data_other_daily_job',
        'job_function': 'save_nph_stock_bonus',
        'priority': 6,
        'dependencies': [],
        'estimated_time': 60,
        'data_size': 'small',
        'update_frequency': 'weekly',
        'category': 'fundamental'
    },
    'technical_indicators': {
        'name': '技术指标数据',
        'description': 'MACD、KDJ、RSI等70+个技术指标',
        'table': 'cn_stock_indicators',
        'job_module': 'indicators_data_daily_job',
        'job_function': 'prepare',
        'priority': 7,
        'dependencies': ['basic_stock_data'],
        'estimated_time': 900,
        'data_size': 'large',
        'update_frequency': 'daily',
        'category': 'technical'
    },
    'buy_sell_signals': {
        'name': '买卖信号数据',
        'description': '基于技术指标的买入卖出信号',
        'table': 'cn_stock_indicators_buy',
        'job_module': 'indicators_data_daily_job',
        'job_function': 'guess_buy',
        'priority': 8,
        'dependencies': ['technical_indicators'],
        'estimated_time': 300,
        'data_size': 'medium',
        'update_frequency': 'daily',
        'category': 'technical'
    },
    'kline_patterns': {
        'name': 'K线形态数据',
        'description': '60+种K线形态识别结果',
        'table': 'cn_stock_pattern',
        'job_module': 'klinepattern_data_daily_job',
        'job_function': 'main',
        'priority': 9,
        'dependencies': ['basic_stock_data'],
        'estimated_time': 450,
        'data_size': 'medium',
        'update_frequency': 'daily',
        'category': 'technical'
    },
    'strategy_enter': {
        'name': '放量上涨策略',
        'description': '放量上涨交易策略信号',
        'table': 'cn_stock_strategy_enter',
        'job_module': 'strategy_data_daily_job',
        'job_function': 'prepare',
        'priority': 10,
        'dependencies': ['technical_indicators'],
        'estimated_time': 180,
        'data_size': 'small',
        'update_frequency': 'daily',
        'category': 'strategy'
    },
    'strategy_ma_trend': {
        'name': '均线多头策略',
        'description': '均线多头排列交易策略',
        'table': 'cn_stock_strategy_keep_increasing',
        'job_module': 'strategy_data_daily_job',
        'job_function': 'prepare',
        'priority': 11,
        'dependencies': ['technical_indicators'],
        'estimated_time': 180,
        'data_size': 'small',
        'update_frequency': 'daily',
        'category': 'strategy'
    },
    'strategy_parking_apron': {
        'name': '停机坪策略',
        'description': '停机坪形态交易策略',
        'table': 'cn_stock_strategy_parking_apron',
        'job_module': 'strategy_data_daily_job',
        'job_function': 'prepare',
        'priority': 12,
        'dependencies': ['technical_indicators'],
        'estimated_time': 180,
        'data_size': 'small',
        'update_frequency': 'daily',
        'category': 'strategy'
    },
    'strategy_ma250': {
        'name': '回踩年线策略',
        'description': '回踩250日均线交易策略',
        'table': 'cn_stock_strategy_backtrace_ma250',
        'job_module': 'strategy_data_daily_job',
        'job_function': 'prepare',
        'priority': 13,
        'dependencies': ['technical_indicators'],
        'estimated_time': 180,
        'data_size': 'small',
        'update_frequency': 'daily',
        'category': 'strategy'
    },
    'strategy_breakthrough': {
        'name': '突破平台策略',
        'description': '突破平台整理交易策略',
        'table': 'cn_stock_strategy_breakthrough_platform',
        'job_module': 'strategy_data_daily_job',
        'job_function': 'prepare',
        'priority': 14,
        'dependencies': ['technical_indicators'],
        'estimated_time': 180,
        'data_size': 'small',
        'update_frequency': 'daily',
        'category': 'strategy'
    },
    'strategy_turtle': {
        'name': '海龟交易策略',
        'description': '海龟交易法则策略',
        'table': 'cn_stock_strategy_turtle_trade',
        'job_module': 'strategy_data_daily_job',
        'job_function': 'prepare',
        'priority': 15,
        'dependencies': ['technical_indicators'],
        'estimated_time': 180,
        'data_size': 'small',
        'update_frequency': 'daily',
        'category': 'strategy'
    },
    'backtest_data': {
        'name': '策略回测数据',
        'description': '各策略历史回测收益率数据',
        'table': 'cn_stock_backtest_data',
        'job_module': 'backtest_data_daily_job',
        'job_function': 'main',
        'priority': 16,
        'dependencies': ['buy_sell_signals', 'strategy_enter'],
        'estimated_time': 1200,
        'data_size': 'large',
        'update_frequency': 'daily',
        'category': 'analysis'
    },
    'selection_data': {
        'name': '综合选股数据',
        'description': '综合多种因子的选股结果',
        'table': 'cn_stock_selection',
        'job_module': 'selection_data_daily_job',
        'job_function': 'main',
        'priority': 17,
        'dependencies': ['technical_indicators', 'fund_flow_data'],
        'estimated_time': 300,
        'data_size': 'medium',
        'update_frequency': 'daily',
        'category': 'analysis'
    }
}

# 数据类别定义
DATA_CATEGORIES = {
    'basic': {'name': '基础数据', 'color': '#007bff', 'icon': 'fa-database'},
    'market': {'name': '市场数据', 'color': '#28a745', 'icon': 'fa-chart-line'},
    'fundamental': {'name': '基本面数据', 'color': '#ffc107', 'icon': 'fa-building'},
    'technical': {'name': '技术分析', 'color': '#dc3545', 'icon': 'fa-chart-bar'},
    'strategy': {'name': '交易策略', 'color': '#6f42c1', 'icon': 'fa-chess'},
    'analysis': {'name': '分析结果', 'color': '#fd7e14', 'icon': 'fa-brain'}
}


class DataUpdateConfigManager:
    """数据更新配置管理器"""
    
    def __init__(self):
        self.config_table = 'data_update_config'
        self._ensure_config_table()
    
    def _ensure_config_table(self):
        """确保配置表存在"""
        try:
            create_sql = f"""
            CREATE TABLE IF NOT EXISTS `{self.config_table}` (
                `module_id` VARCHAR(50) NOT NULL PRIMARY KEY,
                `enabled` BOOLEAN NOT NULL DEFAULT TRUE,
                `priority` INT NOT NULL DEFAULT 1,
                `update_frequency` VARCHAR(20) NOT NULL DEFAULT 'daily',
                `last_update` DATETIME NULL,
                `next_update` DATETIME NULL,
                `config_data` JSON NULL,
                `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
            """
            mdb.executeSql(create_sql)
            
            # 初始化默认配置
            self._init_default_config()
            
        except Exception as e:
            logging.error(f"创建配置表失败: {e}")
    
    def _init_default_config(self):
        """初始化默认配置"""
        try:
            for module_id, module_info in DATA_MODULES.items():
                check_sql = f"SELECT COUNT(*) as count FROM `{self.config_table}` WHERE `module_id` = '{module_id}'"
                result = mdb.executeSql(check_sql)
                
                if not result or result[0]['count'] == 0:
                    insert_sql = f"""
                    INSERT INTO `{self.config_table}` 
                    (`module_id`, `enabled`, `priority`, `update_frequency`) 
                    VALUES ('{module_id}', TRUE, {module_info['priority']}, '{module_info['update_frequency']}')
                    """
                    mdb.executeSql(insert_sql)
                    
        except Exception as e:
            logging.error(f"初始化默认配置失败: {e}")
    
    def get_all_configs(self) -> List[Dict]:
        """获取所有模块配置"""
        try:
            sql = f"SELECT * FROM `{self.config_table}` ORDER BY `priority`"
            configs = mdb.executeSql(sql)
            
            # 合并模块定义信息
            result = []
            for config in configs:
                module_id = config['module_id']
                if module_id in DATA_MODULES:
                    module_info = DATA_MODULES[module_id].copy()
                    module_info.update(config)
                    result.append(module_info)
            
            return result
            
        except Exception as e:
            logging.error(f"获取配置失败: {e}")
            return []
    
    def get_enabled_modules(self) -> List[Dict]:
        """获取启用的模块配置"""
        try:
            sql = f"SELECT * FROM `{self.config_table}` WHERE `enabled` = TRUE ORDER BY `priority`"
            configs = mdb.executeSql(sql)
            
            result = []
            for config in configs:
                module_id = config['module_id']
                if module_id in DATA_MODULES:
                    module_info = DATA_MODULES[module_id].copy()
                    module_info.update(config)
                    result.append(module_info)
            
            return result
            
        except Exception as e:
            logging.error(f"获取启用模块失败: {e}")
            return []
    
    def update_module_config(self, module_id: str, enabled: bool = None, 
                           priority: int = None, update_frequency: str = None) -> bool:
        """更新模块配置"""
        try:
            updates = []
            params = []
            
            if enabled is not None:
                updates.append("`enabled` = %s")
                params.append(enabled)
            
            if priority is not None:
                updates.append("`priority` = %s")
                params.append(priority)
            
            if update_frequency is not None:
                updates.append("`update_frequency` = %s")
                params.append(update_frequency)
            
            if not updates:
                return True
            
            sql = f"UPDATE `{self.config_table}` SET {', '.join(updates)} WHERE `module_id` = %s"
            params.append(module_id)
            
            mdb.executeSql(sql, params)
            return True
            
        except Exception as e:
            logging.error(f"更新模块配置失败: {e}")
            return False
    
    def get_modules_by_category(self) -> Dict[str, List[Dict]]:
        """按类别获取模块配置"""
        configs = self.get_all_configs()
        result = {}
        
        for config in configs:
            category = config.get('category', 'other')
            if category not in result:
                result[category] = []
            result[category].append(config)
        
        return result
    
    def get_execution_plan(self) -> List[Dict]:
        """获取执行计划（考虑依赖关系）"""
        enabled_modules = self.get_enabled_modules()
        
        # 构建依赖图
        dependency_graph = {}
        for module in enabled_modules:
            module_id = module['module_id']
            dependencies = module.get('dependencies', [])
            dependency_graph[module_id] = dependencies
        
        # 拓扑排序
        execution_order = self._topological_sort(dependency_graph)
        
        # 构建执行计划
        execution_plan = []
        for module_id in execution_order:
            for module in enabled_modules:
                if module['module_id'] == module_id:
                    execution_plan.append(module)
                    break
        
        return execution_plan
    
    def _topological_sort(self, graph: Dict[str, List[str]]) -> List[str]:
        """拓扑排序算法"""
        # 计算入度
        in_degree = {node: 0 for node in graph}
        for node in graph:
            for neighbor in graph[node]:
                if neighbor in in_degree:
                    in_degree[neighbor] += 1
        
        # 找到入度为0的节点
        queue = [node for node in in_degree if in_degree[node] == 0]
        result = []
        
        while queue:
            node = queue.pop(0)
            result.append(node)
            
            # 更新邻居节点的入度
            for neighbor in graph.get(node, []):
                if neighbor in in_degree:
                    in_degree[neighbor] -= 1
                    if in_degree[neighbor] == 0:
                        queue.append(neighbor)
        
        return result


# 全局配置管理器实例
config_manager = DataUpdateConfigManager()
