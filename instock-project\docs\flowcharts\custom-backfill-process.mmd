graph TD
    A[用户访问Web界面] --> B[数据回补管理页面]
    B --> C[选择回补类型]
    
    C --> C1[最近一周数据]
    C --> C2[最近一个月数据]
    C --> C3[指定日期范围]
    C --> C4[单个日期]
    
    C1 --> D[前端发送AJAX请求]
    C2 --> D
    C3 --> D
    C4 --> D
    
    D --> E[DataBackfillApiHandler.post]
    E --> F{action参数判断}
    
    F --> F1[start_backfill]
    F --> F2[get_status]
    F --> F3[get_logs]
    F --> F4[stop_task]
    
    F1 --> G[_start_backfill_task方法]
    G --> G1[获取回补参数]
    G1 --> G2[创建任务信息]
    G2 --> G3[启动后台线程]
    
    G3 --> H[_execute_backfill_task方法]
    H --> H1[构建Python执行命令]
    H1 --> H2[subprocess.Popen执行]
    H2 --> H3[实时读取输出日志]
    H3 --> H4[更新任务状态]
    
    H2 --> I{执行的是什么脚本?}
    I --> I1[basic_data_daily_job.py]
    I --> I2[execute_daily_job.py]
    I --> I3[indicators_data_daily_job.py]
    
    F2 --> J[返回当前任务状态]
    F3 --> K[返回任务执行日志]
    F4 --> L[停止当前任务]
    
    H4 --> M[任务完成/失败]
    M --> N[更新任务历史]
    
    style A fill:#fff3e0
    style B fill:#fff3e0
    style H fill:#fff3e0
    style M fill:#e8f5e8
    style I1 fill:#f3e5f5
    style I2 fill:#ffebee
    style I3 fill:#ffebee
