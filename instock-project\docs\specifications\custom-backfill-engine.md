# 个性化回补引擎技术规范

## 📋 概述

个性化回补引擎 (CustomBackfillEngine) 是基于官方回补代码构建的智能化数据回补系统，支持用户按需选择回补模块，自动处理依赖关系，并提供优化的执行策略。

## 🏗️ 架构设计

### 核心组件

```
CustomBackfillEngine
├── ModuleDependency          # 依赖关系管理
│   ├── DEPENDENCIES         # 模块依赖关系定义
│   ├── MODULE_INFO          # 模块信息配置
│   └── CONCURRENT_GROUPS    # 并发执行组配置
├── BackfillModule           # 回补模块枚举
└── CustomBackfillEngine     # 执行引擎主类
    ├── analyze_selection()  # 分析用户选择
    ├── execute_backfill()   # 执行回补任务
    └── get_module_categories() # 获取模块分类
```

## 📊 模块定义

### BackfillModule 枚举

| 模块 | 值 | 描述 | 依赖 |
|------|----|----- |------|
| INIT | init_job | 数据库初始化 | 无 |
| BASIC_DATA | basic_data_daily_job | 基础股票数据 | INIT |
| SELECTION_DATA | selection_data_daily_job | 综合选股数据 | INIT, BASIC_DATA |
| OTHER_DATA | basic_data_other_daily_job | 其他基础数据 | INIT, BASIC_DATA |
| INDICATORS | indicators_data_daily_job | 技术指标数据 | INIT, BASIC_DATA |
| KLINE_PATTERN | klinepattern_data_daily_job | K线形态数据 | INIT, BASIC_DATA |
| STRATEGY | strategy_data_daily_job | 策略数据 | INIT, BASIC_DATA, INDICATORS |
| BACKTEST | backtest_data_daily_job | 回测数据 | INIT, BASIC_DATA, STRATEGY |
| AFTER_CLOSE | basic_data_after_close_daily_job | 闭盘后数据 | INIT, BASIC_DATA |

### 模块分类

- **基础设施**: INIT
- **基础数据**: BASIC_DATA, OTHER_DATA, AFTER_CLOSE
- **选股数据**: SELECTION_DATA
- **技术分析**: INDICATORS, KLINE_PATTERN
- **交易策略**: STRATEGY
- **分析结果**: BACKTEST

## 🔄 执行流程

### 1. 用户选择分析
```python
def analyze_selection(selected_modules: List[str]) -> Dict:
    """
    分析用户选择，返回执行计划
    
    Args:
        selected_modules: 用户选择的模块列表
        
    Returns:
        {
            "selected_modules": [...],      # 用户选择的模块
            "all_modules": [...],           # 包含依赖的所有模块
            "auto_added_dependencies": [...], # 自动添加的依赖
            "execution_plan": [...],        # 执行计划
            "total_modules": int,           # 总模块数
            "estimated_time": int,          # 预估时间(秒)
            "module_info": {...}            # 模块详细信息
        }
    """
```

### 2. 依赖关系解析
```python
def get_dependencies(modules: Set[BackfillModule]) -> Set[BackfillModule]:
    """
    递归获取模块的所有依赖
    
    示例：
    输入: {STRATEGY}
    输出: {INIT, BASIC_DATA, INDICATORS, STRATEGY}
    """
```

### 3. 执行计划生成
```python
def get_execution_plan(selected_modules: Set[BackfillModule]) -> List[List[BackfillModule]]:
    """
    生成分层执行计划，支持并发优化
    
    示例输出:
    [
        [INIT],                                    # 阶段1: 串行
        [BASIC_DATA],                             # 阶段2: 串行
        [SELECTION_DATA],                         # 阶段3: 串行
        [INDICATORS, KLINE_PATTERN, STRATEGY],    # 阶段4: 并发
        [BACKTEST]                                # 阶段5: 串行
    ]
    """
```

### 4. 任务执行
```python
def execute_backfill(selected_modules: List[str], start_date=None, end_date=None, 
                    progress_callback=None) -> bool:
    """
    执行个性化回补
    
    Args:
        selected_modules: 选择的模块列表
        start_date: 开始日期 (可选)
        end_date: 结束日期 (可选)
        progress_callback: 进度回调函数 (可选)
        
    Returns:
        bool: 执行是否成功
    """
```

## 🔧 配置说明

### 依赖关系配置
```python
DEPENDENCIES = {
    BackfillModule.INIT: set(),  # 无依赖
    BackfillModule.BASIC_DATA: {BackfillModule.INIT},
    BackfillModule.SELECTION_DATA: {BackfillModule.INIT, BackfillModule.BASIC_DATA},
    # ... 其他模块依赖
}
```

### 模块信息配置
```python
MODULE_INFO = {
    BackfillModule.BASIC_DATA: {
        "name": "股票基础数据",
        "description": "股票实时行情、ETF数据",
        "estimated_time": 300,  # 预估执行时间(秒)
        "category": "基础数据",
        "required": True        # 是否为必需模块
    },
    # ... 其他模块信息
}
```

### 并发执行组配置
```python
CONCURRENT_GROUPS = [
    {BackfillModule.OTHER_DATA, BackfillModule.INDICATORS, 
     BackfillModule.KLINE_PATTERN, BackfillModule.STRATEGY}
]
```

## 🌐 Web接口集成

### API端点

#### 1. 获取模块分类
```
POST /instock/data_backfill_api
Content-Type: application/x-www-form-urlencoded

action=get_module_categories
```

#### 2. 分析模块选择
```
POST /instock/data_backfill_api
Content-Type: application/x-www-form-urlencoded

action=analyze_selection&selected_modules=["basic_data_daily_job","indicators_data_daily_job"]
```

#### 3. 启动回补任务
```
POST /instock/data_backfill_api
Content-Type: application/x-www-form-urlencoded

action=start_backfill&selected_modules=["basic_data_daily_job"]&use_custom_engine=true&start_date=2024-08-01&end_date=2024-08-19
```

### 响应格式

#### 成功响应
```json
{
    "success": true,
    "data": {
        // 具体数据内容
    }
}
```

#### 错误响应
```json
{
    "success": false,
    "error": "错误信息"
}
```

## 📈 性能优化

### 1. 并发执行优化
- 自动识别可并发执行的模块组
- 使用ThreadPoolExecutor实现并发
- 避免资源竞争和死锁

### 2. 内存管理
- 及时释放不需要的数据
- 使用生成器减少内存占用
- 监控内存使用情况

### 3. 错误处理
- 模块级别的错误隔离
- 支持部分失败后的恢复
- 详细的错误日志记录

## 🧪 测试策略

### 1. 单元测试
```python
def test_dependency_resolution():
    """测试依赖关系解析"""
    selected = {BackfillModule.STRATEGY}
    result = ModuleDependency.get_dependencies(selected)
    expected = {BackfillModule.INIT, BackfillModule.BASIC_DATA, 
                BackfillModule.INDICATORS, BackfillModule.STRATEGY}
    assert result == expected
```

### 2. 集成测试
```python
def test_execution_plan_generation():
    """测试执行计划生成"""
    selected = {BackfillModule.INDICATORS, BackfillModule.STRATEGY}
    plan = ModuleDependency.get_execution_plan(selected)
    # 验证执行顺序和并发组
```

### 3. 端到端测试
```python
def test_full_backfill_execution():
    """测试完整回补流程"""
    engine = CustomBackfillEngine()
    result = engine.execute_backfill(["basic_data_daily_job"])
    assert result == True
```

## 🔄 版本兼容性

### 官方代码兼容
- 基于反射机制动态加载官方模块
- 最小化对官方代码的修改
- 支持官方代码更新后的自动适配

### 向后兼容
- 保持现有API接口不变
- 支持传统全量回补模式
- 渐进式功能升级

## 📝 使用示例

### Python代码示例
```python
from instock.core.custom_backfill_engine import custom_backfill_engine

# 分析用户选择
analysis = custom_backfill_engine.analyze_selection(["strategy_data_daily_job"])
print(f"需要执行 {analysis['total_modules']} 个模块")
print(f"预估时间: {analysis['estimated_time']} 秒")

# 执行回补
def progress_callback(percent, message):
    print(f"进度: {percent}% - {message}")

success = custom_backfill_engine.execute_backfill(
    selected_modules=["strategy_data_daily_job"],
    start_date="2024-08-01",
    end_date="2024-08-19",
    progress_callback=progress_callback
)

print(f"回补{'成功' if success else '失败'}")
```

### JavaScript前端示例
```javascript
// 获取模块分类
fetch('/instock/data_backfill_api', {
    method: 'POST',
    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
    body: 'action=get_module_categories'
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('模块分类:', data.data);
    }
});

// 分析选择
const selectedModules = ['basic_data_daily_job', 'indicators_data_daily_job'];
fetch('/instock/data_backfill_api', {
    method: 'POST',
    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
    body: `action=analyze_selection&selected_modules=${JSON.stringify(selectedModules)}`
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('执行计划:', data.data);
    }
});
```

## 🚀 未来扩展

### 1. 模板化回补
- 预设常用回补组合
- 用户自定义模板保存
- 模板分享和导入

### 2. 增量回补
- 智能检测数据缺失
- 自动补充缺失数据
- 增量更新策略

### 3. 性能监控
- 执行时间统计
- 资源使用监控
- 性能优化建议

### 4. 分布式执行
- 多节点并行处理
- 负载均衡策略
- 容错和恢复机制
