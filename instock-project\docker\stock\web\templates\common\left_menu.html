{% block left_menu %}
<link rel="stylesheet" href="/static/css/ace.min.css" class="ace-main-stylesheet" id="main-ace-style" />
<link rel="stylesheet" href="/static/css/ace-skins.min.css"/>
<script src="/static/js/ace.min.js"></script>
<script src="/static/js/ace-elements.min.js"></script>
<script src="/static/js/ace-extra.min.js"></script>
<script type="text/javascript">
    try{ace.settings.loadState('sidebar')}catch(e){}
    $.extend({
        getUrlVars: function(){
            let vars = [], hash;
            const hashes = window.location.href.slice(window.location.href.indexOf('?') + 1).split('&');
            for(let i = 0; i < hashes.length; i++)
            {
                hash = hashes[i].split('=');
                vars.push(hash[0]);
                vars[hash[0]] = hash[1];
            }
            return vars;
        },
        getUrlVar: function(name){
            return $.getUrlVars()[name];
        }
    });
</script>
<div id="navbar" class="navbar navbar-default ace-save-state" style="display:none;">
    <div class="navbar-container ace-save-state" id="navbar-container">
        <button type="button" class="navbar-toggle menu-toggler pull-left" id="menu-toggler" data-target="#sidebar">
            <span class="sr-only">Toggle sidebar</span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
        </button>
    </div>
</div>
<script type="text/javascript">
    if('ontouchstart' in document.documentElement) document.getElementById('navbar').style.display = "block";
</script>
<div id="sidebar" class="sidebar responsive ace-save-state navbar_fixed sidebar-fixed">
    <ul class="nav nav-list">
        <li class="">
            <a href="/">
                <i class="menu-icon fa fa-tachometer"></i>
                <span class="menu-text"> InStock股票系统 </span>
            </a>
            <b class="arrow"></b>
        </li>
{% set loopType = "" %}
{% for leftMenuTmp in leftMenu.leftMenuList %}
    {% if leftMenuTmp.type != loopType %}
        {% if loopType != "" %}
            </ul>
        {% end %}
        {% set loopType = leftMenuTmp.type %}
        {% set isOpenStr = "" %}
        {% try %}
            {% if web_module_data.type == leftMenuTmp.type %}
                {% set isOpenStr = "open" %}
            {% end %}
        {% except %}
        {% end %}
        <li class="{{ isOpenStr }}">
            <a href="#" class="dropdown-toggle">
                <i class="menu-icon {{ leftMenuTmp.ico }}"></i>
                <span class="menu-text">{{ leftMenuTmp.type }}</span>
                <b class="arrow fa fa-angle-down"></b>
            </a>
            <b class="arrow"></b>
            <ul class="submenu">
    {% end %}
                {% set isActiveStr = "" %}
                {% try %}
                    {% if web_module_data.name == leftMenuTmp.name %}
                        {% set isActiveStr = "active" %}
                    {% end %}
                {% except %}
                {% end %}
                <li class="{{ isActiveStr }}">
                    <a href="{{ leftMenuTmp.url }}">
                        <i class="menu-icon fa fa-caret-right"></i>
                        {{ leftMenuTmp.name }}
                    </a>
                    <b class="arrow"></b>
                </li>
{% end %}
            </ul>
        </li>
    </ul>
    <div class="sidebar-toggle sidebar-collapse" id="sidebar-collapse">
        <i id="sidebar-toggle-icon" class="ace-icon fa fa-angle-double-left ace-save-state" data-icon1="ace-icon fa fa-angle-double-left" data-icon2="ace-icon fa fa-angle-double-right"></i>
    </div>
</div>
{% end %}