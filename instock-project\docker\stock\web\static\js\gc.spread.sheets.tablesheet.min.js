/*!
 * 
 * SpreadJS Library 18.1.2
 *
 * Copyright(c) of respective holders.  All rights reserved.
 *
 * Licensed under the SpreadJS Commercial License.
 *
 */
!function(t){"object"==typeof module&&"object"==typeof module.exports?module.exports=t(require("@grapecity/spread-sheets")):"function"==typeof define&&define.amd?define(["@grapecity/spread-sheets"],t):"object"==typeof exports?exports.Spread=t(require("@grapecity/spread-sheets")):t(GC)}(function(e){!function(){"use strict";var n,i,t;function o(t){var e,e,e=i[t];return void 0!==e||(e=i[t]={exports:{}},n[t].call(e.exports,e,e.exports,o)),e.exports}n={"./dist/plugins/tableSheet/actionColumn.js":function(t,e,n){var i,o;function u(t){this.LEt={visible:!1,title:"Actions",width:210,actionButtons:{remove:{visible:!0,title:"Remove",icon:i.ButtonImageType.minus},save:{visible:!0,title:"Save",icon:i.ButtonImageType.ok},reset:{visible:!0,title:"Reset",icon:i.ButtonImageType.clear}},headerStyle:null},this.hce=t}Object.defineProperty(e,"__esModule",{value:!0}),e.ActionColumn=void 0,i=n("Core"),u.prototype.options=function(t){var e;if(!t)return this.LEt;e=this.LEt,this.LEt=t,this.hce(e,t)},u.prototype.toJSON=function(){return this.LEt},u.prototype.fromJSON=function(t){this.LEt=t},e.ActionColumn=u},"./dist/plugins/tableSheet/arrayFunction.js":function(t,e,n){var i,e,n;Object.defineProperty(e,"__esModule",{value:!0}),(i=n("CalcEngine")).Functions.findGlobalFunction(e="CONVERTARRAY")||((n=new i.Functions.Function(e,1,255)).evaluate=function(){var t=[].slice.apply(arguments);return new i.CalcArray([t])},i.Functions.defineGlobalCustomFunction(e,n))},"./dist/plugins/tableSheet/data.dataManager.js":function(t,e,n){var e,i,o,c,s,u,r;function l(t){return t instanceof Function}function a(){}Object.defineProperty(e,"__esModule",{value:!0}),e=n("Bindings"),i=n("Common"),o=n("DataManager"),c=i.Common.lt.ht,s=null,u=void 0,a.prototype.isDataSource=function(t){return o&&o.isView&&o.isView(t)},a.prototype.getDataLength=function(t){return t.visibleLength()},a.prototype.getDataItem=function(t,e,n){var i;return e<0?s:(i=u,n&&((i={})[n]=1),t.getRowData(e,u,i))},a.prototype.addItems=function(t,e,n,i,o){},a.prototype.removeItems=function(t,e,n,i){var o=this.getDataLength(t);i()},a.prototype.undoRemoveItems=function(t,e,n){},a.prototype.getProperties=function(t){var t=this.getDataItem(t,0);return t?Object.keys(t):[]},a.prototype.getValue=function(t,e,n,i,o,u){var e,o,r,t,a,o=t.kve(o);return o?(r=s,t=t.Nve(i,o),a=!1,c(t)||(a=!0,e&&l(e)&&(t=e(((e={})[n]=t,e))),r=u?u(o,i,t):t),{value:r,hasBinding:a}):{value:s,hasBinding:!0}},a.prototype.setValue=function(t,e,n,i,o,u,r){},a.prototype.allowSetCount=function(){return!0},a.prototype.canAdd=function(){return!0},a.prototype.canInsert=function(){return!1},a.prototype.fromJSON=function(t){return t},a.prototype.toJSON=function(t){return t},e.X$.dataManagerBindingSource=new a},"./dist/plugins/tableSheet/overr-write-worksheet.js":function(t,e,n){var i,n,o,u,r;function a(t){return void 0===t}function c(t,e,n){var t=r.call(this,t)||this;return t.NMi=e,t.qni=n,t}i=this&&this.__extends||(u=function(t,e){return(u=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}u(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),Object.defineProperty(e,"__esModule",{value:!0}),e.OverWriteWorkSheet=void 0,n=n("Core"),r=n.Worksheet,i(c,r),c.prototype.Mtt=function(t){this.Ldi(t)?this.qni.undo(t):r.prototype.Mtt.call(this,t)},c.prototype.vCn=function(){r.prototype.vCn.call(this),this.qni.startTransaction()},c.prototype.FCn=function(){var t,e,t=r.prototype.FCn.call(this),e=this.qni.endTransaction();return 0<(null==e?void 0:e.length)?e:t},c.prototype.Mno=function(){return!a(this.Ino)},c.prototype.mno=function(){this.Ino={}},c.prototype.Nno=function(){this.Dno(),this.Ino=void 0},c.prototype.jno=function(t){a(this.Ino[t])&&(this.Ino[t]=!0)},c.prototype.Dno=function(){if(this.Ino)for(var t in this.Ino)this.Ino[t]&&this.NMi.iM(void 0,t)},c.prototype.resumePaint=function(){r.prototype.resumePaint.call(this),this.My<=0&&this.NMi.refreshDataProvider()},c.prototype.repaint=function(){r.prototype.repaint.call(this),this.My<=0&&this.NMi.refreshDataProvider()},c.prototype.Ldi=function(t){var t=null==t?void 0:t[0];return!(null==t||!t.sourceType)},e.OverWriteWorkSheet=c},"./dist/plugins/tableSheet/tableSheet-action.js":function(Z,t,e){var n,o,e,a,e,U,e,G,B,R,P,Q,Y,W,H,V,F,_,J,X,K,q,i,u,r,c,s,l,f,d,h,g,b,v,M,I,m,N,D;function j(){return null!==u&&u.apply(this,arguments)||this}function C(){return null!==r&&r.apply(this,arguments)||this}function w(){return null!==c&&c.apply(this,arguments)||this}function p(){return null!==s&&s.apply(this,arguments)||this}function x(){return null!==l&&l.apply(this,arguments)||this}function y(){return null!==f&&f.apply(this,arguments)||this}function A(){return null!==d&&d.apply(this,arguments)||this}function T(){return null!==h&&h.apply(this,arguments)||this}function S(){return null!==g&&g.apply(this,arguments)||this}function z(){return null!==b&&b.apply(this,arguments)||this}function L(){return null!==v&&v.apply(this,arguments)||this}function O(){return null!==M&&M.apply(this,arguments)||this}function k(){return null!==I&&I.apply(this,arguments)||this}function $(){return null!==m&&m.apply(this,arguments)||this}function tt(){return null!==N&&N.apply(this,arguments)||this}function E(){return null!==D&&D.apply(this,arguments)||this}function et(t,e){var n,i,o,n=e.getDataView();n&&(i=e.Mf.rowFilter(),(o=e.TLt(n,t,t))&&(e.EPt(),i.UIi()),e.Mf.rowFilter().VIi(t),n.hRe(t),o&&(i.QIi(),e.jPt()),e.restoreView(e.getDataView()))}n=this&&this.__extends||(i=function(t,e){return(i=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),Object.defineProperty(t,"__esModule",{value:!0}),t.TableSheetRemoveColumnInfoAction=t.TableSheetResertViewAction=t.ViewGroupOptionsAction=t.ViewMoveColumnAction=t.ViewAddColumnInfoAction=t.ViewRemoveColumnInfoAction=t.ViewUpdateColumnInfoAction=t.DefineColumnToTableAction=t.RemoveColumnToTableAction=t.ModifyColumnToTableAction=t.ExpandGroupItemAction=t.ExpandGroupAction=t.RemoveRowAction=t.PinRowAction=t.ResetRowAction=t.TableSheetActionBase=void 0,o=e("Core"),e=e("Common"),a=e.Common.lt.ht,e=o.Commands.ActionBase,U=function(){},n(j,u=e),Object.defineProperty(j.prototype,"sheet",{get:function(){return this.Mf},enumerable:!1,configurable:!0}),Object.defineProperty(j.prototype,"sheetTab",{get:function(){return this.sheet.NMi},enumerable:!1,configurable:!0}),j.prototype.execute=function(){var t,e,n,i,t=this,e=!1;if(t.canExecute()){i=(n=t.sheet).parent.i4(),o.Commands.II(i),t.X5(n,!0);try{"boolean"!=typeof(e=this.kdi())&&(e=!0)}finally{t.q5(n,!0),o.Commands.xI(i,t.tT)}}return e},j.prototype.undo=function(){var t,e,n,i,t=this,e=t.sheet,n=!1;if(t.canUndo()){t.X5(e,!0);try{i=e.parent.i4(),t.mze(),o.Commands.NI(i,t.tT),this.rbi(),n=!0}finally{t.q5(e,!0)}}return n},j.prototype.mze=function(){},j.prototype.rbi=function(){},t.TableSheetActionBase=e=j,n(C,r=e),C.prototype.kdi=function(){var t=this.tT,e=t.tableSheet,t=t.index;e.Mf.ki(o.Events.RowOperation,{sheet:e,sheetName:e.name(),actionType:2,row:t}),e.resetRow(t)},t.ResetRowAction=C,n(w,c=e),w.prototype.kdi=function(){var t=this.tT,e=t.tableSheet,t=t.index;e.togglePinnedRows([t])},t.PinRowAction=w,n(p,s=e),p.prototype.kdi=function(){var t=this.tT,e=t.tableSheet,t=t.index;e.Mf.ki(o.Events.RowOperation,{sheet:e,sheetName:e.name(),actionType:0,row:t}),e.removeRow(t).catch(U)},t.RemoveRowAction=p,n(x,l=e),x.prototype.kdi=function(){var t,e,t,n,e,t=this,e=t.tT,t=t.sheetTab,n=e.field,e=e.expand;t.expandGroup(n,e)},t.ExpandGroupAction=x,n(y,f=e),y.prototype.kdi=function(){var t,e,t,n,i,e,t=this,e=t.tT,t=t.sheetTab,n=e.level,i=e.index,e=e.expand;t.expandGroupItem(n,i,e)},t.ExpandGroupItemAction=y,n(A,d=e),A.prototype.kdi=function(){var t,e,t,n,i,e,t=this,e=t.tT,t=t.sheetTab,n=e.column,i=e.originalColumn,e=e.col;return t.JEe(e,n,i)},A.prototype.rbi=function(){var t;this.sheetTab.refreshBindColumns()},t.ModifyColumnToTableAction=A,n(T,h=e),T.prototype.kdi=function(){var t,e,t,n,e,t=this,e=t.tT,t=t.sheetTab,n=e.column,e=e.col;return t.iGe(e,n)},T.prototype.rbi=function(){var t;this.sheetTab.refreshBindColumns()},t.RemoveColumnToTableAction=T,n(S,g=e),S.prototype.kdi=function(){var t,e,t,n,e,t=this,e=t.tT,t=t.sheetTab,n=e.column,e=e.col;return t.qFe(e,n)},S.prototype.rbi=function(){var t;this.sheetTab.refreshBindColumns()},t.DefineColumnToTableAction=S,n(z,b=e),z.prototype.kdi=function(){var t,e,t,e,n,o,i,t=this,e=t.tT,t=t.sheetTab,e=e.columnInfos,n=t.getDataView(),o=n.getColumnInfos(),i=t.Teo(!0)&&e.some(function(e){var t,n,i,t=!1!==e.visible,n=o.find(function(t){return t.value===e.value});return!(!n||t==(!1!==n.visible))});i&&t.qni.startAddComboChanges(),e.forEach(function(t){n.updateColumn(t)}),t.refreshBindColumns(),i&&(t.Seo(),t.qni.endAddComboChanges())},z.prototype.rbi=function(){var t;this.sheetTab.refreshBindColumns()},t.ViewUpdateColumnInfoAction=z,n(L,v=e),L.prototype.kdi=function(){var t,e,t,e,n,t=this,e=t.tT,t=t.sheetTab,e=e.value,n=t.getDataView();t.qni.startAddComboChanges(),n.removeColumn(e),t.refreshBindColumns(),t.Seo(),t.qni.endAddComboChanges()},L.prototype.rbi=function(){var t;this.sheetTab.refreshBindColumns()},t.ViewRemoveColumnInfoAction=L,n(O,M=e),O.prototype.kdi=function(){var t,e,t,e,n,t=this,e=t.tT,t=t.sheetTab,e=e.columnInfo,n;t.getDataView().addColumn(e),t.refreshBindColumns()},O.prototype.rbi=function(){var t;this.sheetTab.refreshBindColumns()},t.ViewAddColumnInfoAction=O,n(k,I=e),k.prototype.kdi=function(){var t,e,t,n,i,o,u,e,r,t=this,e=t.tT,t=t.sheetTab,n=e.fromColumnOptionIndex,i=e.toColumnOptionIndex,o=e.fromColumnIndex,u=e.toColumnIndex,e=e.count,r=!0;!(r=a(n)||a(i)?r:!t.DPt(n,i))||a(o)||a(u)||a(e)||t.OPt(o,u,e)},t.ViewMoveColumnAction=k,n($,m=e),$.prototype.kdi=function(){var t,e,t,e,t=this,e=t.tT,t=t.sheetTab,e=e.groupOptions;0<(null==e?void 0:e.length)?t.groupBy(e):t.removeGroupBy()},t.ViewGroupOptionsAction=$,n(tt,N=e),tt.prototype.kdi=function(){var t,e,t,e,n,i,e,t=this,e=t.tT,t=t.sheetTab,e=e.newConfig,n=e.columnInfoOptions,i=e.includeDefaultColumns,e=e.options;t.resetNewView(n,i,e)},t.TableSheetResertViewAction=tt,n(E,D=e),E.prototype.kdi=function(){var t,e,t,n,t=this,e=t.tT,t=t.sheetTab,n;et(e.index,t)},E.prototype.rbi=function(){var t=this.sheetTab;t.restoreView(t.getDataView())},t.TableSheetRemoveColumnInfoAction=E},"./dist/plugins/tableSheet/tableSheet-grouping.js":function(v,t,e){var n,u,Z,U,M,I,G,B,R,A,X,T,m,N,D,j,C,O,P,w,x,k,y,E,e,M,K,S,q,$,tt,et,nt,z,it,h,c,ot,ut,rt,p,Q;function Y(t){return void 0===t}function at(t,e){return T(t)&&(t=Number.MIN_VALUE),T(e)&&(e=Number.MIN_VALUE),A(t,e)}function W(t){return L(t)||H(t)}function L(t){return t===U.GroupLayoutMode.outline}function H(t){return t===U.GroupLayoutMode.condensed}function ct(t,e){var n,i;return t?(i=t.headerStyle&&!!t.headerStyle.formatter,n=gt(t.headerStyle,e),i||(n.formatter=B)):(n=e.clone()).formatter=B,n}function st(t,e){return t?gt(t.style,e):e}function V(t){var e=new Z.Style;return Y(t.backColor)||(e.backColor=t.backColor),Y(t.foreColor)||(e.foreColor=t.foreColor),Y(t.font)||(e.font=t.font),Y(t.themeFont)||(e.themeFont=t.themeFont),Y(t.cellPadding)||(e.cellPadding=t.cellPadding),e}function lt(t){return ft(t.value,t.caption)}function ft(t,e){return T(t)?B:e?"".concat(e).concat((0,G.getSR)().TextColon," ").concat(t):""+t}function dt(t){return ht(t.value,t.field)}function ht(t,e){return T(t)?""+e:"".concat(e).concat((0,G.getSR)().TextColon," ").concat(t)}function F(t,e,n){var t,t=t instanceof Z.Style?t.clone():new Z.Style(t);return Y(t.foreColor)&&n&&!Y(n.foreColor)&&(t.foreColor=n.foreColor),e&&t.v5(e),t}function gt(t,e){var t;return t&&(t=F(t),e&&t.compose(e),e=t),e&&(delete e.cellButtons,delete e.dropDowns),e}function _(t,e){return t.hAlign===Z.HorizontalAlign.general||T(t.hAlign)?e:t.hAlign}function bt(t,e,n){var i,o;for(o in t=t||{},i=e[n])i.hasOwnProperty(o)&&T(t[o])&&(t[o]=u(u({},i[o]),{value:e.value}));return t}function vt(t,e,n){var i,o,u;for(o in t)t.hasOwnProperty(o)&&((u=t[o])[e]&&(i=bt(i,u,e)),n)&&(i=bt(i,u));return i}function Mt(t,e){var n,i,o,u,r,a;for(n in t)"parent"!==n&&"children"!==n&&(e[n]=t[n]);for(e.children=[],o=0,u=i=t.children;o<u.length;o++)r=u[o],e.children.push(a={}),Mt(r,a)}function It(t){for(var e,n,i,o,e,n=0,i=t.children;n<i.length;n++)(o=i[n]).parent=t,It(o)}function mt(t,e){var n,i,o,u,r,a,c,n=!0,i=t.start===e.start&&t.end===e.end,o=t.children.length===e.children.length;if(i&&o)for(u=t.children.length,r=0;r<u&&!1!==(n=mt(a=t.children[r],c=e.children[r]));r++);else n=!1;return n}function Nt(t){return t&&t.row&&"number"==typeof t.row&&0<t.row?t.row:B}function Dt(t){var t,t,t,e,t,e=0,t=null==(t=null==(t=null==(t=null==t?void 0:t.cellNode)?void 0:t.field)?void 0:t.extra)?void 0:t.spacing;return e=t?Nt(t):e}function J(t){if(t.dataRange)return t.dataRange.convertContext({sheet:t.sheet,row:t.row,col:t.col,sheetArea:t.sheetArea})}function jt(t){return!T(t)&&""!==t}function Ct(t,e,n,i){return jt(e)&&jt(i)?"".concat(t," ").concat(e," | ").concat(n," ").concat(i):jt(e)?"".concat(t," ").concat(e):jt(i)?"".concat(n," ").concat(i):void 0}function i(){}function o(){return null!==z&&z.apply(this,arguments)||this}function wt(){return null!==it&&it.apply(this,arguments)||this}function pt(t,e,n){for(var i,o,u,r,a,i,r=t.getRowCount(Z.SheetArea.colHeader)-1;0<=r;r--){if(a=t.getValue(r,n,Z.SheetArea.colHeader),T(o))o=a;else if(o!==a)break;u=r}return e===u}function r(t){var e=h.call(this)||this;return e.Tno=t,e.typeName="98",e}function a(t){var e=c.call(this)||this;return e.Tno=t,e}function s(t){var e=ot.call(this)||this;return e.Tno=t,e}function l(t){var t=ut.call(this,t)||this;return t.typeName="99",t}function xt(t){var t=rt.call(this,t)||this;return t.typeName="108",t}function f(){var t=p.call(this)||this;return t.zeo=U.GroupLayoutMode.outline,t.Leo=!1,t.typeName="109",t}function d(){var t=Q.call(this)||this;return t.zeo=U.GroupLayoutMode.outline,t.typeName="110",t}function g(){this.tMi=U.GroupOutlinePosition.groupCell,this.GY=U.GroupLayoutMode.tabular,this.Oeo=m}function b(){this._data=new N,this._ui=new tt}n=this&&this.__extends||(nt=function(t,e){return(nt=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}nt(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),u=this&&this.__assign||function(){return(u=Object.assign||function(t){for(var e,n,i,o,n=1,i=arguments.length;n<i;n++)for(o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},Object.defineProperty(t,"__esModule",{value:!0}),t.Grouping=t.nMi=t.getHorizontalAlign=t.getGroupHeaderValueFromCondensed=t.getValueFromCondensed=t.composeValueStyle=t.composeHeaderStyle=t.isGroupCondensedLayout=t.isGroupOutlineLayout=t.isGroupOutlineCondensedLayout=void 0,Z=e("Core"),U=e("./dist/plugins/tableSheet/tableSheet.interface.js"),M=e("Common"),I=e("./dist/plugins/tableSheet/tablesheet-command.js"),G=e("./dist/plugins/tableSheet/tableSheet.js"),B=void 0,R=Math.round,A=Math.max,X=Math.ceil,T=M.Common.lt.ht,m={position:"header"},t.isGroupOutlineCondensedLayout=W,t.isGroupOutlineLayout=L,t.isGroupCondensedLayout=H,t.composeHeaderStyle=ct,t.composeValueStyle=st,t.getValueFromCondensed=lt,t.getGroupHeaderValueFromCondensed=dt,t.getHorizontalAlign=_,i.prototype.Hc=function(t,e){var e,n,i,e={level:-1,collapsed:!1,children:[],parent:B,start:0,end:e},n=0,i=0;this.eMi(t,0,0,e),this.oMi&&this.wno(this.oMi,e),this.oMi=e},i.prototype.eMi=function(t,e,n,i){for(var o,u,r,a,c,s,l,f,d,o=i.children,u,r=0,a=0,c=t.childrenArray&&t.childrenArray[0]||[];a<c.length;a++)l=(s=c[a]).recordRowNumbers,s.childrenArray&&s.childrenArray[0]&&0!==s.childrenArray[0].length?s.childrenArray&&s.childrenArray[0]&&s.childrenArray[0].length&&(f={level:n,value:s.value,collapsed:!1,children:[],parent:i,start:e,end:0,spacingIndex:s.spacingIndex},d=this.eMi(s,e,n+1,f),f.end=e+d-1,o.push(f),e+=d,r+=d):(o.push({start:e,end:e+l.length-1,level:n,collapsed:!1,children:[],parent:i,spacingIndex:s.spacingIndex,value:s.value}),e+=l.length,r+=l.length);return r},i.prototype.wno=function(t,e){for(var n,i,o,u,r,a,c,s,n=t.children,i,o=0,u=e.children;o<u.length;o++)for(r=u[o],a=0,c=n;a<c.length;a++)if(s=c[a],r.value===s.value){r.collapsed=s.collapsed,this.wno(s,r);break}},i.prototype.uMi=function(t,e,n,i){for(var o=0;o<t.length;o++)if(t[o].field===e){this.cMi(this.oMi,o,n,i);break}},i.prototype.cMi=function(t,e,n,i){for(var o,u,r,a,o,u=0,r=t.children;u<r.length;u++)a=r[u],this.rMi(a,e,n,i)},i.prototype.rMi=function(t,e,n,i){var o,u,r,a,c;if(t.level===e)t.collapsed!==(o=!n)&&(t.collapsed=o,i)&&i(t.level,t.start);else for(r=0,a=u=t.children;r<a.length;r++)c=a[r],this.rMi(c,e,n,i)},i.prototype.aMi=function(t,e,n,i){var o,u,r,a,c,s,l,o=this.oMi,u=!n;if(o&&o.start<=e&&e<=o.end)for(r=o.children;0<r.length;){for(a=!0,c=0,s=r;c<s.length;c++)if((l=s[c]).start<=e&&e<=l.end){if(l.level===t)return l.collapsed!==u&&i(!l.collapsed),void(l.collapsed=u);r=l.children,a=!1;break}if(a)break}},i.prototype.Xto=function(t){for(t=t.parent;t;){if(t.collapsed)return!0;t=t.parent}return!1},i.prototype.sMi=function(t,e,n,i,o){var u,r,a,c,s,l,f,d,h,g,u=-1,r=this.oMi,a="number"==typeof i;if(r&&r.start+n<=t&&t<=r.end){if(c=r.collapsed,a&&!o&&(c=r.spacingIndex===i&&r.parent&&this.Xto(r)),u<e&&c)return!1;for(s=r.children,u++;0<s.length;){for(l=!0,f=0,d=s;f<d.length;f++)if((h=d[f]).start+n<=t&&t<=h.end){if(g=h.collapsed,a&&!o&&(g=h.spacingIndex===i&&h.parent&&this.Xto(h)),u<e&&g)return!1;s=h.children,u++,l=!1;break}if(l)break}}return!0},i.prototype.MMi=function(t){var e,n,i,o,u,r,a,e=[],n=this.oMi;if(n&&n.start<=t&&t<=n.end)for(i=n.children;0<i.length;){for(o=!0,u=0,r=i;u<r.length;u++)if((a=r[u]).start<=t&&t<=a.end){e.push(a),i=a.children,o=!1;break}if(o)break}return e},i.prototype.IMi=function(t,e){var n,i,o,u,r,a,n=this.oMi;if(n&&n.start<=t&&t<=n.end)for(i=n.children;0<i.length;){for(o=!0,u=0,r=i;u<r.length;u++)if((a=r[u]).start<=t&&t<=a.end){if(a.level===e)return a;i=a.children,o=!1;break}if(o)break}},i.prototype.gMi=function(t){var e,n,i,o,u,r,a,c,s,l,f,d,e=this.oMi;if(!e)return!1;for(n=e.children.slice(0),i=0;i<t;){for(o=[],u=0,r=n;u<r.length;u++)for(c=0,s=(a=r[u]).children;c<s.length;c++)l=s[c],o.push(l);n=o,i++}for(f=0,d=n;f<d.length;f++)if(!(a=d[f]).collapsed)return!1;return!0},i.prototype.L0=function(){this.oMi=B},i.prototype.toJSON=function(){var t;return this.oMi&&(Mt(this.oMi,t={}),t)},i.prototype.fromJSON=function(t,e){var n=this.oMi;(e||n&&t&&mt(this.oMi,t))&&(It(t),this.oMi=t)},N=i,D=40,j=16,C=4,P=O=2.5,w=5,z=Z.CellTypes.RowHeader,n(o,z),o.prototype.paint=function(t,e,n,i,o,u,r,a){var c,s,l,l,f,d,h,g,b,v,M,I,m,N,D,j,C,w,p,x,y,A,C,T,S,c=a.sheet,s=a.row;if(a.dataRange&&(l=J(a))&&(c=l.sheet,s=l.row),f=16*(l=a.sheet.zoom()),d=4*l,h=O*l,g=c.NMi)for(M=!(v=W(b=g.keo())),N=(40*(m=(I=g.MMi(s)).length)-16*m)/(m+1)*l,z.prototype.paintValue.call(this,t,e,n,i,o,u,r,a),D=0;D<m;D++)j=I[D],(D!==g.lMi.length-1||g.detailColumnsVisible())&&g.DMi(D,s)&&(t.save(),t.beginPath(),t.strokeStyle="#999999",t.lineWidth=1,w=(C=n+N)+f/2,p=i+u,x=i+u/2,(y=j.start===s)&&M?(A=R(C)-.5,C=R(C+f)-.5,T=R(x-f/2)-.5,S=R(x-f/2+f)-.5,t.moveTo(A,T),t.lineTo(C,T),t.lineTo(C,S),t.lineTo(A,S),t.lineTo(A,T),t.moveTo(R(w-d)-.5,R(x)-.5),t.lineTo(R(w+d)-.5,R(x)-.5),j.collapsed?(t.moveTo(R(w)-.5,R(x-d)-.5),t.lineTo(R(w)-.5,R(x+d)-.5)):(t.moveTo(R(w)-.5,R(x+f/2)-.5),t.lineTo(R(w)-.5,R(p)-.5))):j.start<s&&s<j.end||y&&v||j.end===s&&this.Eeo(c,s,D)?(t.moveTo(R(w)-.5,R(i)-.5),t.lineTo(R(w)-.5,R(p)-.5)):j.end===s&&(t.moveTo(R(w)-.5,R(i)-.5),t.lineTo(R(w)-.5,R(p-h)-.5),t.lineTo(R(w+d)-.5,R(p-h)-.5)),t.stroke(),t.restore(),n+=N+f)},o.prototype.fMi=function(t,e,n,i,o,u){var r,u,a,c,s,l,f,d,h,g,b,v,M,I,v,m,M,r=t.x,u=u.sheet.zoom(),a=16*u,c=i.NMi;if(c){if(l=W(s=c.keo()))return B;for(h=(40*(d=(f=c.MMi(o)).length)-16*d)/(d+1)*u,g=0;g<d;g++)if(b=f[g],(g!==c.lMi.length-1||c.detailColumnsVisible())&&c.DMi(g,o)){if(v=r+h,M=t.y+t.height/2,b.start===o&&(I=R(v)-.5,v=R(v+a)-.5,m=R(M-a/2)-.5,M=R(M-a/2+a)-.5,I<=e)&&e<=v&&m<=n&&n<=M)return{level:g,index:o,collapsed:b.collapsed};r+=h+a}}},o.prototype.getHitInfo=function(t,e,n,i,o){var u,r,a,c,s,s;if(o&&(u=o.sheet,r=o.row,a=o.col,c=o.sheetArea,o.dataRange&&(s=J(o))&&(u=s.sheet,r=s.row,a=s.col,c=s.sheetArea),2===c)&&(s=this.fMi(i,t,e,u,r,o)))return{x:t,y:e,row:r,col:a,cellRect:i,sheetArea:2,isReservedLocation:!0,sheet:u,level:s.level,index:s.index,collapsed:s.collapsed}},o.prototype.processMouseDown=function(t){var e,n,i,t;return!(!t||!(e=t.sheet.NMi)||(n=t.level,i=t.index,t=t.collapsed,e.zA({cmd:I.EXPAND_GROUP_ITEM,level:n,index:i,expand:t}),0))},o.prototype.Eeo=function(t,e,n,i){void 0===i&&(i=0);var t=t.ncr&&t.ncr(e,i,-1,!0);return!(!t||t.isSpacing)&&n<=t.level},o.prototype.Zeo=function(e,n,i,t,o){var u,o,u=this;return!!o&&(o=function(t){return u.Eeo(e,n,i,t)})(t)&&o(t+1)},n(wt,it=x=o),wt.prototype.paint=function(t,e,n,i,o,u,r,a){var c,s,l,f,d,d,h,g,b,v,M,I,m,N,D,j,C,w,p,x,y,A,T,S,x,z,L,c=a.row,s=a.sheet,l=a.rowSpacingIndex,f=a.isAfterRowSpacing;if(a.dataRange&&(d=J(a))&&(s=d.sheet,c=d.row,l=d.rowSpacingIndex,f=d.isAfterRowSpacing),h=16*(d=a.sheet.zoom()),g=4*d,b=O*d,(v=s.NMi)&&(M=!f,I=s.ncr&&s.ncr(c,l,-1,f)))for(m=I.level,N=I.isSpacing,C=(40*(j=(D=v.MMi(c)).length)-16*j)/(j+1)*d,it.prototype.paintValue.call(this,t,e,n,i,o,u,r,a),w=0;w<j;w++)m<w||(p=D[w],(w!==v.lMi.length-1||v.detailColumnsVisible())&&v.DMi(w,c)&&(t.save(),t.beginPath(),t.strokeStyle="#999999",t.lineWidth=1,y=(x=n+C)+h/2,A=i+u,T=i+u/2,p.start===c&&w===m&&M?(S=R(x)-.5,x=R(x+h)-.5,z=R(T-h/2)-.5,L=R(T-h/2+h)-.5,t.moveTo(S,z),t.lineTo(x,z),t.lineTo(x,L),t.lineTo(S,L),t.lineTo(S,z),t.moveTo(R(y-g)-.5,R(T)-.5),t.lineTo(R(y+g)-.5,R(T)-.5),p.collapsed?(t.moveTo(R(y)-.5,R(T-g)-.5),t.lineTo(R(y)-.5,R(T+g)-.5)):(t.moveTo(R(y)-.5,R(T+h/2)-.5),t.lineTo(R(y)-.5,R(A)-.5))):p.start<c&&c<p.end||w<m&&(M||this.Zeo(s,c,w,l,f))?(t.moveTo(R(y)-.5,R(i)-.5),t.lineTo(R(y)-.5,R(A)-.5)):p.end!==c||N||(t.moveTo(R(y)-.5,R(i)-.5),t.lineTo(R(y)-.5,R(A-b)-.5),t.lineTo(R(y+g)-.5,R(A-b)-.5)),t.stroke(),t.restore(),n+=C+h))},wt.prototype.fMi=function(t,e,n,i,o,u){var r,a,c,s,l,f,u,d,u,h,g,b,v,M,I,m,N,D,m,j,N,r=t.x,a=u.sheet.zoom(),c=16*a,s=i.NMi;if(s&&(l=u.rowSpacingIndex,f=u.isAfterRowSpacing,u.dataRange&&(u=J(u))&&(l=u.rowSpacingIndex,f=u.isAfterRowSpacing),d=!f,u=i.ncr&&i.ncr(o,l,-1)))for(h=u.level,v=(40*(b=(g=s.MMi(o)).length)-16*b)/(b+1)*a,M=0;M<b;M++)if(!(h<M&&d)&&(I=g[M],M!==s.lMi.length-1||s.detailColumnsVisible())&&s.DMi(M,o)){if(m=r+v,N=t.y+t.height/2,I.start===o&&M===h&&d&&(D=R(m)-.5,m=R(m+c)-.5,j=R(N-c/2)-.5,N=R(N-c/2+c)-.5,D<=e)&&e<=m&&j<=n&&n<=N)return{level:M,index:o,collapsed:I.collapsed};r+=v+c}},k=wt,y=2.5,t.nMi=pt,h=Z.CellTypes.ColumnHeader,n(r,h),r.prototype.dMi=function(t,e){for(var n=0;n<e;n++)if(t.gMi(n))return!1;return!0},r.prototype.Sno=function(t,e,n,i,o,u){var r,a,a,o,r,c,s,r=o.sheet;return o.dataRange&&(a=J(o))&&(r=a.sheet),o=21*(a=o.sheet.zoom()),r=parseFloat(r.getDefaultStyle().cellPadding)*a,{x:c=u?t-o+r:t+o,y:e,w:s=n-o,h:i}},r.prototype.paint=function(t,e,n,i,o,u,r,a){var n;h.prototype.paint.call(this,t,e,n,i,o,u,r,a),this.Tno&&(n=this.Sno(n,i,o,u,a),this.Tno.paint(t,e,n.x,n.y,n.w,n.h,r,a))},r.prototype.paintContent=function(t,e,n,i,o,u,r,a){var c,s,l,f,f,o,e,o,r,a,c,s,d,c,a,l,r,s,f,n,a,i,c=a.sheet,s=a.row,l=a.col;a.dataRange&&(f=J(a))&&(c=f.sheet,s=f.row,l=f.col),f=a.sheet.zoom(),o=this.Sno(n,i,o,u,a),this.Tno||h.prototype.paintContent.call(this,t,e,o.x,o.y,o.w,o.h,r,a),e=16*f,o=4*f,r=c.NMi,a=c.getDataSource(),r&&r.lMi&&0<r.lMi.length&&a&&pt(c,s,l)&&((c=r.groupOutlinePosition())!==U.GroupOutlinePosition.groupCell&&c!==U.GroupOutlinePosition.groupCellAll||!(d=(s=a.kve(l).OFt)===r.lMi.length-1&&!r.detailColumnsVisible())&&this.dMi(r,s)&&(c=r.gMi(s),a=y*f,t.save(),t.beginPath(),t.strokeStyle="#999999",t.lineWidth=1,r=(l=n+a)+e/2,s=i+u/2,f=R(l)-.5,n=R(l+e)-.5,a=R(s-e/2)-.5,i=R(s-e/2+e)-.5,t.moveTo(f,a),t.lineTo(n,a),t.lineTo(n,i),t.lineTo(f,i),t.lineTo(f,a),t.moveTo(R(r-o)-.5,R(s)-.5),t.lineTo(R(r+o)-.5,R(s)-.5),c&&(t.moveTo(R(r)-.5,R(s-o)-.5),t.lineTo(R(r)-.5,R(s+o)-.5)),t.stroke(),t.restore()))},r.prototype.fMi=function(t,e,n,i,o,u,r){var a,r,c,s,l,i,o,f,i,l,d,s,u,t,a,r,s,a=t.x,r=r.sheet.zoom(),c=16*r,s=i.NMi,l=i.getDataSource();if(s&&l&&pt(i,o,u)&&((i=s.groupOutlinePosition())===U.GroupOutlinePosition.groupCell||i===U.GroupOutlinePosition.groupCellAll)&&!(f=(o=l.kve(u).OFt)===s.lMi.length-1&&!s.detailColumnsVisible())&&this.dMi(s,o)&&(i=s.lMi[o].field,l=s.gMi(o),u=t.y+t.height/2,t=R(s=a+(d=y*r))-.5,a=R(s+c)-.5,r=R(u-c/2)-.5,s=R(u-c/2+c)-.5,t<=e)&&e<=a&&r<=n&&n<=s)return{field:i,level:o,collapsed:l}},r.prototype.getHitInfo=function(t,e,n,i,o){var u,r,a,c,s,s,c,r;if(o&&(u=o.sheet,r=o.row,a=o.col,c=o.sheetArea,o.dataRange&&(s=J(o))&&(u=s.sheet,r=s.row,a=s.col,c=s.sheetArea),1===c))return i=this.An(i,n,o.sheet.zoom()),(s=this.fMi(i,t,e,u,r,a,o))?{x:t,y:e,row:r,col:a,cellRect:i,sheetArea:1,isReservedLocation:!0,sheet:u,field:s.field,level:s.level,collapsed:s.collapsed}:this.Tno&&(c=this.Sno(t,e,B,B,o,!0),r=this.Tno.getHitInfo&&this.Tno.getHitInfo(c.x,e,n,i,o))?(r.Drt=!0,r):void 0},r.prototype.processMouseDown=function(t){var e,n,i;if(t&&(e=t.sheet.NMi)){if(!t.Drt)return n=t.field,i=t.collapsed,e.zA({cmd:I.EXPAND_GROUP,field:n,expand:i}),!0;if(this.Tno)return this.Tno.processMouseDown&&this.Tno.processMouseDown(t)}return!1},r.prototype.processMouseUp=function(t){return!!this.Tno&&this.Tno.processMouseUp&&this.Tno.processMouseUp(t)},r.prototype.processMouseMove=function(t){return!!this.Tno&&this.Tno.processMouseMove&&this.Tno.processMouseMove(t)},r.prototype.processMouseEnter=function(t){return!!this.Tno&&this.Tno.processMouseEnter&&this.Tno.processMouseEnter(t)},r.prototype.processMouseLeave=function(t){return!!this.Tno&&this.Tno.processMouseLeave&&this.Tno.processMouseLeave(t)},r.prototype.isReservedKey=function(t,e){return!!this.Tno&&this.Tno.isReservedKey&&this.Tno.isReservedKey(t,e)},r.prototype.processKeyDown=function(t,e){return!!this.Tno&&this.Tno.processKeyDown&&this.Tno.processKeyDown(t,e)},r.prototype.processKeyUp=function(t,e){return!!this.Tno&&this.Tno.processKeyUp&&this.Tno.processKeyUp(t,e)},r.prototype.getAutoFitWidth=function(t,e,n,i,o){var t,t=this.Tno&&this.Tno.getAutoFitWidth?this.Tno.getAutoFitWidth(t,e,n,i,o):h.prototype.getAutoFitHeight.call(this,t,e,n,i,o);return t},r.prototype.format=function(t,e,n,i){return this.Tno&&this.Tno.format?this.Tno.format(t,e,n,i):h.prototype.format.call(this,t,e,n,i)},E=r,Z.CellTypes.ti[98]=E,c=Z.CellTypes.Text,n(a,c),a.prototype.Sno=function(t,e,n,i,o,u){return{x:t,y:e,w:n,h:i}},a.prototype.paint=function(t,e,n,i,o,u,r,a){var n;c.prototype.paint.call(this,t,e,n,i,o,u,r,a),this.Tno&&(n=this.Sno(n,i,o,u,a),this.Tno.paint(t,e,n.x,n.y,n.w,n.h,r,a))},a.prototype.processMouseDown=function(t){return!!this.Tno&&this.Tno.processMouseDown&&this.Tno.processMouseDown(t)},a.prototype.processMouseUp=function(t){return!!this.Tno&&this.Tno.processMouseUp&&this.Tno.processMouseUp(t)},a.prototype.processMouseMove=function(t){return!!this.Tno&&this.Tno.processMouseMove&&this.Tno.processMouseMove(t)},a.prototype.processMouseEnter=function(t){return!!this.Tno&&this.Tno.processMouseEnter&&this.Tno.processMouseEnter(t)},a.prototype.processMouseLeave=function(t){return!!this.Tno&&this.Tno.processMouseLeave&&this.Tno.processMouseLeave(t)},a.prototype.isReservedKey=function(t,e){return!!this.Tno&&this.Tno.isReservedKey&&this.Tno.isReservedKey(t,e)},a.prototype.processKeyDown=function(t,e){return!!this.Tno&&this.Tno.processKeyDown&&this.Tno.processKeyDown(t,e)},a.prototype.processKeyUp=function(t,e){return!!this.Tno&&this.Tno.processKeyUp&&this.Tno.processKeyUp(t,e)},a.prototype.getAutoFitWidth=function(t,e,n,i,o){var t,t=this.Tno&&this.Tno.getAutoFitWidth?this.Tno.getAutoFitWidth(t,e,n,i,o):c.prototype.getAutoFitHeight.call(this,t,e,n,i,o);return t},a.prototype.format=function(t,e,n,i){return this.Tno&&this.Tno.format?this.Tno.format(t,e,n,i):c.prototype.format.call(this,t,e,n,i)},n(s,ot=e=a),s.prototype.Sno=function(t,e,n,i,o,u){var r,a,c,s,l,l,o,l,r,f,d,r,h,g,r=o.sheet,a=o.row,c=o.col,s=o.rowSpacingIndex;return o.dataRange&&(l=J(o))&&(r=l.sheet,a=l.row,c=l.col,s=l.rowSpacingIndex),o=21*(l=o.sheet.zoom()),l=parseFloat(r.getDefaultStyle().cellPadding)*l,h=u?t-o+l:t+o,(d=W(f=(r=r.NMi).keo()))&&r.Mf.ncr&&(r=r.Mf.ncr(a,s,c),h=u?t-o*(r+1)+l:t+o*(r+1)),{x:h,y:e,w:g=n-o,h:i}},s.prototype.getHitInfo=function(t,e,n,i,o){var u,r,a,c,s,l,f,f,c,r;if(o&&(u=o.sheet,r=o.row,a=o.col,c=o.sheetArea,s=o.rowSpacingIndex,l=o.isAfterRowSpacing,o.dataRange&&(f=J(o))&&(u=f.sheet,r=f.row,a=f.col,s=f.rowSpacingIndex,l=f.isAfterRowSpacing),3===c||T(c)))return i=this.An(i,n,o.sheet.zoom()),(f=this.fMi(i,t,e,u,r,a,s,o))?{x:t,y:e,row:r,col:a,cellRect:i,sheetArea:3,isReservedLocation:!0,sheet:u,level:f.level,index:f.index,collapsed:f.collapsed,rowSpacingIndex:s,isAfterRowSpacing:l}:this.Tno&&(c=this.Sno(t,e,B,B,o,!0),r=this.Tno.getHitInfo&&this.Tno.getHitInfo(c.x,e,n,i,o))?(r.Drt=!0,r):void 0},s.prototype.processMouseDown=function(t){var e,n,i,o;if(t&&(e=t.sheet.NMi)){if(!t.Drt)return n=t.level,i=t.index,o=t.collapsed,e.zA({cmd:I.EXPAND_GROUP_ITEM,level:n,index:i,expand:o}),!0;if(this.Tno)return this.Tno.processMouseDown&&this.Tno.processMouseDown(t)}return!1},n(l,ut=M=s),l.prototype.Ueo=function(t,e,n,i,o){var u;return!(!e||(t.keo()===U.GroupLayoutMode.tabular?e.start!==n:"number"!=typeof o||!t.Mf.ncr||"number"!=typeof t.Mf.ncr(n,o,i)))},l.prototype.paintValue=function(t,e,n,i,o,u,r,a){var c,s,l,f,f,d,h,c,g,b,v,M,I,m,N,e,m,D,e,r,m,j,I,c,a,f,h,s,l,v,c=a.sheet,s=a.row,l=a.col;a.dataRange&&(f=J(a))&&(c=f.sheet,s=f.row,l=f.col),d=21*(f=a.sheet.zoom()),h=c.NMi,c=c.getDataSource(),g=16*f,b=4*f,v=a.rowSpacingIndex,I=W(M=h.keo()),!T(m=e)&&I&&"number"==typeof v&&(m="".concat(e)),I&&h.Mf.ncr&&(N=h.Mf.ncr(s,a.rowSpacingIndex,l)),e=this.Sno(n,i,o,u,a),this.Tno||ut.prototype.paintValue.call(this,t,m,e.x,e.y,e.w,e.h,r,a),h&&c&&h.lMi&&0<h.lMi.length&&((m=h.groupOutlinePosition())!==U.GroupOutlinePosition.groupCell&&m!==U.GroupOutlinePosition.groupCellAll||!(D=(N=I?N:c.kve(l).OFt)===h.lMi.length-1&&!h.detailColumnsVisible())&&h.DMi(N,a.row)&&(e=P*f,r=5*f,m=h.IMi(s,N),j=this.Ueo(h,m,s,l,v))&&(t.save(),t.beginPath(),t.rect(n,i,o,u),t.clip(),t.beginPath(),t.strokeStyle="#999999",t.lineWidth=1,I=n+e,W(M)&&(I+=d*N),c=I+g/2,f=(a=i+r)+g/2,h=R(I)-.5,s=R(I+g)-.5,l=R(a)-.5,v=R(a+g)-.5,t.moveTo(h,l),t.lineTo(s,l),t.lineTo(s,v),t.lineTo(h,v),t.lineTo(h,l),t.moveTo(R(c-b)-.5,R(f)-.5),t.lineTo(R(c+b)-.5,R(f)-.5),m.collapsed&&(t.moveTo(R(c)-.5,R(f-b)-.5),t.lineTo(R(c)-.5,R(f+b)-.5)),t.stroke(),t.restore()))},l.prototype.fMi=function(t,e,n,i,o,u,r,a){var a,c,s,i,l,f,i,l,d,h,a,g,b,s,u,r,h,f,l,a=(a?a.sheet:i).zoom(),c=16*a,s=i.NMi,i=i.getDataSource();if(s&&i&&((l=s.groupOutlinePosition())===U.GroupOutlinePosition.groupCell||l===U.GroupOutlinePosition.groupCellAll)&&(l=21*a,!(d=(i=W(f=s.keo())&&s.Mf.ncr?s.Mf.ncr(o,r,u):i.kve(u).OFt)===s.lMi.length-1&&!s.detailColumnsVisible()))&&s.DMi(i,o)&&(h=P*a,a=5*a,g=s.IMi(o,i),b=this.Ueo(s,g,o,u,r))&&(s=t.x+h,W(f)&&(s+=l*i),u=t.y+a,r=R(s)-.5,h=R(s+c)-.5,f=R(u)-.5,l=R(u+c)-.5,r<=e)&&e<=h&&f<=n&&n<=l)return{level:g.level,index:o,collapsed:g.collapsed}},l.prototype.Ln=function(t,e,n,i,o){var u,r,a,a,u,u=o.sheet,r=o.col;return o.dataRange&&(a=J(o))&&(u=a.sheet,r=a.col),a=u.NMi,u=ut.prototype.Ln.call(this,t,e,n,i,o),a&&a.hMi(r)&&(u+=21*i),u},K=l,Z.CellTypes.ti[99]=K,n(xt,rt=e),xt.prototype.getHitInfo=function(t,e,n,i,o){return this.Tno&&this.Tno.getHitInfo?this.Tno.getHitInfo(t,e,n,i,o):rt.prototype.getHitInfo.call(this,t,e,n,i,o)},xt.prototype.paintValue=function(t,e,n,i,o,u,r,a){var c,s,l,l,f,c,d,s,h,g;this.Tno||(c=a.sheet,s=a.isAfterRowSpacing,a.dataRange&&(l=J(a))&&(c=l.sheet,s=l.isAfterRowSpacing),l=a.sheet.zoom(),c=c.NMi,d=-1,!s&&c.lMi&&(d=c.lMi.length-1),rt.prototype.paintValue.call(this,t,e,h=n+(s=(f=21*l)*(d+2)),i,g=o-s,u,r,a))},S=xt,Z.CellTypes.ti[108]=S,p=Z.CellTypes.Text,n(f,p),f.prototype.getValueInOneCell=function(t){var e,n,i,o,t,u,e,n,e=t.sheet,n=t.row,i=t.col,o=t.rowSpacingIndex,t=t.isAfterRowSpacing;if(W(this.zeo))return u=e.getValue(n,i,Z.SheetArea.viewport,void 0,{rowSpacingIndex:o,isAfterRowSpacing:t}),n=(e=e.ncr&&e.ncr(n,o,i,t))&&e.caption,Ct("","",n="".concat(n).concat((0,G.getSR)().TextColon),u)},f.prototype.paint=function(t,e,n,i,o,u,r,a){var c,s,l,f,d,h,g,g,b,v,M,M,s,d,l,h,I,m,N,D,f,j,C,w,w,D,f,c=this,s=a.row,l=a.col,f=a.sheet,d=a.rowSpacingIndex,h=a.isAfterRowSpacing;a.dataRange&&(g=J(a))&&(f=g.sheet,s=g.row,l=g.col,d=g.rowSpacingIndex,h=g.isAfterRowSpacing),g=a.sheet.zoom(),v=(b=f.NMi).currentTheme(),M=r.backColor,a.parentBackColor!==M&&(0,Z.cellTypeContext_paintBackground)(t,n,i,o,u,a.imageLoader,r,e,a),r.backColor=void 0,r.backgroundImage=void 0,M=new Z.Rect(n,i,o,u),d=(s=f.ncr&&f.ncr(s,d,l,h))&&s.caption,l=V(r),h=f.getDefaultStyle(),(m=(I=s&&s.headerStyle)?F(I,v,h):l.clone()).hAlign=_(m,Z.HorizontalAlign.right),(N=H(this.zeo))?(D=2*g,f=f.getDefaultStyle().cellPadding+" 0 0 0",(C=(j=s&&s.style)?F(j,v,h):l.clone()).cellPadding=f,C.hAlign=_(C,Z.HorizontalAlign.right),a.fontInfo=Z.W.x2e(C,j?g:1),C.font=a.fontInfo.font,a.cellStyle=C,p.prototype.paint.call(this,t,e,n,i,o,u,C,a),w=a.ysr,d&&(d="".concat(d).concat((0,G.getSR)().TextColon),w=o-w-D,m.cellPadding=f,m.hAlign=_(m,Z.HorizontalAlign.right),a.fontInfo=Z.W.x2e(m,I?g:1),m.font=a.fontInfo.font,a.cellStyle=m,p.prototype.paint.call(this,t,d,n,i,w,u,m,a))):(D=c.An(M,r,(0,Z.isPrintZoomFactorExist)(a)?a.printZoomFactor:a.sheet.zoom()),f=new Z.Rect(n,i,o,u-D.height),d&&(I&&!T(I.cellPadding)||(m.cellPadding=B),a.cellStyle=m,a.fontInfo=Z.W.x2e(m,I?g:1),m.font=a.fontInfo.font,a.lineHeight=Z.mt.vt(a.fontInfo.font,!1,Z.mt.pt(d)),p.prototype.paint.call(this,t,d,n,f.y,o,f.height,m,a)),(C=(j=s&&s.style)?F(j,v,h):l.clone()).cellPadding=B,C.hAlign=_(C,Z.HorizontalAlign.right),a.cellStyle=C,a.fontInfo=Z.W.x2e(C,j?g:1),C.font=a.fontInfo.font,a.lineHeight=Z.mt.vt(a.fontInfo.font,!1,Z.mt.pt(e)),p.prototype.paint.call(this,t,e,n,D.y,o,D.height,C,a))},f.prototype.Ln=function(t,e,n,i,o){var u,r,a,c,s,l,f,d,f,r,s,a,l,h,n,g,b,v,M,n,u=0,r=o.row,a=o.col,c=o.sheet,s=o.rowSpacingIndex,l=o.isAfterRowSpacing;return o.dataRange&&(f=J(o))&&(c=f.sheet,r=f.row,a=f.col,s=f.rowSpacingIndex,l=f.isAfterRowSpacing),f=(d=c.NMi).currentTheme(),s=(s=(r=c.ncr&&c.ncr(r,s,a,l))&&r.caption)&&"".concat(s).concat((0,G.getSR)().TextColon),a=V(n),l=c.getDefaultStyle(),H(this.zeo)?(h=2,n=c.getDefaultStyle().cellPadding+" 0 0 0",T(t)||((b=(g=r&&r.style)?F(g,f,l):a.clone()).cellPadding=n,b.hAlign=_(b,Z.HorizontalAlign.right),o.fontInfo=Z.W.x2e(b,g?i:1),b.font=o.fontInfo.font,o.cellStyle=b,u+=p.prototype.Ln.call(this,t,(0,Z.C0r)(this,t,b,c),b,i,o)+2),T(s)||((M=(v=r&&r.headerStyle)?F(v,f,l):a.clone()).cellPadding=n,M.hAlign=_(M,Z.HorizontalAlign.right),o.fontInfo=Z.W.x2e(M,v?i:1),M.font=o.fontInfo.font,o.cellStyle=M,u+=p.prototype.Ln.call(this,s,s,M,i,o)+2)):(T(t)||((b=(g=r&&r.style)?F(g,f,l):a.clone()).cellPadding=B,b.hAlign=_(b,Z.HorizontalAlign.right),o.cellStyle=b,o.fontInfo=Z.W.x2e(b,g?i:1),b.font=o.fontInfo.font,u=p.prototype.Ln.call(this,t,(0,Z.C0r)(this,t,b,c),b,i,o)),T(s)||(M=(v=r&&r.headerStyle)?F(v,f,l):a.clone(),v&&!T(v.cellPadding)||(M.cellPadding=B),o.cellStyle=M,o.fontInfo=Z.W.x2e(M,v?i:1),M.font=o.fontInfo.font,u<(n=p.prototype.Ln.call(this,s,s,M,i,o))&&(u=n))),u},q=f,Z.CellTypes.ti[109]=q,n(d,Q=M),d.prototype.getValueInOneCell=function(t){var e,n,i,o,t,u,r,a,c,s,l,c,s,f,d,a,u,c,a,f,u,e=t.sheet,n=t.row,i=t.col,o=t.rowSpacingIndex,t=t.isAfterRowSpacing;if(W(this.zeo))return u=e.getActualStyle(n,i,Z.SheetArea.viewport,void 0,{rowSpacingIndex:o,isAfterRowSpacing:t}),r=e.getValue(n,i,Z.SheetArea.viewport,void 0,{rowSpacingIndex:o,isAfterRowSpacing:t}),a=e.NMi,s=(c=e.ncr&&e.ncr(n,o,-1)).field,l=c.value,c=c.groupLevelOptions,s="".concat(s).concat((0,G.getSR)().TextColon),l=Z.mt.V0(l),d=(f=e.getDefaultStyle()).cellPadding+" 0 0 0",a=a.currentTheme(),u=V(u),(c=c.style?F(c.style,a,f):u.clone()).cellPadding=d,c.hAlign=_(c,Z.HorizontalAlign.left),a=(0,Z.C0r)(this,l,c,e),u=(f=e.ncr&&e.ncr(n,o,i,t))&&f.caption,Ct(s,a,u="".concat(u).concat((0,G.getSR)().TextColon),r)},d.prototype.paint=function(t,e,n,i,o,u,r,a){var c,s,l,f,d,h,g,g,b,v,M,I,I,m,N,D,j,C,w,p,x,m,b,y,b,g,b,C,A,T,S,b,z,A,D,b,A,S,z,g,C,T,j,N,w,b,L,s,d,f,h,O,k,h,f,l,O,E,h,f,l,h,s,d,f,r,p,c=this,s=a.row,l=a.sheet,f=a.col,d=a.rowSpacingIndex,h=a.isAfterRowSpacing;if(a.dataRange&&(g=J(a))&&(l=g.sheet,s=g.row,f=g.col,d=g.rowSpacingIndex,h=g.isAfterRowSpacing),g=l.NMi,b=l.getDataSource(),v=a.sheet.zoom(),M=g.currentTheme(),I=r.backColor,a.parentBackColor!==I&&(0,Z.cellTypeContext_paintBackground)(t,n,i,o,u,a.imageLoader,r,e,a),r.backColor=void 0,r.backgroundImage=void 0,I=new Z.Rect(n,i,o,u),m=l.ncr&&l.ncr(s,d,-1)){if(N=21*v,D=16*v,j=4*v,C=g.keo(),w=m.level,p=m.field,x=m.value,m=m.groupLevelOptions,x=Z.mt.V0(x),g&&b&&g.lMi&&0<g.lMi.length&&((b=g.groupOutlinePosition())===U.GroupOutlinePosition.groupCell||b===U.GroupOutlinePosition.groupCellAll)&&!(y=w===g.lMi.length-1&&!g.detailColumnsVisible())&&g.DMi(w,s)){if(b=P*v,!(g=g.IMi(s,w)))return;t.save(),t.beginPath(),t.rect(n,i,o,u),t.clip(),t.beginPath(),t.strokeStyle="#999999",t.lineWidth=1,b=n+b,W(C)&&(b+=N*w),C=b+D/2,T=(A=i+(u-D)/2)+D/2,S=R(b)-.5,b=R(b+D)-.5,z=R(A)-.5,A=R(A+D)-.5,t.moveTo(S,z),t.lineTo(b,z),t.lineTo(b,A),t.lineTo(S,A),t.lineTo(S,z),t.moveTo(R(C-j)-.5,R(T)-.5),t.lineTo(R(C+j)-.5,R(T)-.5),g.collapsed&&(t.moveTo(R(C)-.5,R(T-j)-.5),t.lineTo(R(C)-.5,R(T+j)-.5)),t.stroke(),t.restore()}D=0,A=n+(b=N*(w+1)),S=o-b,z=4*v,g=V(r),C=l.getDefaultStyle(),T=l.defaults.rowHeight,N=(j=l.ncr&&l.ncr(s,d,f,h))&&j.caption,(b=(w=j&&j.headerStyle)?F(w,M,C):g.clone()).hAlign=Z.HorizontalAlign.right,(L=H(this.zeo))?(s=2*v,d=l.getDefaultStyle().cellPadding+" 0 0 0",(f=m.headerStyle?F(m.headerStyle,M,C):g.clone()).cellPadding=d,f.hAlign=_(f,Z.HorizontalAlign.left),a.fontInfo=Z.W.x2e(f,m.headerStyle?v:1),f.font=a.fontInfo.font,a.cellStyle=f,h="".concat(p).concat((0,G.getSR)().TextColon),O=X(Z.mt.Mt(t,f.font,(0,Z.y0r)(this,t,h,S,u,f,a))+s),Q.prototype.paint.call(this,t,h,A,i,O,u,f,a),A+=O,(k=m.style?F(m.style,M,C):g.clone()).cellPadding=d,k.hAlign=_(k,Z.HorizontalAlign.left),a.fontInfo=Z.W.x2e(k,m.style?v:1),k.font=a.fontInfo.font,a.cellStyle=k,h=(0,Z.C0r)(this,x,k,l),f=X(Z.mt.Mt(t,k.font,(0,Z.y0r)(this,t,h,S,u,k,a))+s),k.formatter=B,Q.prototype.paint.call(this,t,h,A,i,f,u,k,a),A=A+f+z,l=S-O-f-z,(E=(O=j&&j.style)?F(O,M,C):g.clone()).cellPadding=d,E.hAlign=_(E,Z.HorizontalAlign.right),a.fontInfo=Z.W.x2e(E,O?v:1),E.font=a.fontInfo.font,a.cellStyle=E,Q.prototype.paint.call(this,t,e,A,i,l,u,E,a),h=a.ysr,N&&(N="".concat(N).concat((0,G.getSR)().TextColon),f=l-h-s,b.cellPadding=d,b.hAlign=_(b,Z.HorizontalAlign.right),a.fontInfo=Z.W.x2e(b,w?v:1),b.font=a.fontInfo.font,a.cellStyle=b,Q.prototype.paint.call(this,t,N,A,i,f,u,b,a))):(l=c.An(I,r,(0,Z.isPrintZoomFactorExist)(a)?a.printZoomFactor:a.sheet.zoom()),h=new Z.Rect(n,i,o,u-l.height),(k=m.style?F(m.style,M,C):g.clone()).hAlign=_(k,Z.HorizontalAlign.left),s=m.headerStyle?F(m.headerStyle,M,C):g.clone(),(!m.headerStyle||m.headerStyle&&Y(m.headerStyle.cellPadding))&&(s.cellPadding=B),s.hAlign=_(s,Z.HorizontalAlign.left),a.cellStyle=s,a.fontInfo=Z.W.x2e(s,m.headerStyle?v:1),s.font=a.fontInfo.font,a.lineHeight=Z.mt.vt(a.fontInfo.font,!1,Z.mt.pt(p)),Q.prototype.paint.call(this,t,p,A,h.y,S,h.height,s,a),D=at(0,a.ysr),k.cellPadding=B,a.cellStyle=k,a.fontInfo=Z.W.x2e(k,m.style?v:1),k.font=a.fontInfo.font,a.lineHeight=Z.mt.vt(a.fontInfo.font,!1,Z.mt.pt(x)),Q.prototype.paint.call(this,t,x,A,l.y,S,l.height,k,a),d=A+(D=at(D,a.ysr))+z,f=S-D-z,E=(O=j&&j.style)?F(O,M,C):g.clone(),O&&!Y(O.cellPadding)||(E.cellPadding=T+" 0 0 0"),r=c.An(I,E,(0,Z.isPrintZoomFactorExist)(a)?a.printZoomFactor:a.sheet.zoom()),N&&(p=new Z.Rect(n,i,o,u-r.height),w&&!Y(w.cellPadding)||(b.cellPadding=B),a.cellStyle=b,a.fontInfo=Z.W.x2e(b,j&&j.headerStyle?v:1),b.font=a.fontInfo.font,a.lineHeight=Z.mt.vt(a.fontInfo.font,!1,Z.mt.pt(N)),Q.prototype.paint.call(this,t,N,d,p.y,f,p.height,b,a)),E.cellPadding=B,E.hAlign=Z.HorizontalAlign.right,a.cellStyle=E,a.fontInfo=Z.W.x2e(E,O?v:1),E.font=a.fontInfo.font,a.lineHeight=Z.mt.vt(a.fontInfo.font,!1,Z.mt.pt(e)),Q.prototype.paint.call(this,t,e,d,r.y,f,r.height,E,a))}},d.prototype.Ln=function(t,e,n,i,o){var u,r,a,c,s,l,f,d,f,h,g,b,n,v,M,I,m,g,N,M,r,D,j,s,c,C,l,w,p,x,y,b,c,l,u=0,r=o.row,a=o.sheet,c=o.col,s=o.rowSpacingIndex,l=o.isAfterRowSpacing;return o.dataRange&&(f=J(o))&&(a=f.sheet,r=f.row,c=f.col,s=f.rowSpacingIndex,l=f.isAfterRowSpacing),f=(d=a.NMi).currentTheme(),h=21,g=a.ncr&&a.ncr(r,s,-1),b=H(this.zeo),g&&(n=V(n),v=a.getDefaultStyle(),M=g.level,I=g.field,m=g.value,g=g.groupLevelOptions,u=N=21*(M+1),r=(M=a.ncr&&a.ncr(r,s,c,l))&&M.caption,D=4,b?(r=r&&"".concat(r).concat((0,G.getSR)().TextColon),I=I&&"".concat(I).concat((0,G.getSR)().TextColon),j=2,s=a.getDefaultStyle().cellPadding+" 0 0 0",T(I)||((c=g.headerStyle?F(g.headerStyle,f,v):n.clone()).cellPadding=s,c.hAlign=_(c,Z.HorizontalAlign.left),o.fontInfo=Z.W.x2e(c,g.headerStyle?i:1),c.font=o.fontInfo.font,o.cellStyle=c,u+=Q.prototype.Ln.call(this,I,I,c,i,o)+2),T(m)||((C=g.style?F(g.style,f,v):n.clone()).cellPadding=s,C.hAlign=_(C,Z.HorizontalAlign.left),o.fontInfo=Z.W.x2e(C,g.style?i:1),C.font=o.fontInfo.font,o.cellStyle=C,l=(0,Z.C0r)(this,m,C,a),u+=Q.prototype.Ln.call(this,l,l,C,i,o)+2),M&&(u+=4,T(t)||((p=(w=M.style)?F(w,f,v):n.clone()).cellPadding=s,p.hAlign=_(p,Z.HorizontalAlign.right),o.fontInfo=Z.W.x2e(p,w?i:1),p.font=o.fontInfo.font,o.cellStyle=p,u+=Q.prototype.Ln.call(this,t,(0,Z.C0r)(this,t,p,a),p,i,o)+2),T(r)||((y=(x=M.headerStyle)?F(x,f,v):n.clone()).cellPadding=s,y.hAlign=_(y,Z.HorizontalAlign.right),o.fontInfo=Z.W.x2e(y,x?i:1),y.font=o.fontInfo.font,o.cellStyle=y,u+=Q.prototype.Ln.call(this,r,r,y,i,o)+2))):(b=0,T(I)||(c=g.headerStyle?F(g.headerStyle,f,v):n.clone(),(!g.headerStyle||g.headerStyle&&Y(g.headerStyle.cellPadding))&&(c.cellPadding=B),c.hAlign=_(c,Z.HorizontalAlign.left),o.cellStyle=c,o.fontInfo=Z.W.x2e(c,g.headerStyle?i:1),c.font=o.fontInfo.font,b=A(Q.prototype.Ln.call(this,I,I,c,i,o),b)),T(m)||((C=g.style?F(g.style,f,v):n.clone()).hAlign=_(C,Z.HorizontalAlign.left),C.cellPadding=B,o.cellStyle=C,o.fontInfo=Z.W.x2e(C,g.style?i:1),C.font=o.fontInfo.font,b=A(Q.prototype.Ln.call(this,m,(0,Z.C0r)(this,m,C,a),C,i,o),b)),u+=b,l=0,T(r)||(y=(x=M&&M.headerStyle)?F(x,f,v):n.clone(),x&&!Y(x.cellPadding)||(y.cellPadding=B),o.cellStyle=y,o.fontInfo=Z.W.x2e(y,M&&M.headerStyle?i:1),y.font=o.fontInfo.font,l=A(Q.prototype.Ln.call(this,r,r,y,i,o),l)),T(t)||((p=(w=M.style)?F(w,f,v):n.clone()).cellPadding=B,p.hAlign=Z.HorizontalAlign.right,o.cellStyle=p,o.fontInfo=Z.W.x2e(p,w?i:1),p.font=o.fontInfo.font,l=A(Q.prototype.Ln.call(this,t,(0,Z.C0r)(this,t,p,a),p,i,o),l)),u+=l,0<l&&(u+=4))),u},d.prototype.getCellAndPaddingHitInfo=function(t,e,n,i,o){var u,t,u=n.cellPadding;return n.cellPadding=void 0,t=Q.prototype.getCellAndPaddingHitInfo.call(this,t,e,n,i,o),n.cellPadding=u,t},d.prototype.fMi=function(t,e,n,i,o,u,r,a){var a,c,s,l,l,l,r,i,f,l,a,s,l,i,r,t,s,a=(a?a.sheet:i).zoom(),c=16*a,s=i.NMi,l=i.getDataSource();if(s&&l&&((l=s.groupOutlinePosition())===U.GroupOutlinePosition.groupCell||l===U.GroupOutlinePosition.groupCellAll)&&(l=i.ncr&&i.ncr(o,r,-1))&&(i=21*a,!(f=(r=l.level)===s.lMi.length-1&&!s.detailColumnsVisible()))&&s.DMi(r,o)&&(l=P*a,a=s.IMi(o,r)))return s=t.x+l+i*r,l=t.y+(t.height-c)/2,i=R(s)-.5,r=R(s+c)-.5,t=R(l)-.5,s=R(l+c)-.5,i<=e&&e<=r&&t<=n&&n<=s?{level:a.level,index:o,collapsed:a.collapsed}:void 0},Z.CellTypes.ti[110]=$=d,g.prototype.Hc=function(t,e,n){var i,i,i=new Z.Style;i.backColor="white",i.cellType=new x,t.setStyle(-1,0,i,Z.SheetArea.rowHeader),t.setColumnWidth(0,40*e,Z.SheetArea.rowHeader),(i=this.tMi)!==U.GroupOutlinePosition.rowHeader&&i!==U.GroupOutlinePosition.groupCellAll||t.setColumnVisible(0,!0,Z.SheetArea.rowHeader),this.Beo(t,n)},g.prototype.Beo=function(t,e){var n;W(this.GY)?(this.Geo(t),this.Reo(t),this.Peo(t,e)):this.Qeo(t,e)},g.prototype.Qeo=function(t,e){for(var n,i,i,o,i,i,u,n=0;n<e.length;n++)e[n].DFt&&(i=(i=e[n].headerStyle&&e[n].headerStyle.cellType)&&("98"===i.typeName?B:(0,Z.createCellType)(i)),i=new E(i),t.setCellType(-1,n,i,Z.SheetArea.colHeader),(o=(o=e[n].headerStyle)||(e[n].headerStyle={})).cellType=i,i=(i=e[n].style&&e[n].style.cellType)&&("99"===i.typeName?B:(0,Z.createCellType)(i)),i=new K(i),t.setCellType(-1,n,i),(u=(u=e[n].style)||(e[n].style={})).cellType=i)},g.prototype.Peo=function(t,e){var n,i,o,u,r,a,c,s,l,f,d,h,g,b,v,M,I,m,v,N,D,j,j,C,n,w,p,n,n,x,y,y,y,i=t.NMi,o=t.getRowCount(),u=e.length,r=t.k9(Z.SheetArea.viewport),a=t.NMi.currentTheme();if(!T(r)){if(c=t.defaults.rowHeight,s=t.Csr)for(l=0;l<o;l++){for(f=t.Rsr(l),d=0;d<f;d++){for(g=null==(h=t.ncr(l,d,-1))?void 0:h.field,b=0;b<u;b++)(I=new(M=(v=b===r)?$:q)).zeo=this.GY,(v=(v=st(m=v&&h&&h.groupLevelOptions?h.groupLevelOptions:t.ncr(l,d,b)))||new Z.Style).cellType=I,t.tcr(l,d,b,v);h&&(D=Y((N=F(h.groupLevelOptions&&h.groupLevelOptions.style||{},a)).cellPadding),L(this.GY)&&D&&(N.cellPadding=c+" 0 0 0"),t.tcr(l,d,-1,N)),(j=new Z.Style).backColor="white",j.cellType=new k,t.tcr(l,d,0,j,!1,Z.SheetArea.rowHeader),L(this.GY)&&(j=i.getGroupingHeaderHeight(g,c),t.qsr(l,d,j))}for(C=t.Rsr(l,!0),d=0;d<C;d++)if(g=null==(n=null==(h=t.ncr(l,d,-1,!0))?void 0:h.groupLevelOptions)?void 0:n.field,h&&h.isSpacing)(n=new Z.Style).backColor="white",n.cellType=new k,t.tcr(l,d,0,n,!0,Z.SheetArea.rowHeader),t.qsr(l,d,h.spacingRow,!0);else{for(b=0;b<u;b++)(w=new q).zeo=this.GY,w.Leo=!0,(p=(p=st(m=t.ncr(l,d,b,!0)))||new Z.Style).cellType=w,t.tcr(l,d,b,p,!0);h&&(D=Y((N=F(h.groupLevelOptions&&h.groupLevelOptions.style||{},a)).cellPadding),L(this.GY)&&D&&(N.cellPadding=c+" 0 0 0"),t.tcr(l,d,-1,N,!0)),(n=new Z.Style).backColor="white",n.cellType=new k,t.tcr(l,d,0,n,!0,Z.SheetArea.rowHeader),L(this.GY)&&(n=i.getGroupingFooterHeight(g,c),t.qsr(l,d,n,!0))}}for(l=0,x=u;l<x;l++)t.getCellType(-1,l)instanceof S&&t.setCellType(-1,l,B);y=(y=(y=e[r].style)&&y.cellType)&&("108"===y.typeName?B:(0,Z.createCellType)(y)),y=new S(y),t.setCellType(-1,r,y),e[r].zno=y}},g.prototype.Geo=function(t,e,n,i,o,u){var r,a,c,s,l,f,d,h,g,b,v,v,g,M,M,v,b,M;if(t.Csr&&(a=(r=t.NMi).getDataView(),c=this.Yeo(),s=r.groupBy(),l=W(c),T(e)&&(e=a.FFt.childrenArray[0]),T(n)&&(n=r.INi.Weo()),e.length===n.length)&&(T(i)&&(i=0),T(o)&&(o=0),T(u)&&(u=0),e)&&0<e.length)for(f=0;f<e.length;f++)d=e[f],h=n[f],g=d.recordRowNumbers,b=d.value,v=(v=d.cellNode).field.extra&&!T(v.field.extra.caption)?v.field.extra.caption:v.field.fieldValue,Array.isArray(v)&&(v=(0,G.tIi)(v)),g=g.length,M=t.Rsr(i),t.Ksr(i,M,1),t.ecr(i,o,-1,{groupLevelOptions:s[h.level],level:h.level,field:v,value:Z.mt.Z0(b)}),h.spacingIndex=o,l&&(M=d.summaryResults)&&(v=vt(M,"header",r.Heo()))&&this.Veo(t,i,o,v),b=d.childrenArray[0],M=h.children,b&&0<b.length&&this.Geo(t,b,M,i,o+1,u),i+=g,o=0},g.prototype.Reo=function(t,e,n,i,o){var u,r,a,c,s,l,f,d,f,h,g,g,d,h;if(t.Csr&&(r=(u=t.NMi).getDataView(),T(e)&&(e=r.FFt.childrenArray[0]),T(n)&&(n=u.INi.Weo()),e.length===n.length)&&(T(i)&&(i=0),T(o)&&(o=0),e)&&0<e.length)for(a=u.groupBy(),c=0;c<e.length;c++)s=e[c],l=n[c],f=s.recordRowNumbers,d=s.summaryResults,h=i+(f=f.length)-1,0<(g=Dt(s))&&c<e.length-1&&(t.Ksr(h,0,1,!0),t.ecr(h,0,-1,{level:l.level,spacingRow:g,isSpacing:!0},!0)),d&&(g=vt(d,"footer"))&&(t.Ksr(h,0,1,!0),t.ecr(h,0,-1,{groupLevelOptions:a[l.level],level:l.level},!0),this._eo(t,h,0,g)),d=s.childrenArray[0],h=l.children,d&&0<d.length&&this.Reo(t,d,h,i,o),i+=f},g.prototype.Veo=function(t,e,n,i){var o,u,r,a,c,s;if(t.Csr)for(u=(o=t.NMi.getDataView()).getColumnInfos(),r=0;r<u.length;r++)(s=i[c=(a=u[r]).value])&&(t.$sr(e,n,r,s.value),t.ecr(e,n,r,s))},g.prototype._eo=function(t,e,n,i){var o,u,r,a,c,s;if(t.Csr)for(u=(o=t.NMi.getDataView()).getColumnInfos(),r=0;r<u.length;r++)(s=i[c=(a=u[r]).value])&&(t.$sr(e,n,r,s.value,!0),t.ecr(e,n,r,s,!0))},g.prototype.TMi=function(t,e){var n,n;if(0===arguments.length)return this.tMi;this.tMi=e,(n=t.NMi)&&n.lMi&&(n=e===U.GroupOutlinePosition.rowHeader||e===U.GroupOutlinePosition.groupCellAll,t.setColumnVisible(0,n,Z.SheetArea.rowHeader))},g.prototype.Yeo=function(t){if(T(t))return this.GY;this.GY=t},g.prototype.Jeo=function(t){if(T(t))return this.Oeo;this.Oeo=u(u({},this.Oeo),t)},g.prototype.L0=function(t){t.setColumnVisible(0,!1,Z.SheetArea.rowHeader);for(var e=0,n=t.getColumnCount();e<n;e++)t.getCellType(-1,e,Z.SheetArea.colHeader)instanceof E&&t.setCellType(-1,e,B,Z.SheetArea.colHeader),t.getCellType(-1,e)instanceof K&&t.setCellType(-1,e,B),t.getCellType(-1,e)instanceof S&&t.setCellType(-1,e,B)},g.prototype.toJSON=function(){return this.tMi},g.prototype.fromJSON=function(t,e){this.TMi(t,e)},tt=g,b.prototype.Weo=function(){return this._data.oMi.children},b.prototype.Hc=function(t,e,n,i,o){o||this._data.Hc(t,e);var o=n.getDataSource();this._ui.Hc(n,i.length,o&&o.getColumnInfos())},b.prototype.TMi=function(t,e){if(0===arguments.length)return this._ui.TMi();this._ui.TMi(t,e)},b.prototype.uMi=function(t,e,n,i){this._data.uMi(t,e,n,i)},b.prototype.aMi=function(t,e,n,i){this._data.aMi(t,e,n,i)},b.prototype.sMi=function(t,e,n,i,o){return this._data.sMi(t,e,n,i,o)},b.prototype.MMi=function(t){return this._data.MMi(t)},b.prototype.IMi=function(t,e){return this._data.IMi(t,e)},b.prototype.gMi=function(t){return this._data.gMi(t)},b.prototype.Yeo=function(t){return this._ui.Yeo(t)},b.prototype.Jeo=function(t){return this._ui.Jeo(t)},b.prototype.L0=function(t){this._data.L0(),this._ui.L0(t)},b.prototype.Beo=function(t,e){this._ui.Beo(t,e)},b.prototype.toJSON=function(){return{outlinePosition:this._ui.toJSON(),tree:this._data.toJSON()}},b.prototype.fromJSON=function(t,e,n){this._ui.fromJSON(t,e.outlinePosition),this._data.fromJSON(e.tree,n)},t.Grouping=b},"./dist/plugins/tableSheet/tableSheet-panel/tableSheetPanel.js":function(i,M,t){var L,O,k,R,o,P,Q,Y,E,W,H,a,V,u,n,g,Z,N,r,F,_,J,c,f,d,h,X,v,K,q,I,$,tt,et,nt,s,l,C,w,b,it,ot,m,D,j,ut,rt,p,at,x,ct,y,A,st,lt,ft,dt,T,S,ht,gt,bt,z,vt,Mt,It,mt,Nt,Dt,t;function jt(t,e){for(var n,i,n=0;n<t.length;n++)if(e(i=t[n]))return n;return-1}function U(t){return t.value||t}function G(t){return B(t)&&t.bMi}function Ct(t){return t.yBt}function B(t){return 1===t.textIndent}function wt(t){return 2<=t.textIndent}function pt(t){return N(t)||""===t}function xt(t){return t&&t.richText&&t.richText[0]&&t.richText[0].text||t&&t.text||t}function e(t,e,n,i){this.AMi=!0,this.Zc=t,this.vMi=e,this.jMi=0,this.Ti=n,this.options=k.util.b0(Nt,this.zMi.bind(this)),this.Zp(i||{}),this.CMi=[],this.wMi=-1,n&&(0,k.GC$)(n).data(u,this),this.Ti.innerHTML=this.yMi(),this.mMi=n.querySelector(".".concat(_)),this.LMi=n.querySelector(".".concat(F)),this.xMi=n.querySelector(".".concat(f)),this.SMi=n.querySelector(".".concat(c)),this.ZMi=n.querySelector(".".concat(x)),this.OMi=this.ZMi.querySelector(".".concat(y)),this.EMi=n.querySelector(".".concat(c,".").concat(J,".").concat(at)),this.GMi=n.querySelector(".".concat(s)),this.pMi=n.querySelector(".".concat(rt)),this.attach(e),this.kMi(),(this.vMi.UMi=this).AMi=!1}function yt(t){return JSON.parse(JSON.stringify(t))}L=this&&this.__assign||function(){return(L=Object.assign||function(t){for(var e,n,i,o,n=1,i=arguments.length;n<i;n++)for(o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},Object.defineProperty(M,"__esModule",{value:!0}),M.TableSheetPanel=M.ShowSourceOptions=M.ARROW_RIGHT=M.ARROW_DOWN=void 0,O=t("./dist/plugins/tableSheet/tableSheet.js"),k=t("Core"),R=t("./dist/plugins/tableSheet/tableSheet.interface.js"),o=t("Common"),P=t("FormulaTextBox"),Q=t("CalcEngine"),Y=t("CellTypes"),E=t("./dist/plugins/tableSheet/tablesheet-command.js"),W=t("./dist/plugins/tableSheet/tableSheet-grouping.js"),H=k.CellTypes.Base,a=o.Common.zf.j0,V=o.Common.lt.tv,n="."+(u="table-sheet-panel"),g=null,Z=void 0,N=o.Common.lt.ht,r=window,F="gc-table-sheet-panel",_="gc-table-sheet-field-panel",J="gc-table-sheet-cross-panel",c="gc-table-sheet-group-panel",f="gc-table-sheet-group-list",d="gc-table-sheet-group-input",X=(h="gc-table-sheet-group-item")+"-remove",v="group-index",K="group-name",q="group-field",I="summary-index",$="gc-table-sheet-summary-item",tt="summary-name",et="summary-formula",nt=h+"-label",s="gc-table-sheet-summary-container",l="gc-table-sheet-field-container",C="gc-table-sheet-summary-item-label",w="gc-table-sheet-summary-item-input-formula",b="gc-table-sheet-summary-item-input-slice",it="gc-table-sheet-summary-function",ot="gc-table-sheet-summary-item-input-caption",m="gc-table-sheet-summary-list-container",D="gc-table-sheet-group-item-reorder-top",j="gc-table-sheet-group-item-reorder-bottom",ut="gc-table-sheet-add-calculate-column",rt="fake-ftb-workbook",p="gc-table-sheet-summary-item-caption",at="gc-table-sheet-cross-column",x="gc-table-sheet-cross-column-detail",ct="gc-table-sheet-cross-column-detail-value",y="gc-table-sheet-cross-column-attributes-list",A="gc-table-sheet-cross-column-item",st="gc-table-sheet-cross-column-item-checkbox",lt="gc-table-sheet-cross-column-item-edit",ft="gc-table-sheet-cross-column-item-input-filter",dt="gc-table-sheet-cross-column-item-editing",T="gc-table-sheet-cross-detail-input",S="gc-table-sheet-hidden",ht="gc-table-sheet-summary-item-input-relateTo",gt="gc-table-sheet-summary-item-input-position",bt="gc-table-sheet-group-item-setting",z="attribute-index",M.ARROW_DOWN="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAHJJREFUOE+10i0OwkAQBtC394ED4PE4EA0HBIeurEX2QE2TDYKwOyTTrt3Myzc/RfKVZL1dgQPmKGErwRFP3PHuIb0WTnhgwNRCohmca5Ibxl9IBKw1l4pc8fpGdgdSLaSGuMkaU4cUHeDn/58tdLE0sABwBBIR30jsewAAAABJRU5ErkJggg==",M.ARROW_RIGHT="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAI1JREFUOE/t0rEJwlAQBuAv09hJagewsBQMFuIqDuEA9tpa2qbMAA7gAG4gB08IjySvSCe+9riPe/9dZearZvb7UWCB50g2O9z6tTyDE7bY4JUh0XxF00eGQgxknZB3QqL5gj3uUxN8a4GsEhITnXHAI//a1BoDWaLGEe1QLqU7CCRG7sYOrgQUD/UP8AFFTRERJMaNUQAAAABJRU5ErkJggg==",vt=function(t,e){return t?e:""},Mt=function(t){var e=t.formula,n=t.caption,n=void 0===n?"":n,i=t.sliceField,o=t.columnList,u=t.relateTo,r=t.position,t=t.showSlice;return'\n    <div class="gc-table-sheet-summary-item-formula">\n        <span class="'.concat(C,'">').concat((0,O.getSR)().GroupPanelSummaryLabelFormula,'</span>\n        <input class="').concat(w,'" input-type="formula" spellcheck="false" tabIndex="-1" current-data=\'').concat(a(e),"' value='").concat(a(e),'\'></input>\n        <span class="gc-table-sheet-summary-item-remove-icon" title="').concat((0,O.getSR)().GroupPanelItemRemove,'"></span>\n    </div>\n    <div class="').concat(p,'">\n        <span class="').concat(C,'">').concat((0,O.getSR)().GroupPanelSummaryLabelCaption,'</span>\n        <input class="').concat(ot,'" input-type="caption" spellcheck="false" tabIndex="-1" current-data=\'').concat(a(n),"' value='").concat(a(n),"'></input>\n    </div>")+vt(t,'\n        <div class="gc-table-sheet-summary-item-slice">\n        <span class="'.concat(C,'">').concat((0,O.getSR)().GroupPanelSummaryLabelSlice,'</span>\n        <input class="').concat(b,'" input-type="slice" spellcheck="false" tabIndex="-1" current-data=\'').concat(i,"' value='").concat(i,"'></input>\n    </div>"))+vt(!t,'\n        <div class="gc-table-sheet-summary-item-relateTo">\n        <span class="'.concat(C,'">').concat((0,O.getSR)().GroupPanelSummaryLabelRelateTo,'</span>\n        <select class="').concat(ht,' gc-table-sheet-summary-item-select" name="relateTo">').concat('<option value="" style="display: none"></option>'+o.map(function(t){return'<option value="'.concat(t.value,'" ').concat(t.value===u?"selected":"",">").concat(t.text,"</option>")}),'</select>\n    </div>\n    <div class="gc-table-sheet-summary-item-position">\n        <span class="').concat(C,'">').concat((0,O.getSR)().GroupPanelSummaryLabelPosition,'</span>\n        <select class="').concat(gt,' gc-table-sheet-summary-item-select" name="position">\n          <option value="header" ').concat("header"===r?"selected":"",">").concat((0,O.getSR)().GroupPanelSummaryLabelPositionHeader,'</option>\n          <option value="footer" ').concat("footer"===r?"selected":"",">").concat((0,O.getSR)().GroupPanelSummaryLabelPositionFooter,"</option>\n        </select>\n    </div>"))},It=function(t){var e,n,i,o,u,e,n,i,o,u;return{formulaInputValue:t.find(".".concat(w)).val(),captionInputValue:t.find(".".concat(ot)).val(),sliceInputValue:t.find(".".concat(b)).val(),relateToValue:t.find(".".concat(ht)).val(),positionValue:t.find(".".concat(gt)).val()}},(t=mt=M.ShowSourceOptions||(M.ShowSourceOptions={}))[t.none=0]="none",t[t.field=1]="field",t[t.formula=2]="formula",t[t.all=3]="all",Nt={showSource:mt.none,fieldAreaVisible:!0,crossAreaVisible:!1,groupAreaVisible:!0,showEditButton:!1,showViewName:!0,showGroupSetting:void 0},e.prototype.ZS=function(){var n=this;setTimeout(function(){var t,e,e;null!=(e=null==(e=null==(t=n.vMi)?void 0:t.getParent)?void 0:e.call(t))&&e.focus(!0)},0)},e.prototype.Hc=function(t,e){var e,n,t,i,e=new k.Workbook(e,{allowUndo:!1});e.xU=!0,e.suspendPaint(),e.suspendEvent(),this.fb(e),n=e.getSheet(0),this.BMi=n,this.QMi(n),this.RMi(),this.PMi(),this.YMi=this.WMi(),t=this.HMi(t.getDataView()),this.CMi=t,this.VMi(e,n.getViewportBottomRow(1)<t.length-1),this.FMi(n,t),e.resumeEvent(),e.resumePaint(),(i=this).mMi&&(i.JMi=r.setTimeout(function(){var t,e,t=(0,k.GC$)(i.mMi),e=t.data("workbook");e&&Math.abs(t.height()-(0,k.GC$)(e._vp).height())>e.Xm()&&e.refresh(),i.JMi&&(r.clearTimeout(i.JMi),i.JMi=0)},100))},e.prototype.fb=function(t){t.options.allowContextMenu=!1,t.options.allowUserZoom=!1,t.options.allowUserDragDrop=!1,t.options.allowDragHeaderToMove=k.AllowDragHeaderToMove.none,t.options.allowUserDragFill=!1,t.options.showHorizontalScrollbar=!1,t.options.scrollbarMaxAlign=!0,t.options.tabStripVisible=!1,t.options.tabNavigationVisible=!1,t.options.grayAreaBackColor="white",t.options.scrollByPixel=!0,this.XMi(t)},e.prototype.QMi=function(t){var e=this.options;t.selectionPolicy(k.SelectionPolicy.single),t.selectionUnit(k.SelectionUnit.row),t.options.showFormulas=!0,t.options.rowHeaderVisible=!1,(t.options.colHeaderVisible=!1)===e.showViewName?t.setRowVisible(0,e.showViewName):t.frozenRowCount(1),t.options.gridline.showHorizontalGridline=!1,t.options.gridline.showVerticalGridline=!1,t.setColumnCount(2),t.setRowCount(30),t.setColumnWidth(0,20),t.setColumnWidth(1,"*"),t.outlineColumn.options({columnIndex:1,showIndicator:!0,collapseIndicator:M.ARROW_RIGHT,expandIndicator:M.ARROW_DOWN}),t.outlineColumn.refresh(),t.showRowOutline(!1),t.rowOutlines.KMi=!0,t.t4(0,1)},e.prototype.zMi=function(t,e,n){var i,o,i=this;if(!i.AMi)switch(t){case"showSource":i._Mi();break;case"fieldAreaVisible":case"crossAreaVisible":case"groupAreaVisible":o=i.vMi,i.detach(),i.vMi=o,i.Ti.innerHTML=i.yMi(),i.mMi=i.Ti.querySelector(".".concat(_)),i.LMi=i.Ti.querySelector(".".concat(F)),i.ZMi=i.Ti.querySelector(".".concat(x)),this.OMi=this.ZMi.querySelector(".".concat(y)),i.xMi=i.Ti.querySelector(".".concat(f)),i.SMi=i.Ti.querySelector(".".concat(c)),i.EMi=i.Ti.querySelector(".".concat(c,".").concat(J,".").concat(at)),this.GMi=i.Ti.querySelector(".".concat(s)),this.pMi=i.Ti.querySelector(".".concat(rt)),i.attach(o),i.kMi(),o.UMi=i}},e.prototype.Zp=function(t){var n=this.options||{};t=k.GC$.extend(!0,{},Nt,t),k.GC$.each(t,function(t,e){n.hasOwnProperty(t)&&(n[t]=e)})},e.prototype.RMi=function(){var S,l,z,o,a,u,e,r,c,s,f,S=this,l=S.Tp(),z=S.vMi,o=S.LMi.getBoundingClientRect();l.bind(k.Events.EditStarting+n,function(t,e){var n,i,n=e.row,i=S.CMi;i[n]&&i[n].isCalculateColumn||(e.cancel=!0)}),l.bind(k.Events.EditStarted+n,function(t,e){var n,i;e.row!==l.getRowCount()-1||l.il.text()||l.il.text("="),l.il&&(l.il.qMi=!0),S.$Mi=!0}),l.bind(k.Events.EditEnded+n,function(t,e){var n,i,o,e,u,r,a,c,r,s,c,n=e.row,i=S.CMi,o=z.getDataView(),e=e.editingText,u=!1;if(S.$Mi=!1,r=l.getText(n,1),a=e&&e.replace(/\s/g,""),c=r&&r.replace(/\s/g,""),!a){if(r)return;u=!0}if(String(c).toUpperCase()===String(a).toUpperCase()||u){if(i[n]&&i[n].isCalculateColumn){if(r=S.HMi(o,n-1),e){if(0===e.indexOf("=")&&e!==(r&&r.value)){try{(s=l.Lp).parse(new Q.CalcSource(s),e,0,0)}catch(t){return void S._Mi()}r?((c=L({},r)).value===c.caption&&(c.caption=e),c.value=e,z.zA({cmd:E.VIEW_UPDATE_COLUMN_INFO,columnInfos:[c]})):z.zA({cmd:E.VIEW_Add_COLUMN_INFO,columnInfo:{value:e,caption:e,readonly:!0}})}}else r&&z.zA({cmd:E.VIEW_REMOVE_COLUMN_INFO,value:r.value});S._Mi(),l.il&&(l.il.qMi=!1),S.ZS()}}else S._Mi()}),l.bind(k.Events.ValueChanged+n,function(t,e){var n,i,o,u,r,a,c,s,l,f,d,h,g,o,b,v,M,I,m,N,D,j,C,w,p,x,y,A,T,n=e.newValue,i=e.row,o=e.col,u=e.sheet,r=z.getDataView();if(0===o&&r){if(a=[],l=r.getColumnInfos(!(s=c=void 0),!0),(f=S.CMi[e.row])&&f.isCalculateColumn)c=f.value;else if(f.bMi){for(d=0;d<l.length;d++)if((h=l[d])&&h.cross&&h.cross===f.bMi){s=d;break}}else c=xt(g=u.getValue(i,1,k.SheetArea.viewport,k.ValueType.richText));if(o=r.getColumnInfos(!0),0===e.row&&c===r.name){for(b=0,v=o;b<v.length;b++)h=v[b],a.push(L(L({},h),{visible:n}));for(M=0,I=l;M<I.length;M++)m=I[M],a.push(L(L({},m),{visible:n}))}else if(s!==Z){for(N=0,D=o;N<D.length;N++)(h=D[N]).isCross&&h.RBt===s&&a.push(L(L({},h),{visible:n}));a.push(L(L({},l[s]),{visible:n}))}else if(1===(j=u.getCell(i,1).textIndent())){for(C=0,w=o;C<w.length;C++)if(h=w[C],((0,O.tIi)(h.caption)===c||h.value===c)&&!h.isGroup){a.push(L(L({},h),{visible:n}));break}}else for(x=(p=function(t){return"string"==typeof t?t.substring(0,t.indexOf(".")):""})(o[i-1].value),y=0,A=o;y<A.length;y++)if("string"==typeof(T=(h=A[y]).value)&&-1!==T.indexOf(x)&&-1!==T.indexOf("."+c)&&!h.isGroup){a.push(L(L({},h),{visible:n}));break}z.zA({cmd:E.VIEW_UPDATE_COLUMN_INFO,columnInfos:a}),S.CMi=S.HMi(r),S.ZS()}}),a=function(t){var t=S.CMi[t].nIi;return!N(t)&&0<t.indexOf(".")},u=function(t){var t=S.CMi[t].nIi;return!N(t)&&0<t.indexOf(".")&&S.eIi().isLookupField(t)},e=function(t){return!N(t)&&!N(S.oIi)&&0<t.indexOf(".")},r=function(t){var t=S.CMi[t].nIi;return e(t)&&!S.mLt(t)},c=function(t){var t=S.CMi[t].nIi;return e(t)&&S.mLt(t)},s=function(t){var t,e,n,e,t=S.CMi[t],e=t.yBt,n=t.nIi;S.uIi=n,e?(S.oIi=n,S.cIi=[],S.rIi&&-1<S.rIi.indexOf(".")&&(S.rIi=void 0,S.aIi=-1,S.wMi=-1),S.AMi=!0,S.sIi(!0),S.MIi(!1),S.IIi(),S.AMi=!1):G(t)&&(e=S.gIi(t.bMi),S.NIi=S.CMi.filter(G).indexOf(t),S.cIi=S.lIi(),S.options.groupAreaVisible?(S.oIi=e.over,S.rIi&&-1<S.rIi.indexOf(".")&&(S.rIi=void 0,S.aIi=-1,S.wMi=-1),S.AMi=!0,S.sIi(!0),S.MIi(!1),S.IIi(),S.AMi=!1):S.options.crossAreaVisible&&S.IIi())},f={},S.DIi=function(t){var e,t,n,n,e,i;t.target===l.Dg()&&(e=S.fIi(l,t))&&!S.$Mi&&(f={x:t.x,y:t.y},t=e.row,n=e.col,!N(t))&&0<t&&!N(n)&&(u(t)?s(t):!r(t)&&(e.cellTypeHitInfo&&e.cellTypeHitInfo.outlineColumnHitInfo&&(S.dIi=!0),n=l.getValue(t,1),e=void 0,S.Ano=!1,"string"!=typeof n?(e=n=l.getFormula(t,1),S.Ano=!0):S.CMi[t].isRelationship?(e=S.hIi(l,t)+"."+n,S.TIi=!1):(e=xt(i=l.getValue(t,1,k.SheetArea.viewport,k.ValueType.richText)),S.TIi=!0),S.CMi[t].isRelationship||!wt(S.CMi[t])||S.options.crossAreaVisible)&&(S.rIi=e,o=S.LMi.getBoundingClientRect(),S.aIi=t,S.wMi=t,s(t)))},S.bIi=function(t){var e,n,n,i,e,t,e=S.rIi,n=t.x!==f.x||t.y!==f.y;if(e&&n){if(S.AIi(t,e,o,S.LMi),(n=S.fIi(l,t))&&(i=n.row,e=n.col,!N(e))&&i&&i!==S.wMi){if(a(i))return;l.t4(i,1)}(t=l.Dg())&&(t.style.cursor="move"),S.xMi&&(S.xMi.style.cursor="move"),S.dIi=!1}},S.vIi=function(t){var e,n,i,n,o,u,r,n,o,t,u,e;S.rIi&&((n=S.fIi(l,t))&&!S.dIi&&(i=n.row,n=n.col,o=z.jIi,u=S.aIi,N(i)||N(n)||!(i!==u&&o<i&&o<u||i===u)?(l.t4(S.aIi,0),S.zIi(t.target,S.xMi)&&S.kMi(),S.CIi(t)):(r=z.getDataView())&&!c(u)&&(S.CMi[u].isRelationship&&2===S.CMi[u].textIndent?S.wIi(u,i):S.yIi(u,i),o=!((n=S.CMi[i]).yBt||G(n)||a(i)),S.options.crossAreaVisible)&&o&&(S.rIi=void 0,S.TIi=void 0,S.aIi=-1,S.wMi=-1,S.cIi=Z,S.oIi=Z,S.NIi=void 0,S.mIi=void 0,S.AMi=!0,S.sIi(!1),S.MIi(!0),S.LIi(),S.xIi(),S.AMi=!1)),S.dIi=!1,S.rIi=void 0,S.TIi=void 0,S.aIi=-1,S.wMi=-1,(t=l.Dg())&&(t.style.cursor="default"),S.xMi&&(S.xMi.style.cursor="default"),u=S.SIi)&&(u.style.display="none")},(0,k.GC$)(".".concat(ut)).bind("click",function(){var t,e,n,t=l.getRowCount();S.$Mi||0===t||(e=t+1,l.suspendPaint(),l.ffe(t,1,3,{rowExpand:!1,clearUndoNotInCommand:!1}),S.CMi[t]={isCalculateColumn:!0,checkStatus:!0,value:"",textIndent:1},l.getCell(e,1).locked(!1).textIndent(1),l.outlineColumn.getCollapsed(0)&&l.outlineColumn.setCollapsed(0,!1),l.outlineColumn.refresh(),l.setActiveCell(e,1),l.showCell(t,0,k.VerticalPosition.center,k.HorizontalPosition.center),n=setTimeout(function(){l.startEdit(),clearTimeout(n)},0),l.resumePaint())}),S.mMi&&S.mMi.addEventListener("mousedown",S.DIi,!0),document.addEventListener("mousemove",S.bIi,!0),document.addEventListener("mouseup",S.vIi,!0)},e.prototype.hIi=function(t,e){for(;0<=e;){var n;if(1===t.getCell(e,1).textIndent())return t.getValue(e,1);e--}return""},e.prototype.ZIi=function(n,i){var t,e,o,e,u,r,a,c,s,l,f,d,h,t=this,e=t.eIi();if(t.CMi=t.HMi(e),o=e.getColumnInfos(),e=t.CMi.filter(B),r=(u=t.CMi.filter(function(t,e){return e<n&&G(t)&&!t.isRelationship})).reduce(function(t,e){return t+o.filter(function(t){return t.RBt===e.RBt}).length-1},0),a=n<i?i+1:i,s=(c=t.CMi.filter(function(t,e){return e<a&&G(t)&&!t.isRelationship})).reduce(function(t,e){return t+o.filter(function(t){return t.RBt===e.RBt}).length-1},0),l=t.CMi.filter(function(t,e){return e<n&&wt(t)&&!t.isRelationship}).length,f=t.CMi.filter(function(t,e){return e<i&&wt(t)&&!t.isRelationship}).length,!t.CMi[n].isRelationship&&t.CMi[i].isRelationship)if(l=0,i<n){for(d=i;0<=d;d--)if(!t.CMi[d].isRelationship||2!==t.CMi[d].textIndent){f=i-d;break}}else{for(h=!1,d=i;d<t.CMi.length;d++)if(!t.CMi[d].isRelationship||2!==t.CMi[d].textIndent){f=i-d,h=!0;break}h||(f=i-t.CMi.length)}return{columnInfos:o,columnFieldInfos:e,crossColumnCountBeforeFromRow:r,crossColumnCountBeforeToRow:s,crossRelationFieldCountBeforeFromRow:l,crossRelationFieldCountBeforeToRow:f}},e.prototype.OIi=function(n,t){var e,i,o,i,u,r,a,c,s,l,f,e=this,i=e.eIi();return e.CMi=e.HMi(i),o=i.getColumnInfos(),i=e.CMi.filter(B),r=(u=e.CMi.filter(function(t,e){return e<n&&G(t)})).reduce(function(t,e){return t+o.filter(function(t){return t.RBt===e.RBt}).length-1},0),a=n<t?t+1:t,s=(c=e.CMi.filter(function(t,e){return e<a&&G(t)})).reduce(function(t,e){return t+o.filter(function(t){return t.RBt===e.RBt}).length-1},0),l=0,f=e.EIi(n,t),e.CMi[n].textIndent===e.CMi[t].textIndent&&e.CMi[n].isRelationship&&e.CMi[t].isRelationship&&e.GIi(n,t)&&(f=0),{columnInfos:o,columnFieldInfos:i,crossColumnCountBeforeFromRow:r,crossColumnCountBeforeToRow:s,crossRelationFieldCountBeforeFromRow:0,crossRelationFieldCountBeforeToRow:f}},e.prototype.GIi=function(t,e){for(var n,i,o,u,r,a,n=this,i=n.eIi(),o=n.HMi(i),u="",r=t;0<=r;r--)if(!o[r].isRelationship||2!==o[r].textIndent){u=o[r].value;break}for(a="",r=e;0<=r;r--)if(!o[r].isRelationship||2!==o[r].textIndent){a=o[r].value;break}return u===a},e.prototype.EIi=function(t,e){for(var n,i,o,n,u,r,n=this,i=n.eIi(),o=n.HMi(i),n=e-t,r=t;0<=r;r--)if(!o[r].isRelationship||2!==o[r].textIndent){u=t-r-1;break}return n+u},e.prototype.yIi=function(t,e){var n,i,o,u,r,a,c,s,u,l,f,d,s,a,h,g,b,n=this,i=n.vMi,o=i.getDataView();if(!(n.CMi[t].isRelationship&&1===n.CMi[t].textIndent&&n.CMi[e].isRelationship&&2===n.CMi[e].textIndent||(r=(u=n.ZIi(t,e)).columnInfos,a=u.crossColumnCountBeforeFromRow,c=u.crossColumnCountBeforeToRow,f=t-1-(s=u.crossRelationFieldCountBeforeFromRow),d=e-1-(u=u.crossRelationFieldCountBeforeToRow),(s=t-1+(l=n.pIi(o).length||0)-s+a)===(a=e-1+l-u+c)))){if(h=1,G(n.CMi[t])&&(g=n.kIi(o,s),h=r.filter(function(t){return t.RBt===g}).length),n.CMi[t].isRelationship)for(b=t+(h=1);b<n.CMi.length&&2===n.CMi[b].textIndent;b++)h++;i.zA({cmd:E.VIEW_MOVE_COLUMN,fromColumnOptionIndex:f,toColumnOptionIndex:d,fromColumnIndex:s,toColumnIndex:a,count:h}),n.xxi(),n.ZS()}},e.prototype.xxi=function(){var t,e,n,t=this.vMi,e=t.getDataView(),n=t.Mf.rowFilter();t.suspendPaint(),t.EPt(),n.UIi(),t.resetNewView(e.ILt,e.aPt,e.LEt),n.QIi(),t.jPt(),t.resumePaint()},e.prototype.wIi=function(t,e){var n,i,o,u,r,a,c,s,u,l,f,d,s,a,e,h;t!==e&&(o=(i=(n=this).vMi).getDataView(),r=(u=n.OIi(t,e)).columnInfos,a=u.crossColumnCountBeforeFromRow,c=u.crossColumnCountBeforeToRow,f=t-1-(s=u.crossRelationFieldCountBeforeFromRow),d=e-1-(u=u.crossRelationFieldCountBeforeToRow),(s=t-1+(l=n.pIi(o).length||0)-s+a)!==(a=e-1+l-u+c))&&(e=1,n.CMi[t].isRelationship&&1===n.CMi[t].textIndent&&(h=n.kIi(o,s),e=r.filter(function(t){return t.RBt===h}).length),i.zA({cmd:E.VIEW_MOVE_COLUMN,fromColumnOptionIndex:f,toColumnOptionIndex:d,fromColumnIndex:s,toColumnIndex:a,count:e}),n.xxi(),n.ZS())},e.prototype.sIi=function(t){var e=this,n=e.EMi;(e.options.crossAreaVisible=t)?n.classList.remove(S):n.classList.add(S)},e.prototype.setGroupAreaVisible=function(t){this.MIi(t)},e.prototype.MIi=function(t){var e=this,n=e.SMi,i=e.GMi;(e.options.groupAreaVisible=t)?(n.classList.remove(S),i.classList.remove(S)):(n.classList.add(S),i.classList.add(S))},e.prototype.AIi=function(t,e,n,i){var o,u,r,a,r,o=this,u=o.SIi,r=document.documentElement,a=r&&r.scrollTop||0,r=r&&r.scrollLeft||0;u&&u.parentElement&&u.parentElement!==i&&(u.parentElement.removeChild(u),u=this.SIi=void 0),u||((u=o.SIi=document.createElement("span")).className="gc-table-sheet-move-span",i.appendChild(u)),u.textContent=e,u.style.top=t.pageY+5-a-n.top+"px",u.style.left=t.pageX-50-r-n.left+"px",u.style.display="block"},e.prototype.fIi=function(t,e){var n=t.uw();return t.hitTest(e.pageX-n.left,e.pageY-n.top,!1)},e.prototype.PMi=function(){var o,u,r,o=this,u=o.Tp(),r=o.vMi;r.bind(k.Events.ColumnChanged+n,function(t,e){var n,i,n,n,n,n=e.sheetArea,i=r.getDataView();1!==n&&3!==n||("isVisible"===e.propertyName?(n=e.sheet.getValue(0,e.col,k.SheetArea.colHeader),0<=(n=o.nv(i,n))&&(u.suspendPaint(),u.suspendEvent(),u.setValue(n,0,e.newValue),u.resumeEvent(),u.resumePaint())):"addColumns"!==e.propertyName&&"deleteColumns"!==e.propertyName||(u.suspendPaint(),u.suspendEvent(),n=o.RIi(r),o.FMi(u,n),u.resumeEvent(),u.resumePaint()),o.CMi=o.HMi(i))})},e.prototype.Tp=function(){if(this.mMi){var t=(0,k.GC$)(this.mMi).data("workbook");if(t)return t.getSheet(0)}return null},e.prototype.RIi=function(t){var e,n,i,o,u,r,a,a,e=t.getDataView();if(e){for(i=[(n=this.HMi(e))[0]],u=0,r=(o=t.Mf).getColumnCount();u<r;u++)a=o.getValue(0,u,k.SheetArea.colHeader),0<=(a=this.nv(e,a))&&i.push(n[a]);return i}return[]},e.prototype.nv=function(t,e){var n,i;if(t)for(n=t.getColumnInfos(!0),i=0;i<n.length;i++)if(n[i].caption===e||n[i].value===e)return i+1;return-1},e.prototype.kIi=function(t,e){var e,t,e=t.BPt(e),t=t.yLt[e];return t&&t.RBt},e.prototype.HMi=function(t,e){var n,i,o,u,r,a,c,s,l,f,d,h,f,g,n=this;if(t){for(i=[],u=t.getColumnInfos(!(o=!1),!0),r=!N(e),a=function(){return r&&i.length-1>=e},c=0;c<u.length;c++){if("string"==typeof(s=u[c]).value&&0===s.value.trim().indexOf("="))i.push({value:s.value,textIndent:1,checkStatus:!1!==s.visible,isCalculateColumn:!0});else if(N(s.cross))if("string"==typeof s.value&&t.FPt(s.value)&&1===s.value.split(".").length)if(u[c].isRelationship){for(l=s.value,f=!this.PIi(u,l)||!1,i.push({value:l,textIndent:1,checkStatus:!1!==s.visible,setCollapsed:f,isRelationship:!0}),d=c+1;this.YIi(u,d,l);)"string"==typeof(h=u[d].value)&&i.push({value:h.substring(h.indexOf(".")+1),textIndent:2,checkStatus:!1!==u[d].visible,needCheckBox:!0,isRelationship:!0}),d++;c=d-1}else i.push({value:n.$t(s),textIndent:1,checkStatus:!1!==s.visible}),n.WIi(s.value,n.YMi,t).forEach(function(t){return i.push(t)});else{if("string"==typeof s.value&&"_ganttColumn"===s.value){if(a())return s;continue}g=f=void 0,"string"==typeof s.value&&-1===s.value.indexOf(".")&&(f=t.isLookupField(s.value),s.yBt=f,g=s.value),i.push({value:n.$t(s),textIndent:1,checkStatus:!1!==s.visible,yBt:f,nIi:g})}else i.push({value:n.$t(s),bMi:s.cross,RBt:s.RBt,textIndent:1,checkStatus:!1!==s.visible});if((N(s.visible)||s.visible)&&(o=!0),a())return s}return r?Z:(i.unshift({value:t.name,textIndent:0,checkStatus:o}),i)}return[]},e.prototype.PIi=function(t,e){for(var n,i,n=0;n<t.length;n++)if("string"==typeof(i=t[n]).value&&-1!==i.value.indexOf(e)&&!1!==i.visible)return!0;return!1},e.prototype.YIi=function(t,e,n){var t;return!(e>=t.length)&&-1!==(t=t[e]).value.indexOf(n)&&-1===t.value.indexOf("=")},e.prototype.$t=function(t){var e,n,t,t,i,o,e=[],n=t.value instanceof Function?t.value.name:t.value,t=(0,O.tIi)(t.caption)||n;return e.push({text:t}),(t=this.options.showSource)===mt.field?"="!==n[0]&&(i=n):t===mt.formula?"="===n[0]&&(i=n):t===mt.all&&(i=n),i&&e.push({style:{foreColor:"lightgrey"},text:o="("+i+")"}),{richText:e}},e.prototype.VMi=function(t,e){t.options.showVerticalScrollbar=e},e.prototype.FMi=function(d,r){var a,t,e,n,i,h,g,o,u,c,s,l,f,b,v,a=this,t=r.length,e=d.outlineColumn,n,i=this.options.showEditButton,h=this.vMi,g=h.Mf,o={};for(d.suspendEvent(),d.suspendPaint(),u=0;u<d.getRowCount();u++)e.getCollapsed(u)&&(e.setCollapsed(u,!1),o[(c=d.getCell(u,1)).value()]=c.textIndent()),r[u]&&!N(r[u].setCollapsed)&&r[u].setCollapsed&&(c=d.getCell(u,1)).textIndent()<=1&&(o[c.value()]=c.textIndent());for(e.reset(),d.setRowCount(t),d.clear(0,0,t,2,3,k.StorageType.data|k.StorageType.style),s=0;s<t;s++)l=r[s],c=d.getCell(s,1).textIndent(l.textIndent),l.isCalculateColumn?d.setFormula(s,1,l.value):c.value(l.value),(l.textIndent<=1||l.needCheckBox)&&d.getCell(s,0).value(l.checkStatus).cellType(new Y.CheckBox),f=[],Ct(l)&&f.push({useButtonStyle:!1,hoverBackColor:"white",imageType:k.ButtonImageType.custom,imageSrc:"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+bG9vayB1cCBjb2x1bW4xPC90aXRsZT4KICAgIDxnIGlkPSJsb29rLXVwLWNvbHVtbjEiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwYXRoIGQ9Ik0zLDMgTDUsMyBMNSw0IEwzLDQgTDMsMyBaIE0zLDYgTDUsNiBMNSw3IEwzLDcgTDMsNiBaIE0zLDkgTDUsOSBMNSwxMCBMMywxMCBMMyw5IFogTTMsMTIgTDUsMTIgTDUsMTMgTDMsMTMgTDMsMTIgWiBNNiwzIEwxNCwzIEwxNCw0IEw2LDQgTDYsMyBaIE02LDYgTDE0LDYgTDE0LDcgTDYsNyBMNiw2IFogTTYsOSBMMTQsOSBMMTQsMTAgTDYsMTAgTDYsOSBaIE02LDEyIEwxNCwxMiBMMTQsMTMgTDYsMTMgTDYsMTIgWiIgaWQ9IuW9oueKtue7k+WQiCIgZmlsbD0iIzY2NjY2NiI+PC9wYXRoPgogICAgPC9nPgo8L3N2Zz4=",visibility:k.ButtonVisibility.always,iMe:!0,imageSize:{height:20,width:20}}),G(l)&&(f.push({useButtonStyle:!1,hoverBackColor:"white",imageType:k.ButtonImageType.custom,imageSrc:"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMzJweCIgaGVpZ2h0PSIzMnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+Y3Jvc3MgY29sdW1uMTwvdGl0bGU+CiAgICA8ZyBpZD0iY3Jvc3MtY29sdW1uMSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9Iue8lue7hCIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMS4wMDAwMDAsIDIuMDAwMDAwKSIgZmlsbD0iIzY2NjY2NiI+CiAgICAgICAgICAgIDxwYXRoIGQ9Ik0xMywwIEwxMywzLjUzMSBMMTIsMi42OTcgTDEyLDEgTDksMSBMOSw1IEw4LDUgTDgsMSBMNSwxIEw1LDEwIEw0LDEwIEw0LDEgTDEsMSBMMSwxMSBMMy42OTcsMTEgTDQuNTMxLDEyIEwwLDEyIEwwLDAgTDEzLDAgWiBNMTMsOS40NjggTDEzLDEyIEwxMC40NjksMTIgTDExLjMwMywxMSBMMTIsMTEgTDEyLDEwLjMwMiBMMTMsOS40NjggWiIgaWQ9IuW9oueKtue7k+WQiCIgZmlsbC1ydWxlPSJub256ZXJvIj48L3BhdGg+CiAgICAgICAgICAgIDxwb2x5Z29uIGlkPSLlvaLnirbnu5PlkIgiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDEwLjAwMDAwMCwgOS4wMDAwMDApIHNjYWxlKC0xLCAtMSkgdHJhbnNsYXRlKC0xMC4wMDAwMDAsIC05LjAwMDAwMCkgIiBwb2ludHM9IjggMTQgNSAxMS41IDggOSA4IDExIDEyIDExIDEyIDcgMTAgNyAxMi41IDQgMTUgNyAxMyA3IDEzIDEyIDggMTIiPjwvcG9seWdvbj4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==",visibility:k.ButtonVisibility.always,iMe:!0,imageSize:{height:16,width:16}}),f.push({imageType:k.ButtonImageType.clear,visibility:k.ButtonVisibility.always,iMe:!0,imageSize:{height:18,width:18},command:function(t,e,n,i){var o,o,u,o=h.getDataView();if(o){for(o=o.uPt(r.filter(B).indexOf(r[e])),h.zA({cmd:E.REMOVE_CROSS_COLUMN,index:o}),a.cIi=Z,a.oIi=Z,a.NIi=void 0,a.mIi=void 0,u=0;u<r.length;u++)if(r[u]&&B(r[u])){t.t4(u,1);break}a.AMi=!0,a.MIi(!0),a.sIi(!1),a.AMi=!1,a.ZS()}}})),i&&f.push({imageType:k.ButtonImageType.custom,imageSrc:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgY2xpcC1wYXRoPSJ1cmwoI2NsaXAwXzE5XzE1MTEpIj4KPHJlY3Qgd2lkdGg9IjE2IiBoZWlnaHQ9IjE2IiBmaWxsPSJ0cmFuc3BhcmVudCIvPgo8cGF0aCBkPSJNMC44ODE2OCAxNkMwLjc1OTg0IDE1Ljk5OTggMC42MzkzNzcgMTUuOTc0MiAwLjUyNzk1MSAxNS45MjQ5QzAuNDE2NTI1IDE1Ljg3NTYgMC4zMTY1NzQgMTUuODAzNyAwLjIzNDQ1NCAxNS43MTM3QzAuMTUwODQ5IDE1LjYyNDUgMC4wODcwNTI3IDE1LjUxODYgMC4wNDcyNDg5IDE1LjQwMjlDMC4wMDc0NDUxNSAxNS4yODczIC0wLjAwNzQ2NzAxIDE1LjE2NDYgMC4wMDM0OTAwMiAxNS4wNDI4TDAuMjE4NjQ2IDEyLjY3NjlMMTAuMTU4IDIuNzQxMDhMMTMuMjY0MiA1Ljg0NjM2TDMuMzI3NDQgMTUuNzgxM0wwLjk2MTU5NSAxNS45OTY1QzAuOTM1MDI4IDE1Ljk5ODkgMC45MDgzNTkgMTYuMDAwMSAwLjg4MTY4IDE2VjE2Wk0xMy44ODQyIDUuMjI1NDhMMTAuNzc4OSAyLjEyMDJMMTIuNjQxNSAwLjI1NzU2MUMxMi43MjMxIDAuMTc1OTExIDEyLjgxOTkgMC4xMTExMzYgMTIuOTI2NSAwLjA2Njk0MTdDMTMuMDMzMiAwLjAyMjc0NzUgMTMuMTQ3NCAwIDEzLjI2MjggMEMxMy4zNzgyIDAgMTMuNDkyNSAwLjAyMjc0NzUgMTMuNTk5MSAwLjA2Njk0MTdDMTMuNzA1NyAwLjExMTEzNiAxMy44MDI2IDAuMTc1OTExIDEzLjg4NDIgMC4yNTc1NjFMMTUuNzQ2OCAyLjEyMDJDMTUuODI4NSAyLjIwMTc2IDE1Ljg5MzIgMi4yOTg2MiAxNS45Mzc0IDIuNDA1MjNDMTUuOTgxNiAyLjUxMTg0IDE2LjAwNDQgMi42MjYxMSAxNi4wMDQ0IDIuNzQxNTJDMTYuMDA0NCAyLjg1NjkzIDE1Ljk4MTYgMi45NzEyMSAxNS45Mzc0IDMuMDc3ODJDMTUuODkzMiAzLjE4NDQzIDE1LjgyODUgMy4yODEyOCAxNS43NDY4IDMuMzYyODRMMTMuODg1IDUuMjI0NkwxMy44ODQyIDUuMjI1NDhWNS4yMjU0OFoiIGZpbGw9IiM2NjY2NjYiLz4KPC9nPgo8ZGVmcz4KPGNsaXBQYXRoIGlkPSJjbGlwMF8xOV8xNTExIj4KPHJlY3Qgd2lkdGg9IjE2IiBoZWlnaHQ9IjE2IiBmaWxsPSJ0cmFuc3BhcmVudCIvPgo8L2NsaXBQYXRoPgo8L2RlZnM+Cjwvc3ZnPgo=",visibility:k.ButtonVisibility.onSelected,iMe:!0,command:function(t,e,n,i){var o,u,r,e,a,c,s,l,s,l,f,o=h.getDataView();if(o&&(u=d.getText(e,n)))for(0!==u.indexOf("=")?0<=(e=u.indexOf("("))&&(u=u.substring(0,e)):r=!0,a=o.getColumnInfos(),c=0;c<a.length;c++)if(s=a[c],l=r?s.value:(0,O.tIi)(s.caption||s.value),!s.isGroup&&l===u){s=c,l=g.getSelections(),f=[(0,k.Ec)(-1,s,-1,1)],g.EA(l,f),g.setSelection(-1,s,-1,1),g.showCell(g.Tb||0,s,k.VerticalPosition.nearest,k.HorizontalPosition.nearest),g.jA(l);break}}}),0<f.length&&c.cellButtons(f);for(b in d.outlineColumn.options({columnIndex:1,showIndicator:!0}),d.outlineColumn.options().collapseIndicator=M.ARROW_RIGHT,d.outlineColumn.options().expandIndicator=M.ARROW_DOWN,d.outlineColumn.refresh(),d.showRowOutline(!1),e=d.outlineColumn,o)if(o.hasOwnProperty(b))for(v=o[b],u=0;u<d.getRowCount();u++)if((c=d.getCell(u,1)).value()===b&&c.textIndent()===v){e.setCollapsed(u,!0);break}d.resumePaint(),d.resumeEvent()},e.prototype.kMi=function(){var t,e,n,i,o,u,r,o,a,c,t=this,e=t.vMi,n=t.rIi,i=e.getDataView();i&&t.options.groupAreaVisible&&(o=i.oPt,u=(0,k.RF)(e.lMi)||[],n?(r=void 0,r=t.TIi?this.CMi.filter(B).indexOf(this.CMi[t.aIi]):this.CMi.indexOf(this.CMi[t.aIi-1]),o&&o.length&&(r=i.ALt()[r]),o=i.ILt[r],a=o&&o.value,0===(c=u.filter(function(t){return t.field===a})).length&&(u.push({caption:t.Ano?n:(0,O.hze)(n),field:a}),e.zA({cmd:E.GROUP_OPTIONS,groupOptions:u}),t.ZS())):t.updatePanelLayout())},e.prototype.pIi=function(t){var e,t,n,i,o,e=[];if((t=(t=t||this.eIi())&&t.getColumnInfos())&&t.length)for(n=0,i=t;n<i.length;n++)(o=i[n]).isGroup&&e.push(o);return e},e.prototype.yMi=function(){var t,e,n,i,o,u,r,t=this.options.fieldAreaVisible,e=this.options.crossAreaVisible,n=this.options.groupAreaVisible,i="",o="",u="",r="";return i='<div class="'.concat(l,'">\n                            <span class="gc-table-sheet-field-header">').concat((0,O.getSR)().GroupPanelFieldsHeader,'\n                                <span class="').concat(ut,"\" title='").concat((0,O.getSR)().GroupPanelAddCalculateColumn,"'></span>\n                            </span>\n                            <div class=\"").concat(_,'"></div>\n                        </div>'),o=this.FIi(e),u='<div class="'.concat(c,'">\n                                <span class="gc-table-sheet-group-header">').concat((0,O.getSR)().GroupPanelGroupsHeader,'</span>\n                                <ul class="').concat(f,'">\n                                    <span class="gc-table-sheet-group-tip">').concat((0,O.getSR)().GroupPanelTip,"</span>\n                                </ul>\n                            </div>"),r='<div class="'.concat(s,'"></div>'),!1===t&&(i=i.replace(l,l+" "+S),u=u.replace(c,"".concat(c," half-height")),r=r.replace(s,"".concat(s," half-height"))),!1===n&&(u=u.replace(c,c+" "+S),r=r.replace(s,s+" "+S)),!1===e&&!1===n&&(i=i.replace(l,"".concat(l," total-height"))),'<div class="'.concat(F,'">\n                    ').concat(i,"\n                    ").concat(u,"\n                    ").concat(o,"\n                    ").concat(r,'\n                </div>\n                <div class="').concat(rt,'" style="display:none;"></div>')},e.prototype.updatePanelLayout=function(){this.IIi(),this.LIi(),this.xIi(),this._Mi()},e.prototype.getBindingTable=function(){return this.vMi},e.prototype.LIi=function(){this.SWt(),this.JIi(),this.Fz()},e.prototype.xIi=function(){var e,t,n,t,e=this;this.GMi&&(this.XIi(),t=null==(t=(n=function(){var t;return null==(t=e.GMi)?void 0:t.querySelector(".".concat(m))})())?void 0:t.scrollTop,this.GMi.innerHTML=this.KIi(),n()&&(n().scrollTop=t),this._Ii(),this.qIi=!1)},e.prototype.JIi=function(){var t,e,n,i,o,u,r,t,e=this.vMi.lMi,n=this.xMi;if(e&&n){for(i="",o=void 0,u=e.length,this.jMi=this.jMi>=u?u-1:this.jMi,r=0;r<e.length;r++)o=e[r],i+=this.$Ii(o,r);n.innerHTML=i,n.style.border="solid 1px lightgrey",this.igi=n.querySelector(".".concat(d))}else this.tgi()},e.prototype._Mi=function(){var t,t;this.mMi&&(t=this.vMi,t=this.HMi(t.getDataView()),this.CMi=t,this.FMi(this.Tp(),t))},e.prototype.tgi=function(){var t,t,t=this.xMi;t&&(t.innerHTML='<span class="gc-table-sheet-group-tip">'.concat((0,O.getSR)().GroupPanelTip,"</span>"),t.style.border="",this.jMi=0),(t=this.GMi)&&(t.innerHTML="")},e.prototype.Fz=function(){var u,r,t,a,c,s,e,l,u=this,r=u.vMi,t=u.BMi,a=t&&t.Dg(),c=(0,k.RF)(r.lMi),s=u.xMi;s&&(e=u.igi,l=u.LMi.getBoundingClientRect(),(0,k.GC$)(".".concat(h),s).bind("click",function(t){var e,n,i,e=t.target.className;e===X?(i=(n=(0,k.GC$)(t.target).parent()).attr(v),u.ngi(i)):e===bt&&(i=(n=(0,k.GC$)(t.target).parent()).attr(v),u.rno(i))}).bind("mousedown",function(t){var e,t,n,t,e=t.target,t=t.target.className,n=!1;-1<t.indexOf(X)||(-1<t.indexOf(nt)?(e=e.parentElement,n=!0):-1<t.indexOf(h)&&(n=!0),n&&(t=(0,k.GC$)(e).attr(v),u.egi=t,l=u.LMi.getBoundingClientRect(),u.jMi!==+t&&(u.jMi=+t,u.XIi(),u.ogi(e),u._Ii()),u.ugi=e))}),u.cgi=function(t){var e,n,i,o,n,o,n,e,n,o,e=u.egi;e!==Z&&e!==g&&(n=(c=(0,k.RF)(r.lMi))[e].caption||c[e].field,u.AIi(t,n,l,u.LMi),a&&(a.style.cursor="move"),n=!(u.Ti.style.cursor="move"),(o=(i=t.target).parentElement)&&-1<o.className.indexOf(h)?(i=o,n=!0):-1<i.className.indexOf(h)&&(n=!0),o=u.ugi,n&&o!==i&&((n=i.getAttribute(v))<e?(o.className=o.className.replace(D,"").replace(j,"").trim(),i.className=i.className+" ".concat(D)):e<n?(o.className=o.className.replace(D,"").replace(j,"").trim(),i.className=i.className+" ".concat(j)):o.className=o.className.replace(D,"").replace(j,"").trim(),u.ugi=i),n=(e=s.getBoundingClientRect()).top+document.documentElement.scrollLeft,o=e.bottom+document.documentElement.scrollTop,t.pageY<n?s.scrollTop=s.scrollTop-10:t.pageY>o&&(s.scrollTop=s.scrollTop+10))},u.rgi=function(t){var e,t,t,t,n,n,e=u.egi,t=t.target;c=(0,k.RF)(r.lMi),e!==Z&&(u.ugi.className=u.ugi.className.replace(D,"").replace(j,"").trim(),u.zIi(t,u.xMi)?(t=u.ugi)&&(t=(0,k.GC$)(t).attr(v))!==e&&(n=c.splice(e,1)[0],c.splice(t,0,n),u.jMi=+t,r.zA({cmd:E.GROUP_OPTIONS,groupOptions:c}),u.ZS()):u.ngi(e)),u.egi=Z,u.ugi=Z,a&&(a.style.cursor="default"),u.Ti.style.cursor="default",(n=u.SIi)&&(n.style.display="none")},document.addEventListener("mouseup",u.rgi,!0),document.addEventListener("mousemove",u.cgi,!0),(0,k.GC$)(".".concat(d)).bind("blur",function(t){"none"!==e.style.display&&(u.agi(t.target),e.style.display="none")}).bind("keydown",function(t){13===t.keyCode?(u.agi(t.target),e.style.display="none"):27===t.keyCode&&(e.style.display="none")}),(0,k.GC$)(".".concat(f)).bind("dblclick",function(t){var e;t.target.className===nt&&u.sgi(t)}))},e.prototype.Xeo=function(t){var e;return t?t.getColumnInfos(!0,!0).filter(function(t){return N(t.cross)}).map(function(t){var e=t.value instanceof Function?t.value.name:t.value;return{text:(0,O.tIi)(t.caption)||e,value:e}}):[]},e.prototype.Mgi=function(){var t,e,n,i,t=document.createElement("li");return t.className=$,e=this.Xeo(this.vMi.getDataView()),n=this.vMi.options.groupLayout.mode===R.GroupLayoutMode.tabular,i=this.vMi.options.groupLayout.position,t.innerHTML=Mt({sliceField:"",caption:"",formula:"",columnList:e,showSlice:n,position:i}),t},e.prototype.ogi=function(t){var e,n,i,o,u,e,n,i=this.xMi.querySelectorAll(".".concat(h));for(o in i)i.hasOwnProperty(o)&&((u=i[o]).className=u.className.replace("selected","").trim(),u===t)&&(u.className=u.className+" selected");this.GMi&&(this.GMi.innerHTML=this.KIi())},e.prototype.KIi=function(){var t,e,n,i,e,o,o,u,r,a,c,s,l,f,d,h,d,g,t,b,e=this,n=e.jMi,i="",e=e.vMi,o=e.lMi,o=o&&o[n],u=o&&o.field,r="",a=o&&o.summaryFields,c=this.Xeo(this.vMi.getDataView()),s=e.options.groupLayout&&(0,W.isGroupOutlineCondensedLayout)(e.options.groupLayout.mode);if(a)for(l=0;l<a.length;l++)h="",(d=(f=a[l]).slice)&&(h="string"==typeof d?d:d.field),d=s?f.caption:f.caption||f.formula,g=f.relateTo,t=f.position||(null==(t=this.vMi.options.groupLayout)?void 0:t.position)||"header",g=g||this.vMi.getDataView().ucr(f.formula,!0)[0],b=this.vMi.options.groupLayout.mode===R.GroupLayoutMode.tabular,r+='<li class="'.concat($,'" ').concat(v,"=").concat(n," ").concat(I,"=").concat(l,">\n                        ").concat(Mt({formula:f.formula,caption:d,sliceField:h,columnList:c,relateTo:g,position:t,showSlice:b}),"\n                   </li>");return i=u?'<div class="gc-table-sheet-summary-header">\n                            <span class="gc-table-sheet-summary-label">'.concat(u,'</span>\n                            <span class="').concat(it,'" title="').concat((0,O.getSR)().GroupPanelItemAddCalculation,'"></span>\n                            <span class="gc-table-sheet-summary-remove" title="').concat((0,O.getSR)().GroupPanelItemRemoveAll,'"></span>\n                        </div>\n                        <ul class="').concat(m,'">\n                            ').concat(r,"\n                        </ul>"):i},e.prototype._Ii=function(){var f,d,h,n,f=this,n=f.pMi;(0,k.GC$)(".".concat(m)).bind("change",function(t){var t,e,n,t,e,n,i,i,o,t,u,t,r,a,c,s,t,l,l,t=(0,k.GC$)(t.target),e=t.attr("current-data"),n=t.val(),t=t.parent();if(e!==n&&t&&t[0]&&"="!==n){if(e=f.vMi,n=(0,k.RF)(e.lMi),i=f.jMi,i=n&&n[i],o=JSON.parse(JSON.stringify(n)),u=(t=t.parent()).attr(I),r=(t=It(t)).formulaInputValue,a=t.captionInputValue,c=t.sliceInputValue,s=t.relateToValue,t=t.positionValue,i.summaryFields||(i.summaryFields=[]),c&&""!==c){if(-1===c.indexOf("(")&&(l=c.replace("=",""),!f.Igi(c=l)))return}else c=Z;(l=i.summaryFields[u])?(l.formula=r,l.caption=a||r,l.slice=""===c?Z:c,l.position=t,l.relateTo=s):i.summaryFields.push({formula:r,caption:a||r,slice:""===c?Z:c,position:t,relateTo:s});try{e.zA({cmd:E.GROUP_OPTIONS,groupOptions:n})}catch(t){e.groupBy(o)}f.ZS()}f.ggi(d,h)}),(0,k.GC$)(".".concat(w)).bind("focus",function(t){var t=t.target,e=(0,k.GC$)(t).val()||"=";(d=new P.FormulaTextBox(t,g,g)).Ngi=!0,h=new k.Workbook(n,{allowDynamicArray:!0,allowUndo:!1}),f.XMi(h),f.lgi(h),d.workbook(h),d.text(e)}).bind("blur",function(t){var t=(0,k.GC$)(t.target),e;"="===t.val()&&t.val(""),f.ggi(d,h)}).bind("keydown",function(t){13===t.keyCode&&(f.ggi(d,h),t.target.blur())}),(0,k.GC$)(".".concat(b)).bind("focus",function(t){var t=t.target,e=(0,k.GC$)(t).val()||"=";(d=new P.FormulaTextBox(t,g,g)).Ngi=!0,h=new k.Workbook(n,{allowDynamicArray:!0,allowUndo:!1}),f.XMi(h),f.lgi(h),d.workbook(h),d.text(e)}).bind("blur",function(t){var t=(0,k.GC$)(t.target),e;"="===t.val()&&t.val(""),f.ggi(d,h)}).bind("keydown",function(t){13===t.keyCode&&(f.ggi(d,h),t.target.blur())}),(0,k.GC$)(".".concat(s)).bind("mouseup",function(t){var e,n,i,o,u,r,a,c,s,l,c,s,n,e,n=t.target.className;"gc-table-sheet-summary-remove"===n?(i=f.vMi,o=(0,k.RF)(i.lMi),u=f.jMi,(r=o&&o[u]).summaryFields=[],i.zA({cmd:E.GROUP_OPTIONS,groupOptions:o}),f.ZS()):n===it?(c=(a=f.GMi).querySelector(".".concat(m)))&&!f.qIi&&(f.qIi=!0,f.XIi(),s=f.Mgi(),c.appendChild(s),f._Ii()):"gc-table-sheet-summary-item-remove-icon"===n&&(c=(l=(0,k.GC$)(t.target)).parent())&&c[0]&&(i=f.vMi,o=(0,k.RF)(i.lMi),u=f.jMi,r=o&&o[u],(n=(s=c.parent()).attr(I))&&r.summaryFields?(r.summaryFields.splice(parseInt(n,10),1),i.zA({cmd:E.GROUP_OPTIONS,groupOptions:o}),f.ZS()):n||(s.remove(),f.ggi(d,h),f.qIi=!1))})},e.prototype.lgi=function(t){var t=t.getActiveSheet();delete t.Qy().PUt.enter,t.setRowCount(1),t.setColumnCount(1)},e.prototype.ggi=function(t,e){t&&t.destroy&&t.destroy(),e&&e.destroy&&e.destroy()},e.prototype.Igi=function(t){var e,n=this.vMi.getDataView(),n=n&&n.getColumnInfos(!0).map(function(t){return t.value});return!!(n&&-1<n.indexOf(t))},e.prototype.XMi=function(t){var e,n,n,i,o,u,r,a,e,n=this.vMi.getDataView(),n=n&&n.getColumnInfos(!0,!0);if(n)for(this.Dgi&&(i=this.Dgi.map(function(t){return{value:t.attribute}}),n=n.concat(i)),o=0,u=n;o<u.length;o++)(a=(r=u[o]).value)&&"string"==typeof a&&-1===a.indexOf("=")&&r.cross===Z&&t.zwt(t.hyt,a,"=A1",0,0,!0,(0,O.getSR)().GroupPanelDropDownCalcField+" "+a)},e.prototype.XIi=function(){(0,k.GC$)(".".concat(m)).unbind("change"),(0,k.GC$)(".".concat(w)).unbind("focus"),(0,k.GC$)(".".concat(w)).unbind("blur"),(0,k.GC$)(".".concat(w)).unbind("keydown"),(0,k.GC$)(".".concat(b)).unbind("focus"),(0,k.GC$)(".".concat(b)).unbind("blur"),(0,k.GC$)(".".concat(b)).unbind("keydown"),(0,k.GC$)(".".concat(s)).unbind("mouseup")},e.prototype.sgi=function(t){var e,n,t,i,e,n=this.igi,t=(0,k.GC$)(t.target).parent(),i=t.attr(K);n.style.display="inline",n.value=i,n.style.left="",n.style.width="",t.append(n),n.focus()},e.prototype.ngi=function(t){var e,n,i,t,o,e=this,n=e.vMi,i=(0,k.RF)(n.lMi),t=i[t],o=t&&t.field;(i=i.filter(function(t){return t.field!==o})).length?n.zA({cmd:E.GROUP_OPTIONS,groupOptions:i}):n.zA({cmd:E.GROUP_OPTIONS,groupOptions:[]}),e.ZS()},e.prototype.rno=function(t){var e,t,e=this.vMi,t=e.lMi[t];e.ki(k.Events.TableSheetGroupFieldSetting,{groupOption:t})},e.prototype.SWt=function(){var t,e,t=this,e=t.xMi;(0,k.GC$)(".".concat(h),e).unbind("click"),(0,k.GC$)(".".concat(h),e).unbind("mousedown"),(0,k.GC$)(".".concat(f)).unbind("dblclick"),(0,k.GC$)(".".concat(d)).unbind("blur"),(0,k.GC$)(".".concat(d)).unbind("keydown"),(0,k.GC$)(".".concat(it)).unbind("click"),document.removeEventListener("mouseup",t.rgi,!0),document.removeEventListener("mousemove",t.cgi,!0)},e.prototype.agi=function(t){var e,n,t,i,o,u,r,a,t,c,s,e=this,n=t.value;n&&(i=(t=(0,k.GC$)(t).parent()).attr(q),o=t.attr(K),u=t.attr(tt),r=t.attr(et),a=t.attr(v),t=t.attr(I),c=e.vMi,s=(0,k.RF)(c.lMi),i||o?n!==o&&s&&(s[a].caption=(0,O.hze)(n),c.zA({cmd:E.GROUP_OPTIONS,groupOptions:s})):(u||r)&&n!==u&&s&&(s[a].summaryFields[t].caption=n,c.zA({cmd:E.GROUP_OPTIONS,groupOptions:s})),e.ZS())},e.prototype.$Ii=function(t,e){var n,t,i,n=t.field,t=(0,O.tIi)(t.caption)||n,i=e===this.jMi?" selected":"";return'<li class="'.concat(h).concat(i,'" ').concat(K,'="').concat(a(t),'" ').concat(q,'="').concat(n,'" ').concat(v,"=").concat(e,'>\n                    <span class="').concat(nt,'">').concat(a(t),'</span>\n                    <span class="').concat(X,'" title="').concat((0,O.getSR)().GroupPanelItemRemove,'"></span>\n                    ').concat(vt(this.options.showGroupSetting,'<span class="'.concat(bt,'"></span>')),'\n                </li>\n                <input class="').concat(d,'" display="none"></input>')},e.prototype.zIi=function(t,e){for(;t;){if(t===e)return!0;t=t.parentElement}return!1},e.prototype.BIi=function(t,e){var n,i,o,t,u,e,n=this,i=n.vMi,o=i.getDataView(),t=t||o.ILt,u=o.aPt,e=null!=e?e:o.LEt;i.zA({cmd:E.RESET_NEW_VIEW,newConfig:{columnInfoOptions:t,includeDefaultColumns:u,options:e}}),n.ZS()},e.prototype.Cno=function(t){for(var e,n,i,o,u,r,a,c,s,l,e={},n=0;n<t.ILt.length;n++)!((i=t.ILt[n]).style instanceof k.Style)&&i.style&&i.style.cellType&&i.style.cellType instanceof H&&(e[n]||(e[n]={}),e[n].style=i.style,i.style=new k.Style(i.style)),!(i.headerStyle instanceof k.Style)&&i.headerStyle&&i.headerStyle.cellType&&i.headerStyle.cellType instanceof H&&(e[n]||(e[n]={}),e[n].headerStyle=i.headerStyle,i.headerStyle=new k.Style(i.headerStyle));for(n in o=V(t.ILt,!1),e)if(e.hasOwnProperty(n)){if((u=e[n]).style){if((r=o[n])&&r.style){for(c in a={},u.style)u.style.hasOwnProperty(c)&&(a[c]=r.style[c]);r.style=a}t.ILt[n]&&(t.ILt[n].style=u.style)}if(u.headerStyle){if((s=o[n])&&s.headerStyle){for(c in l={},u.headerStyle)u.headerStyle.hasOwnProperty(c)&&(l[c]=s.headerStyle[c]);s.headerStyle=l}t.ILt[n]&&(t.ILt[n].headerStyle=u.headerStyle)}}return o},e.prototype.dgi=function(t,e){var n,i,o,u,e,r,a,c,s,l,f,f,d,h,g,b,h,g,n=this,i,o=n.vMi.getDataView(),u=n.Cno(o),e=null!=e?e:(0,k.RF)(o.LEt),r=n.cIi,a=e&&e.cross||{};for(t&&u.push(r[r.length-1]),s=c=0;c<u.length;c++)l=void 0,N(u[c].cross)||(l=r[s],s++),l&&((f=l.cross.attributes)&&(l.cross.attributes=f.filter(function(t){return t})),f=l.cross.hgi,d=l.cross.Zc,h=l.cross.Tgi,g=l.cross.bgi,b=yt(l),h?(delete b.cross.Tgi,delete l.cross.Tgi,d?(a[d]||(n.Agi(f),b.cross.vgi=b.vgi,a[d]=b.cross),b.cross=d,l.cross=d):f&&a[f]&&(l.cross=b.cross=a[f])):g&&(n.Agi(f),(h=a[d])?(l.vgi=h.vgi,l.cross=d,b.cross=d):(delete l.cross.jgi,delete b.cross.jgi,delete l.cross.Zc,delete b.cross.Zc)),l.cross.jgi!==Z&&(g=l.cross.jgi,delete l.cross.jgi,delete b.cross.jgi,a[g]=l.cross,b.cross=g,l.cross=g),delete b.cross.hgi,delete b.cross.Tgi,delete b.cross.bgi,delete b.cross.jgi,delete l.cross.hgi,delete l.cross.Tgi,delete l.cross.bgi,delete l.cross.jgi,delete b.vgi,u[c]=b);0<Object.keys(a).length&&(e.cross=a),n.BIi(u,e),n.options.crossAreaVisible||(n.cIi=Z,n.oIi=Z,n.NIi=void 0,n.mIi=void 0)},e.prototype.Agi=function(t){var e,n,n,i,o,u,r,e=this,n=e.vMi.getDataView().LEt,n=n&&n.cross||{},i=e.cIi;if(t&&n[t]){for(u=o=0;u<i.length&&((r=i[u]).cross.hgi!==t&&r.cross!==t||!(2<=++o));u++);o<2&&delete n[t]}},e.prototype.CIi=function(t){var e,n,i,t,n,t,n,o,e=this,n=e.uIi;n&&e.zIi(t.target,e.zgi())&&e.oIi&&e.mLt(n)&&((i=e.cIi&&0===e.cIi.length)&&(e.cIi=e.lIi()),t=e.cIi,n={value:n,cross:{over:e.oIi}},t&&t.length?t.push(n):e.cIi=[n],e.NIi=e.cIi.length-1,e.IIi(),n=(t=e.cIi[e.NIi])&&t.cross,e.Cgi(n,t.value))&&(e.dgi(!0),(o=e.Tp()).t4(e.CMi.length-1,0))},e.prototype.wgi=function(t){var e,n,i,o,u,r,e,n=this.YMi,i="";if(n&&n[t])for(u=(o=n[t].level)-1,i=n[t].name,t--;0<=u&&0<=t;)(r=n[t])&&r.level===u&&(u--,i=r.name+"."+i),t--;return i},e.prototype.FIi=function(t){var t=t?"":" "+S;return'<div class="'.concat(c," ").concat(J," ").concat(at).concat(t,'">\n                    ').concat(this.ygi(),"\n                </div>")},e.prototype.ygi=function(){return'<span class="gc-table-sheet-group-header">'.concat((0,O.getSR)().CrossColumnCrossHeader,'</span>\n                <div class="').concat(x,'">').concat(this.mgi(),"</div>")},e.prototype.IIi=function(){var t;this.ZMi&&(this.Lgi(),(t=this.mgi())&&(this.ZMi.innerHTML=t),this.OMi=this.ZMi.querySelector(".".concat(y)),this.xgi())},e.prototype.mgi=function(){var t,e,u,n,i,o,r,i,a,c,s,l,f,d,h,g,i,b,v,b,M,I,b,m,N,D,j,t=this,e="",i=t.oIi,o=t.vMi.getDataView(),r=o&&o.LEt||{};if((void 0!==t.NIi&&t.cIi&&t.cIi.length||i)&&(i=t.cIi&&t.cIi[t.NIi]||{value:"",cross:{over:i}})){if(a=t.gIi(i.cross),l=(s=!pt(c=i.value))?"":"disabled",n=o.isLookupRelationField(a.over))if(u=i.vgi,a.attributes){if(!u)for(u=i.vgi=t.Sgi(a.over).map(function(e){var t=jt(a.attributes,function(t){return U(t)===U(e)}),n=-1<t;return{attribute:n?a.attributes[t]:e,checked:n}}),f=function(t){for(var e,i,n,o,e=a.attributes[t],i=jt(u,function(t){return U(t.attribute)===U(e)}),n=function(t){var e,t,n,e=a.attributes[t],t=jt(u,function(t){return U(t.attribute)===U(e)});i<t&&(n=u[t],u.splice(t,1),u.splice(i,0,n),i+=1)},o=t-1;0<=o;o--)n(o)},d=a.attributes.length-1;0<=d;d--)f(d)}else a.attributes=t.Sgi(a.over),u=i.vgi=a.attributes.map(function(t){return{attribute:t,checked:!0}});else u=o.getLookup(a.over).map(function(t){return{attribute:t=t instanceof Date?t.toString():t}});for(h="",i=g=n?"":" "+S,t.Dgi=u||[],t.mIi===Z&&(t.mIi=0),d=0;d<u.length&&c;d++)v=(b=u[d]).checked,I="",(M=b=b.attribute)!==Z&&("object"==typeof b&&(M=b.value,I=b.formatter),b=n&&d===this.mIi?" selected":"",h+='<li class="'.concat(n?A+b:"gc-table-sheet-cross-column-static-list-item",'" attribute-value="').concat(M,'" attribute-index=').concat(d,' >\n                                                <input name="').concat(M,'" class="').concat(st," ").concat(g,'" input-type="attributes" type="checkbox" value="" ').concat(v?"checked":"",' />\n                                                <span class="gc-table-sheet-cross-column-item-label">').concat(M,'</span>\n                                                <span class="').concat(lt," ").concat(g,'" title="').concat((0,O.getSR)().GroupPanelItemRemove,'"></span>\n                                                <span class="').concat(p,' gc-table-sheet-cross-column-attributes-formatter gc-table-sheet-hidden">\n                                                    <span class="').concat(C,'">').concat((0,O.getSR)().CrossColumnDetailFormatter,'</span>\n                                                    <input class="').concat(ot,'" input-type="attributes.formatter" spellcheck="false" tabIndex="-1" value="').concat(I,'"></input>\n                                                </span>\n                                          </li>'));if(m="<option></option>",N=o.LEt&&o.LEt.cross)for(D in N)N.hasOwnProperty(D)&&N[D].over===a.over&&(j=D===a.Zc?'selected="selected"':"",m+="<option value=".concat(D," ").concat(j,">").concat(D,"</option>"));e='<div class="gc-table-sheet-cross-column-detail-content">\n                                            <div class="'.concat(p,'">\n                                                <span class="').concat(C,'">').concat((0,O.getSR)().CrossColumnDetailName,'</span>\n                                                <input class="gc-table-sheet-cross-item-input-name" input-type="name" spellcheck="false" tabIndex="-1" value=\'').concat(a.Zc||"","' ").concat(l,'></input>\n                                                <select class="gc-table-sheet-cross-item-select-name" input-type="name-select" ').concat(l,">\n                                                    ").concat(m,'\n                                                </select>\n                                            </div>\n                                            <div class="').concat(p,'">\n                                                <span class="').concat(C,'">').concat((0,O.getSR)().CrossColumnDetailValue,'</span>\n                                                <input class="').concat(ct," ").concat(T,'" input-type="value" spellcheck="false" tabIndex="-1" readonly="readonly" maxlength="0" placeholder="').concat((0,O.getSR)().CrossColumnDetailValuePlaceHolder,"\" value='").concat(c,'\'></input>\n                                            </div>\n                                            <div class="gc-table-sheet-summary-item-formula">\n                                                <span class="').concat(C,'">').concat((0,O.getSR)().CrossColumnDetailOver,'</span>\n                                                <input class="').concat(ct," ").concat(T,'" input-type="over" spellcheck="false" tabIndex="-1" readonly="readonly" maxlength="0" value=\'').concat(a.over,"'></input>\n                                                \x3c!--input class=\"").concat(w,'" input-type="over" spellcheck="false" tabIndex="-1" value=\'').concat(a.over||"","'></input--\x3e\n                                            </div>\n                                            <div class=\"").concat(p,'">\n                                                <span class="').concat(C,'">').concat((0,O.getSR)().CrossColumnDetailCaption,'</span>\n                                                <input class="').concat(T,'" input-type="header" spellcheck="false" tabIndex="-1" value=\'').concat(a.caption||"","' ").concat(l,'></input>\n                                            </div>\n                                            <div class="').concat(p,' gc-table-sheet-cross-column-value-caption">\n                                                <span class="').concat(C,'"></span>\n                                                <input name="showCrossValueHeader" class="').concat(st,' gc-table-sheet-cross-column-show-value-header" input-type="showCrossValueHeader" type="checkbox" value="" ').concat(r.showCrossValueHeader?"checked":""," ").concat(l,' />\n                                                <span class="').concat(C,' gc-table-sheet-cross-column-value-caption-label">').concat((0,O.getSR)().CrossColumnDetailValueHeader,'</span>\n                                            </div>\n                                            <div class="').concat(p).concat(i,'">\n                                                <span class="').concat(C,'">').concat((0,O.getSR)().CrossColumnDetailFilter,'</span>\n                                                <input class="').concat(T,'" input-type="filter" spellcheck="false" tabIndex="-1" value=\'').concat(n&&a.filter||"","' ").concat(l,'></input>\n                                            </div>\n                                            <div class="gc-table-sheet-cross-column-attributes-list-container">\n                                            <span class="').concat(C,'">').concat(n?(0,O.getSR)().CrossColumnDetailAttributes:(0,O.getSR)().CrossColumnDetailList,'</span>\n                                                <ul class="').concat(y,'">\n                                                    ').concat(h,"\n                                                </ul>\n                                            </div>\n                                        </div>")}return e},e.prototype.zgi=function(){return this.ZMi&&this.ZMi.querySelector(".".concat(ct))},e.prototype.WIi=function(t,e,n){for(var i,o,u,r,a,i=this,r=0;r<e.length;r++)0===(a=e[r]).level&&(a.name===t?o=r+1:N(o)||(u=r));return e.slice(o,u).map(function(t){var e=N(t.cross)&&(n.isLookupField(t.name)||n.isLookupField(t.fieldString));return{value:i.$t({value:t.name}),nIi:t.fieldString,textIndent:t.level+1,checkStatus:!1,yBt:e}})},e.prototype.WMi=function(){var t,e,n,n,f,t,e,n=this.vMi.getDataView(),n=n&&n.gLt,f=function(t,e,n,i,o){var u,r,a,c,s,l;if(t&&t.Pq){for(u=[],r=0,a=t.Pq;r<a.length;r++)(c=a[r]).targetTable?u.push(c):n.push({level:e,name:c.name,isLeafNode:!0,field:c,hostTable:t,index:n.length,fieldString:o.concat(c.name).join(".")});for(i.push(t.name),s=0,l=u;s<l.length;s++)(c=l[s]).targetTable&&-1===i.indexOf(c.targetTable.name)&&(n.push({level:e,name:c.name,fieldString:o.concat(c.name).join(".")}),o.push(c.name),f(c.targetTable,e+1,n,i,o),o.pop())}return n};return f(n,0,[],[],[])},e.prototype.xgi=function(){var m,n,i,o,t,e,u,r,a,c,m=this,o=m.pMi,t,e=function(t){var e=t.target,t=t.target.classList;t.contains(A)||(e=e.parentElement),t.contains(lt)&&m.Zgi(e)},r=function(t){var e,n,i,o,e=t.target,n=(0,k.GC$)(t.target),i=n.attr(z),o;t.target.classList.contains(A)||(e=e.parentElement,i=n.parent().attr(z)),!N(i)&&e.classList.contains(A)&&(u=m.LMi.getBoundingClientRect(),m.Ogi=e,m.Egi=e,m.mIi!==+i)&&(m.mIi=+i,m.Ggi(e))},a=function(t){var e,n,n,t,i,n,i,n,e=m.mIi,n=m.Ogi;!N(e)&&n&&(n=n.getAttribute("attribute-value"),m.AIi(t,n,u,m.LMi),n=!(m.Ti.style.cursor="move"),(i=(t=t.target).parentElement)&&-1<i.className.indexOf(A)?(t=i,n=!0):-1<t.className.indexOf(A)&&(n=!0),i=m.Egi,n)&&i!==t&&((n=t.getAttribute(z))<e?(i.className=i.className.replace(D,"").replace(j,"").trim(),t.className=t.className+" ".concat(D)):e<n?(i.className=i.className.replace(D,"").replace(j,"").trim(),t.className=t.className+" ".concat(j)):i.className=i.className.replace(D,"").replace(j,"").trim(),m.Egi=t)},c=function(t){var e,e,n,i,t,o,o,t,t,i,t,e=m.cIi,e=e&&e[m.NIi],n=e&&e.cross,i=m.mIi,t=t.target,o=m.Egi,o=o&&+(0,k.GC$)(o).attr(z);i!==Z&&m.Ogi&&o!==Z&&o!==i&&(m.Egi.className=m.Egi.className.replace(D,"").replace(j,"").trim(),m.zIi(t,m.OMi)&&("string"==typeof n&&(t=n,(n=yt(m.gIi(n))).jgi=t,e.cross=n),t=e.vgi.splice(i,1)[0],e.vgi.splice(o,0,t),m.mIi=+o,i=[],i=e.vgi.filter(function(t){return t.checked}).map(function(t){return t&&t.formatter?{formatter:t.formatter,value:t.attribute}:t.attribute}),n.attributes=i,n.vgi=e.vgi),m.Cgi(n,e.value))&&(m.dgi(),m.IIi()),m.Egi=Z,m.Ogi=Z,m.Ti.style.cursor="default",(t=m.SIi)&&(t.style.display="none")};m.pgi=function(t){var e,n,t,i,o,u,t,r,a,c,s,e,l,f,d,h,g,b,v,M,I,n=(0,k.GC$)(t.target),t=m.NIi,i,o=m.cIi[t],u=o&&o.cross,t=n.attr("input-type"),r=n.val().trim(),a=!1;if(u){switch(c=m.vMi.getDataView(),s=null!=(e=(0,k.RF)(c.LEt))?e:{},"string"==typeof u&&(e=u,(u=yt(m.gIi(u))).jgi=e,o.cross=u,o.vgi=u.vgi),t){case"name":""===r||(l=c.LEt.cross)&&l.hasOwnProperty(r)?u.bgi=!0:u.Tgi=!0,u.hgi=u.Zc,u.Zc=m.kgi(u,r),a=!0;break;case"name-select":u.hgi=u.Zc,u.Zc=r,a=u.bgi=!0;break;case"over":u.over=r,m.Ugi(r);break;case"header":u.caption=pt(r)?Z:r;break;case"filter":u.filter=r;break;case"showCrossValueHeader":s.showCrossValueHeader=!s.showCrossValueHeader;break;case"attributes":if(f=[],d=o.vgi,n.parent().parent()[0].querySelectorAll("li").forEach(function(t,e){var n,e,n,n=t.querySelector(".".concat(st));d[e].checked=n.checked,n.checked&&(e=(0,k.GC$)(n).attr("name"),n=(0,k.GC$)(t.querySelector('[input-type="attributes.formatter"]')).val(),N(n)||""===n?f.push(e):f.push({value:e,formatter:n}))}),0===f.length)return void n.prop("checked",!0);u.attributes=f;break;case"attributes.formatter":for(h=o.vgi,g=n.parent().parent().attr("attribute-value"),b=function(e){return jt(h,function(t){var t=t.attribute;return e===("string"==typeof t?t:t.value)})},v=0;v<u.attributes.length;v++)(M=u.attributes[v])!==g&&M.value!==g||("string"==typeof M?(I=b(M),h[I].attribute=u.attributes[v]={value:M,formatter:r}):(I=b(M.value),u.attributes[v].formatter=r,h[I].attribute=u.attributes[v]))}m.Cgi(u,o.value)&&m.dgi(!1,s),a&&m.IIi()}},m.Bgi=e,m.Qgi=r,m.Rgi=a,m.Pgi=c,(0,k.GC$)(".".concat(x)).bind("change",m.pgi),(0,k.GC$)(".".concat(y)).bind("click",m.Bgi),(0,k.GC$)(".".concat(y)).bind("mousedown",m.Qgi).bind("mousemove",m.Rgi),document.addEventListener("mouseup",m.Pgi),(0,k.GC$)(".".concat(ft)).bind("focus",function(t){var t=t.target,e=(0,k.GC$)(t).val()||"=";(n=new P.FormulaTextBox(t,g,g)).Ngi=!0,i=new k.Workbook(o,{allowDynamicArray:!0,allowUndo:!1}),m.XMi(i),m.lgi(i),n.workbook(i),n.text(e)}).bind("blur",function(t){var t=(0,k.GC$)(t.target),e;"="===t.val()&&t.val(""),m.ggi(n,i)}).bind("keydown",function(t){13===t.keyCode&&(m.ggi(n,i),t.target.blur())})},e.prototype.Lgi=function(){var t=this;t.ZMi&&(t.pgi&&(0,k.GC$)(".".concat(x)).unbind("change"),t.Bgi&&(0,k.GC$)(".".concat(y)).unbind("click"),t.Qgi&&(0,k.GC$)(".".concat(y)).unbind("mousedown").unbind("mousemove"),t.Pgi&&document.removeEventListener("mouseup",t.Pgi),(0,k.GC$)(".".concat(ft)).unbind("focus").unbind("blur").unbind("keydown"))},e.prototype.eIi=function(){return this.vMi.getDataView()},e.prototype.mLt=function(t){var e=this;return e.eIi().mLt(e.oIi,t)},e.prototype.Cgi=function(t,e){var n,i=this.eIi();return t&&t.over&&e&&(i.isLookupListField(t.over)||t.attributes&&0<t.attributes.length)},e.prototype.lIi=function(){return(0,k.RF)(this.eIi().ILt.filter(function(t){return!N(t.cross)}))},e.prototype.gIi=function(t){var e,n,i,i,e;return"string"==typeof t&&(n=t,(i=this.eIi()).LEt.cross)&&((i=i.LEt.cross[n]||{}).Zc=n,t=i),t},e.prototype.kgi=function(t,e){var n,i,e,i,n=this,i=n.eIi(),e=e;return i.LEt.cross&&(i=i.LEt.cross[e])&&i.over!==n.gIi(t).over?e+"1":e},e.prototype.Zgi=function(t){var e,n,e=t.className,n=t.querySelector(".gc-table-sheet-cross-column-attributes-formatter");-1<e.indexOf(dt)?(t.className=t.className.replace(dt,""),n.className+=" "+S):(t.className+=" ".concat(dt),n.className=n.className.replace(S,""),setTimeout(function(){n.querySelector("input").focus()}))},e.prototype.Ggi=function(t){var e,n,i,o,u,e,n,i=this.ZMi.querySelectorAll(".".concat(A));for(o in i)i.hasOwnProperty(o)&&((u=i[o]).className=u.className.replace("selected","").trim(),u===t)&&(u.className=u.className+" selected")},e.prototype.Ugi=function(t){var e,t,e=this,t=e.Sgi(t);e.cIi[e.NIi].cross.attributes=t,e.mIi=0,e.IIi()},e.prototype.Sgi=function(t){var e,t,n,i,o,u,t,r,a,c,s,c,l,f,d,e=this,t=e.Ygi(t),n=[];if(t&&t.hostTable&&t.field&&(u="",o=(i=t.hostTable).tBt(t.field.name))&&o.Pq){for(t=o.Pq,r=e.YMi,a=0;a<r.length;a++)s="",(c=(s=(c=r[a]).hostTable===o?c.fieldString!==Z?c.fieldString:c.fieldString=e.wgi(a):s)&&s.split("."))&&1<c.length&&(u=(c=c.slice(0,c.length-1)).join(".")+".");for(l=0,f=t;l<f.length;l++)(d=f[l]).targetTable||n.push(u+d.name)}return n},e.prototype.Ygi=function(t){var e,n,t,i,o;if(t)return n=this.YMi,t=t.split("."),i=t.length-1,o=t[i],n.filter(function(t){return t.name===o&&t.level===i})[0]},e.prototype.attach=function(t){this.Ti&&t&&(((this.vMi=t).UMi=this).mMi&&this.Hc(t,this.mMi),this.updatePanelLayout())},e.prototype.detach=function(){var t,e,t=this;t.vMi&&(t.vMi.UMi=g,t.vMi.unbind(n),t.vMi=g,document.removeEventListener("mousemove",t.bIi,!0),document.removeEventListener("mouseup",t.vIi,!0),(0,k.GC$)(".".concat(ut)).unbind("click"),t.SWt(),t.XIi(),t.Lgi(),t.tgi(),t.mMi&&(t.mMi.removeEventListener("mousedown",t.DIi,!0),e=(0,k.GC$)(t.mMi).data("workbook"))&&(this.BMi=null,e.getSheet(0).unbind(n),e.destroy()),t.JMi&&(r.clearTimeout(t.JMi),t.JMi=0),t.cIi=Z,t.oIi=Z,t.YMi=Z,t.Dgi=Z,t.uIi=Z,t.NIi=Z,t.AMi=!0,t.sIi(!1),t.MIi(!0))},e.prototype.destroy=function(){var t,e;this.detach(),t=this.Ti,(0,k.GC$)(t).data(u,g),(e=this.SIi)&&e.parentElement&&(e.parentElement.removeChild(e),this.SIi=void 0),t&&(t.innerHTML=""),this.Ti=g},e.findControl=function(t){var e=t;return"string"==typeof t&&(e=document.getElementById(t)),(0,k.GC$)(e).data(u)},M.TableSheetPanel=e},"./dist/plugins/tableSheet/tableSheet-row-action.js":function(Z,t,e){var n,i,o,D,f,e,o,j,d,s,u,r,a,g,U,c,e,l;function h(t){return"number"==typeof t}function b(t){return"string"==typeof t}function v(t){return t instanceof Function}function M(t){return"object"==typeof t&&!j(t)}function I(t){return t instanceof Array}function m(t){return"boolean"==typeof t}function N(t){return b(t)?"ROWACTION_".concat(t):t}function C(t,e){var n=l.call(this)||this;return n.Wgi=[],n.MU=t,n.DJ=e,n}function w(t,e){M(e)?(t.imageType=e.imageType,t.imageSrc=e.imageSrc,t.imageSize=e.imageSize):(t.imageType=s,t.imageSrc=s,t.imageSize=s)}function p(t,e,n,i,o,u,r){var t=S(t,e,n,i,r);if(!j(t))return o[u]=new D.Rect(t.x,t.y,t.width,t.height),o[u].offsetX=t.x-r.x,o[u].offsetY=t.y-r.y,o[u]}function x(t,e,n,i,o,u){y(t,e.x,e.y,e.width,e.height,n,i,o,u)}function y(t,e,n,i,o,u,r,a,c){var s,l;t.save(),t.rect(e,n,i,o),t.clip(),t.beginPath(),(l=k(u,(s=a.dataRange?a.dataRange.convertContext(a):a).row))&&A(t,u,e,n,i,o,l,a,c,f.middleCenter),t.restore()}function A(t,e,n,i,o,u,r,a,c,s){var a,l,a,n,i,o,f,u,s,n,d,e,h;t.save(),t.rect(n,i,o,u),t.clip(),t.beginPath(),(l=(a=a.sheet).BO()).getState(r)?l.qh(r)&&(a=a.zoom(),n=new D.Rect(n,i,o,u),i=L(e),o=R(e),u=T(s,n,f=new D.Rect(0,0,i*a,o*a)),n=(s=l.getImage(r)).width,d=s.height,D.mt.Tt.msie&&e.imageType&&(d=e.imageType===D.ButtonImageType.custom?(g[r]||(s.style.position="absolute",s.style.left="-10000px",s.style.top="-100000px",document.body.appendChild(s),g[r]={width:s.width,height:s.height},document.body.removeChild(s)),n=g[r].width,g[r].height):n=12),c&&D.mt.Tt.msie&&u.x+u.width>c.x+c.width?0<(e=c.x+c.width-u.x)&&t.drawImage(s,0,0,e*(h=n/(i*a)),d,u.x,u.y,e,o*a):t.drawImage(s,0,0,n,d,u.x,u.y,i*a,o*a)):l.addImage(r,"anonymous"),t.restore()}function T(t,e,n){var i=Math.max(0,(e.width-n.width)/2),o=Math.max(0,(e.height-n.height)/2);return t===f.leftCenter&&(i=0),new D.Rect(e.x+i,e.y+o,Math.min(n.width,e.width),Math.min(n.height,e.height))}function S(t,e,n,i,o){if(!(t+n<o.x||t>o.x+o.width||e+i<o.y||e>o.y+o.height))return t+n>o.x+o.width&&(n=o.x+o.width-t),e+i>o.y+o.height&&(i=o.y+o.height-e),t<o.x&&(t=o.x),e<o.y&&(e=o.y),new D.Rect(t,e,n,i)}function z(t,e){return h(t)?{imageType:t}:b(t)?{imageType:D.ButtonImageType.custom,imageSrc:t,imageSize:G(e)}:s}function G(t){return h(t)?{width:t,height:t}:t}function B(t,e,n,i){var n,o;return void 0===i&&(i=!0),n=n.sheet.zoom(),j(t.width)?o=L(t):(o=u(1,t.width),isNaN(o)&&(o=1)),i?o*n:o}function L(t){var e;return e=(e=t.imageSize&&("string"==typeof t.imageSize.width&&(e=r(t.imageSize.width)),"number"==typeof t.imageSize.width)?t.imageSize.width:e)===d||isNaN(e)||e<0?12:e}function R(t){var e;return e=(e=t.imageSize&&("string"==typeof t.imageSize.height&&(e=r(t.imageSize.height)),"number"==typeof t.imageSize.height)?t.imageSize.height:e)===d||isNaN(e)||e<0?12:e}function O(t,e,n,i,o){var u=t.Ut.getValueForKey(e,n,a,i);return j(u)&&P(t,e,n,i,u={layout:{}}),u}function P(t,e,n,i,o){t.Ut.setValueForKey(e,n,a,o,i)}function Q(t,e,n,i,o){var u;delete O(e,n,i,o).layout[t]}function Y(t,e,n){for(var i,o,u,i=e.BO(),o=0;o<t.length;o++)b(u=k(t[o].option,n))&&i.addImage(u,"anonymous")}function k(t,e){var n,i,e,o,n=null,i=t.imageType;if(j(t.imageType))return null;switch(o=(e=E(t.enabled,e))||j(e),i){case D.ButtonImageType.dropdown:n=o?"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+ZHJvcGRvd248L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZyBpZD0iZHJvcGRvd24iIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwb2x5Z29uIGZpbGw9IiM2RTZFNkUiIHBvaW50cz0iMyA0IDEwIDQgNi41IDkiPjwvcG9seWdvbj4KICAgIDwvZz4KPC9zdmc+":"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+ZHJvcGRvd248L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZyBpZD0iZHJvcGRvd24iIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwb2x5Z29uIGZpbGw9IiNDQUNBQ0EiIHBvaW50cz0iMyA0IDEwIDQgNi41IDkiPjwvcG9seWdvbj4KICAgIDwvZz4KPC9zdmc+";break;case D.ButtonImageType.left:n=o?"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+bGVmdDwvdGl0bGU+CiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz4KICAgIDxnIGlkPSJsZWZ0IiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8cG9seWdvbiBmaWxsPSIjNkU2RTZFIiBmaWxsLXJ1bGU9Im5vbnplcm8iIHBvaW50cz0iNS4zNTE1MjcxMiA2LjUgOCA5LjI4ODYzMDM2IDcuMzI0MzU0MDMgMTAgNCA2LjUgNy4zMjQzNTQwMyAzIDggMy43MTEzNjk2NCI+PC9wb2x5Z29uPgogICAgPC9nPgo8L3N2Zz4=":"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+bGVmdDwvdGl0bGU+CiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz4KICAgIDxnIGlkPSJsZWZ0IiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8cG9seWdvbiBmaWxsPSIjQ0FDQUNBIiBmaWxsLXJ1bGU9Im5vbnplcm8iIHBvaW50cz0iNS4zNTE1MjcxMiA2LjUgOCA5LjI4ODYzMDM2IDcuMzI0MzU0MDMgMTAgNCA2LjUgNy4zMjQzNTQwMyAzIDggMy43MTEzNjk2NCI+PC9wb2x5Z29uPgogICAgPC9nPgo8L3N2Zz4=";break;case D.ButtonImageType.right:n=o?"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+cmlnaHQ8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZyBpZD0icmlnaHQiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwb2x5Z29uIGZpbGw9IiM2RTZFNkUiIGZpbGwtcnVsZT0ibm9uemVybyIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoNi4wMDAwMDAsIDYuNTAwMDAwKSBzY2FsZSgtMSwgMSkgdHJhbnNsYXRlKC02LjAwMDAwMCwgLTYuNTAwMDAwKSAiIHBvaW50cz0iNS4zNTE1MjcxMiA2LjUgOCA5LjI4ODYzMDM2IDcuMzI0MzU0MDMgMTAgNCA2LjUgNy4zMjQzNTQwMyAzIDggMy43MTEzNjk2NCI+PC9wb2x5Z29uPgogICAgPC9nPgo8L3N2Zz4=":"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+cmlnaHQ8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZyBpZD0icmlnaHQiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwb2x5Z29uIGZpbGw9IiNDQUNBQ0EiIGZpbGwtcnVsZT0ibm9uemVybyIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoNi4wMDAwMDAsIDYuNTAwMDAwKSBzY2FsZSgtMSwgMSkgdHJhbnNsYXRlKC02LjAwMDAwMCwgLTYuNTAwMDAwKSAiIHBvaW50cz0iNS4zNTE1MjcxMiA2LjUgOCA5LjI4ODYzMDM2IDcuMzI0MzU0MDMgMTAgNCA2LjUgNy4zMjQzNTQwMyAzIDggMy43MTEzNjk2NCI+PC9wb2x5Z29uPgogICAgPC9nPgo8L3N2Zz4=";break;case D.ButtonImageType.clear:n=o?"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+Y2xlYXI8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZyBpZD0iY2xlYXIiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwb2x5Z29uIGZpbGw9IiM2RTZFNkUiIGZpbGwtcnVsZT0ibm9uemVybyIgcG9pbnRzPSI4Ljg3NTQ2MzkgMyAxMCA0LjEyNDUzNjEgNy42MjQ1MzYxIDYuNSAxMCA4Ljg3NTQ2MzkgOC44NzU0NjM5IDEwIDYuNSA3LjYyNDUzNjEgNC4xMjQ1MzYxIDEwIDMgOC44NzU0NjM5IDUuMzc1NDYzOSA2LjUgMyA0LjEyNDUzNjEgNC4xMjQ1MzYxIDMgNi41IDUuMzc1NDYzOSI+PC9wb2x5Z29uPgogICAgPC9nPgo8L3N2Zz4=":"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+Y2xlYXI8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZyBpZD0iY2xlYXIiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwb2x5Z29uIGZpbGw9IiNDQUNBQ0EiIGZpbGwtcnVsZT0ibm9uemVybyIgcG9pbnRzPSI4Ljg3NTQ2MzkgMyAxMCA0LjEyNDUzNjEgNy42MjQ1MzYxIDYuNSAxMCA4Ljg3NTQ2MzkgOC44NzU0NjM5IDEwIDYuNSA3LjYyNDUzNjEgNC4xMjQ1MzYxIDEwIDMgOC44NzU0NjM5IDUuMzc1NDYzOSA2LjUgMyA0LjEyNDUzNjEgNC4xMjQ1MzYxIDMgNi41IDUuMzc1NDYzOSI+PC9wb2x5Z29uPgogICAgPC9nPgo8L3N2Zz4=";break;case 3:n=o?"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+Y2FuY2VsPC90aXRsZT4KICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPgogICAgPGcgaWQ9ImNhbmNlbCIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPHBhdGggZD0iTTYsMSBDOC43NjE0MzE3MSwxIDExLDMuMjM4NTgxMzIgMTEsNiBDMTEsOC43NjE0MzE3MSA4Ljc2MTQzMTcxLDExLjAwMDAxMyA2LjAwMDAwNjUyLDExLjAwMDAxMyBDMy4yMzg1ODEzMiwxMS4wMDAwMTMgMSw4Ljc2MTQ0NDc0IDEsNiBDMSwzLjIzODU2ODI5IDMuMjM4NTgxMzIsMSA2LDEgWiBNOC41MTgwNTU5NiwzLjQ4MTk0NDA0IEM4LjI1MTg4NTYxLDMuMjE1NzczNyA3LjgyMDMzODIyLDMuMjE1NzczNyA3LjU1NDE2Nzg4LDMuNDgxOTQ0MDQgTDYsNS4wMzYxMTE5MiBMNC40NDU4MzIxMiwzLjQ4MTk0NDA0IEM0LjE3OTY2MTc4LDMuMjE1NzczNyAzLjc0ODExNDM5LDMuMjE1NzczNyAzLjQ4MTk0NDA0LDMuNDgxOTQ0MDQgQzMuMjE1NzczNywzLjc0ODExNDM5IDMuMjE1NzczNyw0LjE3OTY2MTc4IDMuNDgxOTQ0MDQsNC40NDU4MzIxMiBMNS4wMzYxMTE5Miw2IEwzLjQ4MTk0NDA0LDcuNTU0MTY3ODggQzMuMjE1NzczNyw3LjgyMDMzODIyIDMuMjE1NzczNyw4LjI1MTg4NTYxIDMuNDgxOTQ0MDQsOC41MTgwNTU5NiBDMy43NDgxMTQzOSw4Ljc4NDIyNjMgNC4xNzk2NjE3OCw4Ljc4NDIyNjMgNC40NDU4MzIxMiw4LjUxODA1NTk2IEw2LDYuOTYzODg4MDggTDcuNTU0MTY3ODgsOC41MTgwNTU5NiBDNy44MjAzMzgyMiw4Ljc4NDIyNjMgOC4yNTE4ODU2MSw4Ljc4NDIyNjMgOC41MTgwNTU5Niw4LjUxODA1NTk2IEM4Ljc4NDIyNjMsOC4yNTE4ODU2MSA4Ljc4NDIyNjMsNy44MjAzMzgyMiA4LjUxODA1NTk2LDcuNTU0MTY3ODggTDYuOTYzODg4MDgsNiBMOC41MTgwNTU5Niw0LjQ0NTgzMjEyIEM4Ljc4NDIyNjMsNC4xNzk2NjE3OCA4Ljc4NDIyNjMsMy43NDgxMTQzOSA4LjUxODA1NTk2LDMuNDgxOTQ0MDQgWiIgaWQ9IuW9oueKtue7k+WQiCIgZmlsbD0iIzZFNkU2RSIgZmlsbC1ydWxlPSJub256ZXJvIj48L3BhdGg+CiAgICA8L2c+Cjwvc3ZnPg==":"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+Y2FuY2VsPC90aXRsZT4KICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPgogICAgPGcgaWQ9ImNhbmNlbCIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPHBhdGggZD0iTTYsMSBDOC43NjE0MzE3MSwxIDExLDMuMjM4NTgxMzIgMTEsNiBDMTEsOC43NjE0MzE3MSA4Ljc2MTQzMTcxLDExLjAwMDAxMyA2LjAwMDAwNjUyLDExLjAwMDAxMyBDMy4yMzg1ODEzMiwxMS4wMDAwMTMgMSw4Ljc2MTQ0NDc0IDEsNiBDMSwzLjIzODU2ODI5IDMuMjM4NTgxMzIsMSA2LDEgWiBNOC41MTgwNTU5NiwzLjQ4MTk0NDA0IEM4LjI1MTg4NTYxLDMuMjE1NzczNyA3LjgyMDMzODIyLDMuMjE1NzczNyA3LjU1NDE2Nzg4LDMuNDgxOTQ0MDQgTDYsNS4wMzYxMTE5MiBMNC40NDU4MzIxMiwzLjQ4MTk0NDA0IEM0LjE3OTY2MTc4LDMuMjE1NzczNyAzLjc0ODExNDM5LDMuMjE1NzczNyAzLjQ4MTk0NDA0LDMuNDgxOTQ0MDQgQzMuMjE1NzczNywzLjc0ODExNDM5IDMuMjE1NzczNyw0LjE3OTY2MTc4IDMuNDgxOTQ0MDQsNC40NDU4MzIxMiBMNS4wMzYxMTE5Miw2IEwzLjQ4MTk0NDA0LDcuNTU0MTY3ODggQzMuMjE1NzczNyw3LjgyMDMzODIyIDMuMjE1NzczNyw4LjI1MTg4NTYxIDMuNDgxOTQ0MDQsOC41MTgwNTU5NiBDMy43NDgxMTQzOSw4Ljc4NDIyNjMgNC4xNzk2NjE3OCw4Ljc4NDIyNjMgNC40NDU4MzIxMiw4LjUxODA1NTk2IEw2LDYuOTYzODg4MDggTDcuNTU0MTY3ODgsOC41MTgwNTU5NiBDNy44MjAzMzgyMiw4Ljc4NDIyNjMgOC4yNTE4ODU2MSw4Ljc4NDIyNjMgOC41MTgwNTU5Niw4LjUxODA1NTk2IEM4Ljc4NDIyNjMsOC4yNTE4ODU2MSA4Ljc4NDIyNjMsNy44MjAzMzgyMiA4LjUxODA1NTk2LDcuNTU0MTY3ODggTDYuOTYzODg4MDgsNiBMOC41MTgwNTU5Niw0LjQ0NTgzMjEyIEM4Ljc4NDIyNjMsNC4xNzk2NjE3OCA4Ljc4NDIyNjMsMy43NDgxMTQzOSA4LjUxODA1NTk2LDMuNDgxOTQ0MDQgWiIgaWQ9IuW9oueKtue7k+WQiCIgZmlsbD0iI0NBQ0FDQSIgZmlsbC1ydWxlPSJub256ZXJvIj48L3BhdGg+CiAgICA8L2c+Cjwvc3ZnPg==";break;case D.ButtonImageType.ok:n=o?"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+b2s8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZyBpZD0ib2siIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwYXRoIGQ9Ik01LjY3NjUyMjQsMTAgQzUuNjc2NTIyNCwxMCA2LjkxNzAzMjUzLDcuMzM0ODI5MDcgOC40MTk2ODA3OSw1LjMyNjE2ODAyIEM5LjU4MDA5Mzk2LDMuNzc2MzU0NDYgMTEsMi43MTg3NTQxMSAxMSwyLjcxODc1NDExIEwxMC41MTYxMDg0LDIgQzEwLjUxNjEwODQsMiA4Ljk2NzEyNTYsMi43MDIwNjg3NSA3LjU3NzY3MjIzLDQuMDUwMzc0NDYgQzYuMTY4MzYwMTEsNS40MTcyOTA3NiA1LjM0NDg4MTIxLDYuOTQ2NTczNSA1LjM0NDg4MTIxLDYuOTQ2NTczNSBMMy4yOTc0Mzg1Niw1LjAxMTcxMzA5IEwyLDYuNDYwMTI1OTcgTDUuNjc2NTE5ODEsOS45OTk5ODc0NyBMNS42NzY1MTk4MSw5Ljk5OTk4NzQ3IEw1LjY3NjUyMjQsMTAgWiIgaWQ9Ik9LIiBmaWxsPSIjNkU2RTZFIiBmaWxsLXJ1bGU9Im5vbnplcm8iPjwvcGF0aD4KICAgIDwvZz4KPC9zdmc+":"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+b2s8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZyBpZD0ib2siIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwYXRoIGQ9Ik01LjY3NjUyMjQsMTAgQzUuNjc2NTIyNCwxMCA2LjkxNzAzMjUzLDcuMzM0ODI5MDcgOC40MTk2ODA3OSw1LjMyNjE2ODAyIEM5LjU4MDA5Mzk2LDMuNzc2MzU0NDYgMTEsMi43MTg3NTQxMSAxMSwyLjcxODc1NDExIEwxMC41MTYxMDg0LDIgQzEwLjUxNjEwODQsMiA4Ljk2NzEyNTYsMi43MDIwNjg3NSA3LjU3NzY3MjIzLDQuMDUwMzc0NDYgQzYuMTY4MzYwMTEsNS40MTcyOTA3NiA1LjM0NDg4MTIxLDYuOTQ2NTczNSA1LjM0NDg4MTIxLDYuOTQ2NTczNSBMMy4yOTc0Mzg1Niw1LjAxMTcxMzA5IEwyLDYuNDYwMTI1OTcgTDUuNjc2NTE5ODEsOS45OTk5ODc0NyBMNS42NzY1MTk4MSw5Ljk5OTk4NzQ3IEw1LjY3NjUyMjQsMTAgWiIgaWQ9Ik9LIiBmaWxsPSIjQ0FDQUNBIiBmaWxsLXJ1bGU9Im5vbnplcm8iPjwvcGF0aD4KICAgIDwvZz4KPC9zdmc+";break;case D.ButtonImageType.plus:n=o?"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+cGx1czwvdGl0bGU+CiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz4KICAgIDxnIGlkPSJwbHVzIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8cGF0aCBkPSJNNSw1IEw1LDMgTDcsMyBMNyw1IEw5LDUgTDksNyBMNyw3IEw3LDkgTDUsOSBMNSw3IEwzLDcgTDMsNSBMNSw1IFoiIGZpbGw9IiM2RTZFNkUiPjwvcGF0aD4KICAgIDwvZz4KPC9zdmc+":"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+cGx1czwvdGl0bGU+CiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz4KICAgIDxnIGlkPSJwbHVzIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8cGF0aCBkPSJNNSw1IEw1LDMgTDcsMyBMNyw1IEw5LDUgTDksNyBMNyw3IEw3LDkgTDUsOSBMNSw3IEwzLDcgTDMsNSBMNSw1IFoiIGZpbGw9IiNDQUNBQ0EiPjwvcGF0aD4KICAgIDwvZz4KPC9zdmc+";break;case D.ButtonImageType.minus:n=o?"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+bWludXM8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZyBpZD0ibWludXMiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxyZWN0IGZpbGw9IiM2RTZFNkUiIHg9IjMiIHk9IjUiIHdpZHRoPSI2IiBoZWlnaHQ9IjIiPjwvcmVjdD4KICAgIDwvZz4KPC9zdmc+":"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+bWludXM8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZyBpZD0ibWludXMiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxyZWN0IGZpbGw9IiNDQUNBQ0EiIHg9IjMiIHk9IjUiIHdpZHRoPSI2IiBoZWlnaHQ9IjIiPjwvcmVjdD4KICAgIDwvZz4KPC9zdmc+";break;case D.ButtonImageType.redo:n=o?"data:image/svg+xml;base64,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":"data:image/svg+xml;base64,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";break;case D.ButtonImageType.undo:n=o?"data:image/svg+xml;base64,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":"data:image/svg+xml;base64,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";break;case D.ButtonImageType.search:n=o?"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+c2VhcmNoPC90aXRsZT4KICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPgogICAgPGcgaWQ9InNlYXJjaCIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPHBhdGggZD0iTTcuNDQ1MzczOTYsNi43MzgyNjcxOCBMMTAuMDM1NTMzOSw5LjMyODQyNzEyIEw5LjMyODQyNzEyLDEwLjAzNTUzMzkgTDYuNzM4MjY3MTgsNy40NDUzNzM5NiBDNi4yNDc4NTc3NCw3Ljc5NDYwNDUyIDUuNjQ3OTE1MjIsOCA1LDggQzMuMzQzMTQ1NzUsOCAyLDYuNjU2ODU0MjUgMiw1IEMyLDMuMzQzMTQ1NzUgMy4zNDMxNDU3NSwyIDUsMiBDNi42NTY4NTQyNSwyIDgsMy4zNDMxNDU3NSA4LDUgQzgsNS42NDc5MTUyMiA3Ljc5NDYwNDUyLDYuMjQ3ODU3NzQgNy40NDUzNzM5Niw2LjczODI2NzE4IFogTTUuMDAwNTI2MTYsMyBDMy44OTYyNTEzMSwzIDMuMDAxMDUyNDgsMy44OTU0MzQ1NiAzLDUgQzMuMDAxMTYyODYsNi4xMDM2NzQ4NCAzLjg5NTAyNzk5LDYuOTk4MjUxNzQgNS4wMDA1MjYxNiw3IEM2LjEwNDgwMTAxLDcgNi45OTk5OTk4Myw2LjEwNDU2NTQ0IDcsNSBDNywzLjg5NTQzNDU2IDYuMTA0ODAxMDEsMy4wMDAwMDAxNyA1LjAwMDUyNjE2LDMgWiIgZmlsbD0iIzZFNkU2RSIgZmlsbC1ydWxlPSJub256ZXJvIj48L3BhdGg+CiAgICA8L2c+Cjwvc3ZnPg==":"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+c2VhcmNoPC90aXRsZT4KICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPgogICAgPGcgaWQ9InNlYXJjaCIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPHBhdGggZD0iTTcuNDQ1MzczOTYsNi43MzgyNjcxOCBMMTAuMDM1NTMzOSw5LjMyODQyNzEyIEw5LjMyODQyNzEyLDEwLjAzNTUzMzkgTDYuNzM4MjY3MTgsNy40NDUzNzM5NiBDNi4yNDc4NTc3NCw3Ljc5NDYwNDUyIDUuNjQ3OTE1MjIsOCA1LDggQzMuMzQzMTQ1NzUsOCAyLDYuNjU2ODU0MjUgMiw1IEMyLDMuMzQzMTQ1NzUgMy4zNDMxNDU3NSwyIDUsMiBDNi42NTY4NTQyNSwyIDgsMy4zNDMxNDU3NSA4LDUgQzgsNS42NDc5MTUyMiA3Ljc5NDYwNDUyLDYuMjQ3ODU3NzQgNy40NDUzNzM5Niw2LjczODI2NzE4IFogTTUuMDAwNTI2MTYsMyBDMy44OTYyNTEzMSwzIDMuMDAxMDUyNDgsMy44OTU0MzQ1NiAzLDUgQzMuMDAxMTYyODYsNi4xMDM2NzQ4NCAzLjg5NTAyNzk5LDYuOTk4MjUxNzQgNS4wMDA1MjYxNiw3IEM2LjEwNDgwMTAxLDcgNi45OTk5OTk4Myw2LjEwNDU2NTQ0IDcsNSBDNywzLjg5NTQzNDU2IDYuMTA0ODAxMDEsMy4wMDAwMDAxNyA1LjAwMDUyNjE2LDMgWiIgZmlsbD0iI0NBQ0FDQSIgZmlsbC1ydWxlPSJub256ZXJvIj48L3BhdGg+CiAgICA8L2c+Cjwvc3ZnPg==";break;case D.ButtonImageType.separator:n=o?"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+c2VwYXJhdG9yPC90aXRsZT4KICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPgogICAgPGcgaWQ9InNlcGFyYXRvciIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPHBhdGggZD0iTTExLDQgTDEwLDQgTDEwLDggTDExLDggTDExLDkgTDksOSBMOSwzIEwxMSwzIEwxMSw0IFogTTIsNCBMMiwzIEw0LDMgTDQsOSBMMiw5IEwyLDggTDMsOCBMMyw0IEwyLDQgWiBNNiwxIEw3LDEgTDcsMTEgTDYsMTEgTDYsMSBaIiBpZD0i5b2i54q257uT5ZCIIiBmaWxsPSIjNkU2RTZFIj48L3BhdGg+CiAgICA8L2c+Cjwvc3ZnPg==":"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+c2VwYXJhdG9yPC90aXRsZT4KICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPgogICAgPGcgaWQ9InNlcGFyYXRvciIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPHBhdGggZD0iTTExLDQgTDEwLDQgTDEwLDggTDExLDggTDExLDkgTDksOSBMOSwzIEwxMSwzIEwxMSw0IFogTTIsNCBMMiwzIEw0LDMgTDQsOSBMMiw5IEwyLDggTDMsOCBMMyw0IEwyLDQgWiBNNiwxIEw3LDEgTDcsMTEgTDYsMTEgTDYsMSBaIiBpZD0i5b2i54q257uT5ZCIIiBmaWxsPSIjQ0FDQUNBIj48L3BhdGg+CiAgICA8L2c+Cjwvc3ZnPg==";break;case D.ButtonImageType.spinLeft:n=o?"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+c3BpbmxlZnQ8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZyBpZD0ic3BpbmxlZnQiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwb2x5Z29uIGZpbGw9IiM2RTZFNkUiIHBvaW50cz0iOCAzIDQgNi41IDggMTAiPjwvcG9seWdvbj4KICAgIDwvZz4KPC9zdmc+":"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+c3BpbmxlZnQ8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZyBpZD0ic3BpbmxlZnQiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwb2x5Z29uIGZpbGw9IiNDQUNBQ0EiIHBvaW50cz0iOCAzIDQgNi41IDggMTAiPjwvcG9seWdvbj4KICAgIDwvZz4KPC9zdmc+";break;case D.ButtonImageType.spinRight:n=o?"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+c3BpbnJpZ2h0PC90aXRsZT4KICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPgogICAgPGcgaWQ9InNwaW5yaWdodCIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPHBvbHlnb24gZmlsbD0iIzZFNkU2RSIgcG9pbnRzPSI0IDMgOCA2LjUgNCAxMCI+PC9wb2x5Z29uPgogICAgPC9nPgo8L3N2Zz4=":"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+c3BpbnJpZ2h0PC90aXRsZT4KICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPgogICAgPGcgaWQ9InNwaW5yaWdodCIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPHBvbHlnb24gZmlsbD0iI0NBQ0FDQSIgcG9pbnRzPSI0IDMgOCA2LjUgNCAxMCI+PC9wb2x5Z29uPgogICAgPC9nPgo8L3N2Zz4=";break;case D.ButtonImageType.ellipsis:n=o?"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+ZWxsaXBzaXM8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZyBpZD0iZWxsaXBzaXMiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwYXRoIGQ9Ik0yLDcgQzEuNDQ3NzE1MjUsNyAxLDYuNTUyMjg0NzUgMSw2IEMxLDUuNDQ3NzE1MjUgMS40NDc3MTUyNSw1IDIsNSBDMi41NTIyODQ3NSw1IDMsNS40NDc3MTUyNSAzLDYgQzMsNi41NTIyODQ3NSAyLjU1MjI4NDc1LDcgMiw3IFogTTYsNyBDNS40NDc3MTUyNSw3IDUsNi41NTIyODQ3NSA1LDYgQzUsNS40NDc3MTUyNSA1LjQ0NzcxNTI1LDUgNiw1IEM2LjU1MjI4NDc1LDUgNyw1LjQ0NzcxNTI1IDcsNiBDNyw2LjU1MjI4NDc1IDYuNTUyMjg0NzUsNyA2LDcgWiBNMTAsNyBDOS40NDc3MTUyNSw3IDksNi41NTIyODQ3NSA5LDYgQzksNS40NDc3MTUyNSA5LjQ0NzcxNTI1LDUgMTAsNSBDMTAuNTUyMjg0Nyw1IDExLDUuNDQ3NzE1MjUgMTEsNiBDMTEsNi41NTIyODQ3NSAxMC41NTIyODQ3LDcgMTAsNyBaIiBmaWxsPSIjNkU2RTZFIj48L3BhdGg+CiAgICA8L2c+Cjwvc3ZnPg==":"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+ZWxsaXBzaXM8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZyBpZD0iZWxsaXBzaXMiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwYXRoIGQ9Ik0yLDcgQzEuNDQ3NzE1MjUsNyAxLDYuNTUyMjg0NzUgMSw2IEMxLDUuNDQ3NzE1MjUgMS40NDc3MTUyNSw1IDIsNSBDMi41NTIyODQ3NSw1IDMsNS40NDc3MTUyNSAzLDYgQzMsNi41NTIyODQ3NSAyLjU1MjI4NDc1LDcgMiw3IFogTTYsNyBDNS40NDc3MTUyNSw3IDUsNi41NTIyODQ3NSA1LDYgQzUsNS40NDc3MTUyNSA1LjQ0NzcxNTI1LDUgNiw1IEM2LjU1MjI4NDc1LDUgNyw1LjQ0NzcxNTI1IDcsNiBDNyw2LjU1MjI4NDc1IDYuNTUyMjg0NzUsNyA2LDcgWiBNMTAsNyBDOS40NDc3MTUyNSw3IDksNi41NTIyODQ3NSA5LDYgQzksNS40NDc3MTUyNSA5LjQ0NzcxNTI1LDUgMTAsNSBDMTAuNTUyMjg0Nyw1IDExLDUuNDQ3NzE1MjUgMTEsNiBDMTEsNi41NTIyODQ3NSAxMC41NTIyODQ3LDcgMTAsNyBaIiBmaWxsPSIjQ0FDQUNBIj48L3BhdGg+CiAgICA8L2c+Cjwvc3ZnPg==";break;case D.ButtonImageType.expand:n=o?"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGlkPSJjb2xsYXBzZSIgd2lkdGg9IjEycHgiIGhlaWdodD0iMTJweCI+CiAgICAgICAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEycHgiIGhlaWdodD0iMTJweCIgcng9IjRweCIgcnk9IjRweCIgc3R5bGU9InN0cm9rZTogIzZlNmU2ZTsgc3Ryb2tlLXdpZHRoOjFweDsgZmlsbDogbm9uZTsiLz4KICAgICAgICA8bGluZSB4MT0iMnB4IiB5MT0iNnB4IiB4Mj0iMTBweCIgeTI9IjZweCIgc3R5bGU9InN0cm9rZTogIzZlNmU2ZTsgc3Ryb2tlLXdpZHRoOjFweDsiLz4KICAgIDwvc3ZnPg==":"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGlkPSJjb2xsYXBzZSIgd2lkdGg9IjEycHgiIGhlaWdodD0iMTJweCI+CiAgICAgICAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEycHgiIGhlaWdodD0iMTJweCIgcng9IjRweCIgcnk9IjRweCIgc3R5bGU9InN0cm9rZTogI2NhY2FjYTsgc3Ryb2tlLXdpZHRoOjFweDsgZmlsbDogbm9uZTsiLz4KICAgICAgICA8bGluZSB4MT0iMnB4IiB5MT0iNnB4IiB4Mj0iMTBweCIgeTI9IjZweCIgc3R5bGU9InN0cm9rZTogI2NhY2FjYTsgc3Ryb2tlLXdpZHRoOjFweDsiLz4KICAgIDwvc3ZnPg==";break;case D.ButtonImageType.collapse:n=o?"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGlkPSJleHBhbmQiIHdpZHRoPSIxMnB4IiBoZWlnaHQ9IjEycHgiPgogICAgICAgIDxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIxMnB4IiBoZWlnaHQ9IjEycHgiIHJ4PSI0cHgiIHJ5PSI0cHgiIHN0eWxlPSJzdHJva2U6ICM2ZTZlNmU7IHN0cm9rZS13aWR0aDoxcHg7IGZpbGw6IG5vbmU7Ii8+CiAgICAgICAgPGxpbmUgeDE9IjJweCIgeTE9IjZweCIgeDI9IjEwcHgiIHkyPSI2cHgiIHN0eWxlPSJzdHJva2U6ICM2ZTZlNmU7IHN0cm9rZS13aWR0aDoxcHg7Ii8+CiAgICAgICAgPGxpbmUgeDE9IjZweCIgeTE9IjJweCIgeDI9IjZweCIgeTI9IjEwcHgiIHN0eWxlPSJzdHJva2U6ICM2ZTZlNmU7IHN0cm9rZS13aWR0aDoxcHg7Ii8+CiAgICA8L3N2Zz4=":"data:image/svg+xml;PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGlkPSJleHBhbmQiIHdpZHRoPSIxMnB4IiBoZWlnaHQ9IjEycHgiPgogICAgICAgIDxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIxMnB4IiBoZWlnaHQ9IjEycHgiIHJ4PSI0cHgiIHJ5PSI0cHgiIHN0eWxlPSJzdHJva2U6ICNjYWNhY2E7IHN0cm9rZS13aWR0aDoxcHg7IGZpbGw6IG5vbmU7Ii8+CiAgICAgICAgPGxpbmUgeDE9IjJweCIgeTE9IjZweCIgeDI9IjEwcHgiIHkyPSI2cHgiIHN0eWxlPSJzdHJva2U6ICNjYWNhY2E7IHN0cm9rZS13aWR0aDoxcHg7Ii8+CiAgICAgICAgPGxpbmUgeDE9IjZweCIgeTE9IjJweCIgeDI9IjZweCIgeTI9IjEwcHgiIHN0eWxlPSJzdHJva2U6ICNjYWNhY2E7IHN0cm9rZS13aWR0aDoxcHg7Ii8+CiAgICA8L3N2Zz4=";break;case D.ButtonImageType.custom:n=t.imageSrc}return n}function E(t,e){return"function"==typeof t?t(e):t}n=this&&this.__extends||(c=function(t,e){return(c=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}c(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var e,n,i,o,n=1,i=arguments.length;n<i;n++)for(o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},Object.defineProperty(t,"__esModule",{value:!0}),t.paintImage=t.RowAction=t.ImagePosition=void 0,o=e("Common"),D=e("Core"),(e=f=t.ImagePosition||(t.ImagePosition={}))[e.leftTop=0]="leftTop",e[e.leftCenter=1]="leftCenter",e[e.leftBottom=2]="leftBottom",e[e.middleTop=3]="middleTop",e[e.middleCenter=4]="middleCenter",e[e.middleBottom=5]="middleBottom",e[e.rightTop=6]="rightTop",e[e.rightCenter=7]="rightCenter",e[e.rightBottom=8]="rightBottom",e=o.Common.lt,o=D.CellTypes.RowHeader,j=e.ht,d=null,s=void 0,u=Math.max,r=parseInt,a="rowActionParam",g={},n(C,l=o),C.prototype.toJSON=function(){var t=l.prototype.toJSON.call(this);return delete t.MU,delete t.Wgi,delete t.DJ,t},C.prototype.build=function(t){this.Wgi=this.Hgi(t)},C.prototype.iIe=function(t,e){return t.NMi.tIe(e)?this.Wgi:[]},C.prototype.Hgi=function(n){var t=this,e=t.MU,t=t.MU.subActionOptions;return n=I(n.subActionOptions)?n.subActionOptions:[n],(I(t)?t:[e]).map(function(t,e){return i({rowActionOption:t,command:N(t.command),visible:!0},n[e])})},C.prototype.Vgi=function(t,e){var n=-1;return m(t)?1===e.length?n=t?0:-1:1<e.length&&(n=t?1:0):h(t)&&(n=t),n},C.prototype.Fgi=function(t,e){var n,n,i,o,t,u,r,a,c,n=t.icons,n=void 0===n?[]:n,i=t.iconSelector,o=t.iLi,t=t.iconSize,u=this,r=u.DJ,a,c;return z(n[u.Vgi(i?i(r.getDataItem(e),e,r):!o||o(s,e,r),n)],t)},C.prototype.Jgi=function(t){for(var e,n,i,o,u,r,a,a,e=this,n=t.dataRange?t.dataRange.convertContext(t):t,i=e.iIe(n.sheet,n.row),o=[],u=0;u<i.length;u++)a=(r=i[u]).visible,!(j(a)||v(a)&&a(n.row)||m(a)&&a)||(a=e.Fgi(r.rowActionOption,n.row),j(a))?Q(u,n.sheet,n.row,n.col,n.sheetArea):(o.push({index:u,width:0,option:r}),w(r,a));return o},C.prototype.Xgi=function(t,e,n,i){for(var o,u,r,o=0,u=0;u<t.length;u++)r=B(t[u].option,n,i),o+=t[u].width=r;return e/2-o/2},C.prototype.Kgi=function(t,e,n,i,o,u,r,a){var c,s,l,f,d,h,n,g,b,v,M,I,m,N,c=this,s=a.sheet,l=a.row,f=a.col,d=a.sheetArea,h=new D.Rect(n,i,o,u),n=a.dataRange?a.dataRange.convertContext(a):a,g=c.Jgi(a);if(0<g.length){for(Y(g,s,n.row),t.save(),t.rect(h.x,h.y,h.width,h.height),t.clip(),t.beginPath(),b=h.x+c.Xgi(g,h.width,r,a),M=(v=O(s,l,f,d)).layout,I=0;I<g.length;I++)m=g[I],N=p(b,h.y,m.width,h.height,M,m.index,h),j(N)||(x(t,N,m.option,r,a,v),b+=N.width);t.restore()}},C.prototype.paint=function(t,e,n,i,o,u,r,a){t&&(l.prototype.paint.call(this,t,e,n,i,o,u,r,a),this.Kgi(t,e,n,i,o,u,r,a))},C.prototype.getHitInfo=function(t,e,n,i,o){var t,e,n,o,u,r,a,c,s,l,f,t={x:t,y:e,row:o.row,col:o.col,cellRect:i,cellStyle:n,sheetArea:o.sheetArea,sheet:o.sheet,context:o,isRowAction:!0},e=o.dataRange?o.dataRange.convertContext(o):o,n=O(t.sheet,t.row,t.col,t.sheetArea,!0),o=this.iIe(e.sheet,e.row);if(!n||0===o.length)return null;for(s in u=n.layout,r=-1,a=t.x,c=t.y,u)if(u.hasOwnProperty(s)&&(l=void 0,f=u[s],l=new D.Rect(i.x+f.offsetX,i.y+f.offsetY,f.width,f.height))&&l.contains(a,c)){r=+s;break}return-1!==r&&(t.actionHitInfo={index:r,option:o[r]}),t},C.prototype.processMouseUp=function(t){var e,n,i,o,u,r,a,a,c,s,c,s,l,f,a,a,u;return!(!t||(e=t.sheet,n=t.row,i=t.col,o=t.cellRect,u=t.sheetArea,r=t.actionHitInfo,a=1<(a=e.getSelections()).length||1===a.length&&1<a[0].rowCount,c=t.isShift,s=t.isCtrl,c)||s||a||!(n===e.ui&&i===e.ci||u!==D.SheetArea.rowHeader||e.endEdit())||(s=(c=t.context.dataRange)?t.context.dataRange.convertContext(t.context):t.context,a=(l=this).MU,a=j(r)?(f=N(a.command),!0):(f=(u=r.option).command,E(u.enabled,s.row)),!e)||j(f)||!a&&!j(a)||(e.getActiveRowIndex()===n&&e.getActiveColumnIndex()===i||e.eR(n,-1,1,-1,2),b(f)?e.getParent().commandManager().execute({cmd:f,row:s.row,col:s.col,sheetName:s.sheet.name(),sheetArea:s.sheetArea,isDataRange:c}):v(f)&&f.call(d,s.sheet,s.row,s.col,s.sheetArea),t.isReservedLocation=!0,e.repaint(o),0))},C.prototype.processMouseMove=function(t){var e,n,i,o,t,i,u,r;return!(!t||(n=t.actionHitInfo,o=(e=this).MU,i=(t=t.context.dataRange?t.context.dataRange.convertContext(t.context):t.context).sheet.Ut.Ci(t.row,t.col,t.sheetArea),u=t.sheet.Ut.Ci(-1,t.col,t.sheetArea),b(i))||(i=(j(n)?o:(r=n.option).rowActionOption).tooltip,!t.sheet)||i===u||(t.sheet.Ut.$n(-1,t.col,i,t.sheetArea),0))},t.RowAction=C,t.paintImage=A},"./dist/plugins/tableSheet/tableSheet-status-bar.js":function(Z,t,e){var n,i,o,u,e,r,s,a,c,l,f,d,h,g,b,v,M,I,m,N,r,D,j,C,w,p,x,y,A,T;function U(t){return t&&t._gi===o.SheetType.tableSheet}function G(t){return t&&t._gi===o.SheetType.ganttSheet}function S(t){var t=t.getActiveSheetTab();if(U(t)||G(t))return t}function z(t){return t.b3t}function B(t,e){return(-1===t.row||-1===e.row||e.row<t.row+t.rowCount&&t.row<e.row+e.rowCount)&&(-1===t.col||-1===e.col||e.col<t.col+t.colCount&&t.col<e.col+e.colCount)}function L(t,e,n,i){return{row:t,col:e,rowCount:n,colCount:i}}function R(t,e){var n,i,o,t,n=a(t.row,e.row),i=a(t.col,e.col),o=c(t.row+t.rowCount-1,e.row+e.rowCount-1),t=c(t.col+t.colCount-1,e.col+e.colCount-1);return 0<=n&&0<=i?L(n,i,o-n+1,t-i+1):0<=n?L(n,-1,o-n+1,-1):0<=i?L(-1,i,-1,t-i+1):L(-1,-1,-1,-1)}function P(t){for(var e,n,i,o,e=[],i=0;i<t.length;i++){for(n=t[i],o=0;o<t.length;o++)n!==t[o]&&B(n,t[o])&&(n=R(n,t[o]),i=o);e.push(n)}return e}function Q(t){var e,n,i,n=0;for(t.sort(function(t,e){return t.row-e.row}),e=P(t),i=0;i<e.length;i++)n+=e[i].rowCount;return n}function O(){var t,e,t=T.call(this,j)||this;return t.c3t=": ",t.s3t=f+t.name,e=D().StatusBarRowCount,t.menuContent=e,t.vit=e,t.align=p,t.tipText=D().StatusBarToolTipRowCount,t.visible=!0,t.e4t=C,t.i4t=w,t}function Y(t){var e,t,n,e=z(t),t=S(t);s(e)||(s(t)?e.qgi&&(e.qgi=!1,E(e)):(n=t.qgi,e.qgi!==t.qgi&&((n?k:E)(e),e.qgi=n)))}function W(t,e){var e=e.hitTestType,n=0===e,i=1===e,o=2===e,e=3===e;s(t)||(s(t.$gi)&&H(t),!n&&!o||t.qgi?(e||i)&&t.qgi&&(t.qgi=!1,E(t)):(t.qgi=!0,k(t)),t.DJ.getActiveSheetTab().qgi=t.qgi)}function H(t){for(var e,n,i,o,u,r,a,e=t.all(),n=[],i=0;i<e.length;i++)for(o=e[i],u=0;u<I.length;u++)o.name===I[u]&&n.push({pos:i,item:o});for(t.$gi=n,r=[],i=0;i<x.length;i++)a=V(x[i]),r.push(a);t.iNi=r}function k(t){for(var e,n,i,o,e=0;e<I.length;e++)t.remove(I[e]);if(n=t.$gi&&0<t.$gi.length?t.$gi[0].pos:0,i=t.iNi)for(e=0;e<i.length;e++)o=i[e],t.add(o,e+n)}function E(t){for(var e,n,i,e=0;e<x.length;e++)t.remove(x[e]);if(n=t.$gi)for(e=0;e<n.length;e++)i=n[e],t.add(i.item,i.pos)}function V(t){if(t===j)return new y}n=this&&this.__extends||(A=function(t,e){return(A=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}A(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),Object.defineProperty(t,"__esModule",{value:!0}),t.processStatusBarOnMouseDown=t.updateStatusBarOnActiveSheetChanged=void 0,i=e("Common"),o=e("Core"),u=e("StatusBar"),e=e("./dist/plugins/tableSheet/tablesheet-override.js"),r=i.Common.lt,s=r.ht,a=Math.min,c=Math.max,l=null,f=".statusbar",I=[d="average",h="count",g="numericalCount",b="min",v="max",M="sum"],m="none",N="inline-block",r=new i.Common.ResourceManager(e.SR,"TableSheet"),D=r.getResource.bind(r),C="menuRowCount",w="tooltipRowCount",p="right",x=[j="rowCount"],T=u.StatusItem,n(O,T),O.prototype.rLi=function(){return S(this.DJ)},O.prototype.nNi=function(){return z(this.DJ)},O.prototype.onBind=function(t){var n=this;(n.DJ=t).bind(o.Events.SelectionChanging+n.s3t,function(t,e){n.x3t(e)}),t.bind(o.Events.SelectionChanged+n.s3t,function(t,e){n.x3t(e)}),t.bind(o.Events.ActiveSheetChanged+n.s3t,function(){n.onUpdate()})},O.prototype.x3t=function(t){var e,t=t.newSelections;this.eNi(t)},O.prototype.eNi=function(t){var e,n,n,i,o,u,r,a,c,n,e=this,n=e.rLi();if(n&&(n=n.getDataView(),i=0,o=l,u=!1,r=[],!s(n))){for(i=n.getColumnInfos().length,a=0;a<t.length;a++){if(-1===(c=t[a]).col&&-1===c.row){u=!0;break}-1===c.row||-1!==c.colCount&&c.colCount!==i||r.push(c)}u?o=n.visibleLength():0<(n=Q(r))&&(o=n),e.A3t(o)}},O.prototype.E3t=function(t){this.u3t&&(this.u3t.style.display=t?N:m)},O.prototype.A3t=function(t){var e=this.c3t,n=this.I3t,i=this.vit,o=this.visible,u=!1;this.value=t,n.innerText=i+e+t,!s(t)&&o&&(u=!0),this.E3t(u)},O.prototype.onUpdate=function(){T.prototype.onUpdate.call(this);var t=this,e=t.rLi();s(e)||t.eNi(e.Mf.getSelections())},O.prototype.FM=function(t){var e,t;t&&(e=t.menuContent,t=t.tipText,this.menuContent=e,this.vit=e,this.tipText=t,this.u3t.title=t)},O.prototype.onUnbind=function(){var t=this.DJ,e=this.s3t;t&&(t.unbind(o.Events.SelectionChanging+e),t.unbind(o.Events.SelectionChanged+e),t.unbind(o.Events.ActiveSheetChanged+e),this.DJ=l)},y=O,t.updateStatusBarOnActiveSheetChanged=Y,t.processStatusBarOnMouseDown=W},"./dist/plugins/tableSheet/tableSheet-theme.js":function(t,e,n){var i,u,r,o,e,n,a,c,s,l,f,d,h,g;function b(t,e){return void 0===e&&(e=i.LineStyle.thin),new i.LineBorder(t,e)}function v(t,e,n,i){var o=new u.TableTheme;return o.name(t),o.headerRowStyle(new u.TableStyle(e.backColor,e.foreColor,e.font,r,r,e.borderRight,e.borderBottom)),o.wholeTableStyle(new u.TableStyle(n.backColor,n.foreColor,r,r,r,n.borderRight,n.borderBottom)),i&&i.backColor&&o.firstRowStripStyle(new u.TableStyle(i.backColor)),o}Object.defineProperty(e,"__esModule",{value:!0}),i=n("Core"),u=n("Tables"),r=void 0,e="#808080",n="transparent",a="#E6E4F6",c="#92A5A8",s="#66D1C7",l="black",f="#EBEBEB",d="#FFFFFF",h="bold 11pt Calibri",(g=[v("professional1",{backColor:"#6A76D4",foreColor:o="#F2F2F2",borderRight:b("#6A76D4"),font:h},{foreColor:e,borderRight:b(n),borderBottom:b(a)},r),v("professional2",{backColor:c,foreColor:o,font:h},{foreColor:e,borderRight:b(c),borderBottom:b(n)},r),v("professional3",{backColor:s,foreColor:o,borderRight:b(s),font:h},{foreColor:e,borderRight:b(n),borderBottom:b(s)},r),v("professional4",{backColor:"#36495E",foreColor:o,borderRight:b("#36495E"),font:h},{foreColor:e},{backColor:"#F1F1F1"}),v("professional5",{backColor:"#2F7A84",foreColor:o,borderRight:b("#2F7A84"),font:h},{foreColor:e},{backColor:"#DAF1F3"}),v("professional6",{backColor:"#FFED88",foreColor:l,font:"bold 11pt Arial",borderRight:b("#FFED88")},{foreColor:l,borderRight:b(n),borderBottom:b(n)},r),v("professional7",{backColor:"#9EB09F",foreColor:o,borderRight:b("#9EB09F"),font:h},{foreColor:e,borderRight:b(n),borderBottom:b(a)},r),v("professional8",{backColor:"#362F4A",foreColor:o,borderRight:b("#362F4A"),font:h},{foreColor:e,borderRight:b(n)},{backColor:"#F6F6F6"}),v("professional9",{backColor:"#EB6051",foreColor:o,borderRight:b("#EB6051"),font:h},{foreColor:e,borderRight:b(n)},{backColor:f}),v("professional10",{backColor:"#26AE60",foreColor:o,borderRight:b("#26AE60"),font:h},{foreColor:e,borderRight:b(n)},{backColor:f}),v("professional11",{backColor:"#2882BA",foreColor:o,borderRight:b("#2882BA"),font:h},{foreColor:e,borderRight:b(n)},{backColor:f}),v("professional12",{backColor:"#555F8D",foreColor:o,borderRight:b("#555F8D"),font:h},{foreColor:e},{backColor:"#9EB6C4"}),v("professional13",{backColor:"#54618F",foreColor:o,borderRight:b("#54618F"),font:h},{backColor:"#7591AB",foreColor:d},r),v("professional14",{backColor:d,foreColor:"#5660B9",borderBottom:b("#A1A5CA",i.LineStyle.thick),borderRight:b(d),font:h},{backColor:d,foreColor:"#595959"},{backColor:" #F8F8FD"}),v("professional15",{backColor:"#E6ECF2",foreColor:"#404040",borderRight:b("#E6ECF2"),font:h},{foreColor:e,borderRight:b(n),borderBottom:b("#E6ECF2")},r),v("professional16",{backColor:"#475E86",foreColor:"#67C3F7",borderRight:b("#475E86"),font:h},{backColor:"#2A395A",foreColor:"#D0CECE"},r),v("professional17",{backColor:"#5165F8",foreColor:o,borderRight:b("#5165F8"),font:h},{backColor:d,foreColor:"#595959"},{backColor:"#F3F4FC"}),v("professional18",{backColor:"#F6F8FA",foreColor:"#404040",borderRight:b("#F6F8FA"),font:h},{backColor:d,foreColor:"#757171"},r),v("professional19",{backColor:d,foreColor:e,borderBottom:b("#83ACF6",i.LineStyle.thick),borderRight:b(d),font:h},{backColor:d,foreColor:e,borderBottom:b("#D9E1F2")},r),v("professional20",{backColor:d,foreColor:"#3565F6",borderBottom:b("#3565F6",i.LineStyle.thick),borderRight:b(d),font:h},{backColor:"#FCFDFF",foreColor:"#757171",borderRight:b(d,i.LineStyle.thick),borderBottom:b(d,i.LineStyle.thick)},{backColor:"#F6F8FA"}),v("professional21",{backColor:"#FCFDFF",foreColor:"#404040",borderBottom:b("#ACB9CA",i.LineStyle.thick),borderRight:b("#D6DCE4",i.LineStyle.dashed),font:h},{backColor:"#FCFDFF",foreColor:"#595959",borderBottom:b("#E5E9F0"),borderRight:b("#E5E9F0",i.LineStyle.dashed)},r),v("professional22",{backColor:"#222B35",foreColor:d,borderBottom:b("#08D4FF",i.LineStyle.thick),borderRight:b("#222B35"),font:h},{backColor:"#213551",foreColor:d},{backColor:"#3C5679"}),v("professional23",{backColor:"#213551",foreColor:d,borderBottom:b("#13E86C",i.LineStyle.thick),borderRight:b("#D6DCE4"),font:h},{backColor:"#F4F7F9",foreColor:"#213551",borderRight:b("#D6DCE4")},{backColor:d}),v("professional24",{backColor:"#1D1E1F",foreColor:"#E7E6E6",borderBottom:b("#13E86C",i.LineStyle.thick),borderRight:b("#606060"),font:h},{backColor:"#404142",foreColor:d,borderRight:b("#606060")},{backColor:"#4F4F50"})]).forEach(function(t){u.TableThemes[t.name()]=t})},"./dist/plugins/tableSheet/tableSheet-undo.js":function(t,e,n){var i,s,l,o,u,r,n,a,f,d,c,o,n;function h(t){return(null==t?void 0:t.sourceType)===d.Combo}function g(t,e,n,i){return{sheet:t,sheetName:t.name(),col:e,filterValues:n,conditionInfo:n&&0===n.length&&i?i.toJSON():void 0}}function b(t){this.host=t,this.G8=[],this.P8=0,this.Jpi=0,this.DKi=[]}i=this&&this.__awaiter||function(t,r,n,a){function c(e){return e instanceof n?e:new n(function(t){t(e)})}return new(n=n||Promise)(function(e,n){function i(t){try{u(a.next(t))}catch(t){n(t)}}function o(t){try{u(a.throw(t))}catch(t){n(t)}}function u(t){t.done?e(t.value):c(t.value).then(i,o)}u((a=a.apply(t,r||[])).next())})},s=this&&this.__generator||function(t,n){var i={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},o,u,r,a,a={next:e(0),throw:e(1),return:e(2)};return"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function e(e){return function(t){return c([e,t])}}function c(e){if(o)throw new TypeError("Generator is already executing.");for(;i=a&&e[a=0]?0:i;)try{if(o=1,u&&(r=2&e[0]?u.return:e[0]?u.throw||((r=u.return)&&r.call(u),0):u.next)&&!(r=r.call(u,e[1])).done)return r;switch(u=0,(e=r?[2&e[0],r.value]:e)[0]){case 0:case 1:r=e;break;case 4:return i.label++,{value:e[1],done:!1};case 5:i.label++,u=e[1],e=[0];continue;case 7:e=i.ops.pop(),i.trys.pop();continue;default:if(!(r=0<(r=i.trys).length&&r[r.length-1])&&(6===e[0]||2===e[0])){i=0;continue}if(3===e[0]&&(!r||e[1]>r[0]&&e[1]<r[3]))i.label=e[1];else if(6===e[0]&&i.label<r[1])i.label=r[1],r=e;else{if(!(r&&i.label<r[2])){r[2]&&i.ops.pop(),i.trys.pop();continue}i.label=r[2],i.ops.push(e)}}e=n.call(t,i)}catch(t){e=[6,t],u=0}finally{o=r=0}if(5&e[0])throw e[1];return{value:e[0]?e[1]:void 0,done:!0}}},l=this&&this.__spreadArray||function(t,e,n){if(n||2===arguments.length)for(var i=0,o=e.length,u;i<o;i++)!u&&i in e||((u=u||Array.prototype.slice.call(e,0,i))[i]=e[i]);return t.concat(u||Array.prototype.slice.call(e))},Object.defineProperty(e,"__esModule",{value:!0}),e.UndoHelper=e.TableSheetUndoSourceType=e.TableSheetUndoType=void 0,o=n("Common"),u=n("Core"),r=void 0,n=o.Common.lt,a=n.ht,(o=f=e.TableSheetUndoType||(e.TableSheetUndoType={})).RowAction="rowAction",o.GroupOptions="groupOptions",o.GroupOutlinePosition="groupOutlinePosition",o.ExpandGroupByLevel="expandGroupBylevel",o.ExpandGroupItem="expandGroupItem",o.OptionChanged="optionChanged",o.ThemeChanged="themeChanged",o.PinnedRowsChanged="pinnedRowsChanged",o.PinnedColumnsChanged="pinnedColumnsChanged",o.DefaultRowHeightChanged="defaultRowHeightChanged",o.ApplyFreeHeaderChanged="applyFreeHeaderChanged",o.ResetViewChanged="resetViewChanged",o.UpdateFilterOnMoved="updateFilterOnMoved",o.FilterColumn="filterColumn",o.SetFilterItemMap="setFilterItemMap",o.RemoveCrosRowFilterInfo="removeCrosRowFilterInfo",o.RemoveRow="removeRow",o.SetDataView="setDataView",o.RemoveColumns="removeColumns",o.AddColumns="addColumns",o.ResetGroupOutline="resetGroupOutline",o.SortMapChanged="sortMapChanged",o.StoreTreeData="storeTreeData",o.ApplyTreeData="applyTreeData",o.ChangeCollapse="changeCollapse",(n=d=e.TableSheetUndoSourceType||(e.TableSheetUndoSourceType={}))[n.TableSheet=1]="TableSheet",n[n.DataManager=2]="DataManager",n[n.Combo=3]="Combo",b.prototype.startTransaction=function(){var t;this.P8++,null!=(t=this.view)&&t.vCn()},b.prototype.endTransaction=function(){var t,t;return this.P8=Math.max(this.P8-1,0),null!=(t=this.view)&&t.FCn(),0===this.P8?(t=this.getUndoChanges(),this.TKi(),t):[]},b.prototype.startAddComboChanges=function(){var t={sourceType:d.Combo,changes:[]};this.xKi(t),this.DKi.push(t),this.Jpi++},b.prototype.endAddComboChanges=function(){this.Jpi=Math.max(this.Jpi-1,0),this.DKi.pop()},b.prototype.addUndoChanges=function(t){this.lM()&&(this.PKi(),this.xKi({sourceType:d.TableSheet,changeData:t}))},b.prototype.getUndoChanges=function(){return this.PKi(),this.G8},b.prototype.undo=function(r,a){var c;return void 0===a&&(a=!0),i(this,void 0,void 0,function(){var e,n,i,o,u,u;return s(this,function(t){switch(t.label){case 0:this.host.suspendPaint(),e=a?l([],r,!0).reverse():l([],r,!0),n=!0,i=0,t.label=1;case 1:return i<e.length?(o=e[i]).sourceType!==d.DataManager?[3,4]:(u=null==(c=this.view)?void 0:c.Mtt(o.changeData),n=!1,u?[4,u]:[3,3]):[3,6];case 2:t.sent(),t.label=3;case 3:return[3,5];case 4:o.sourceType===d.TableSheet?(u=this.Yqi(o.changeData),n&&!1===u&&(n=u)):o.sourceType===d.Combo&&this.undo(o.changes,!1),t.label=5;case 5:return i++,[3,1];case 6:return this.$qi(n),[2]}})})},b.prototype.$qi=function(t){this.host.refreshBindColumns(),this.host.WDi(r,r,t),this.host.tze(),this.host.resumePaint()},b.prototype.xKi=function(t){var e=this.DKi[this.DKi.length-1];(e&&0<this.Jpi&&h(e)?e.changes:this.G8).push(t)},Object.defineProperty(b.prototype,"view",{get:function(){return this.host.getDataView()},enumerable:!1,configurable:!0}),b.prototype.TKi=function(){var t;null!=(t=this.view)&&t.PEe(),this.G8=[],this.DKi=[],this.P8=0},b.prototype.lM=function(){return 0<this.P8},b.prototype.Yqi=function(t){var e,n,i,o,u,r,a,o,r,c,o,r,c,o,r,i,e=this,n=!0;return t.type===f.RemoveCrosRowFilterInfo||t.type===f.UpdateFilterOnMoved||t.type===f.FilterColumn||t.type===f.SetFilterItemMap?this.ize(t):t.type===f.RowAction?this.host.rowActionOptions(t.oldData):t.type===f.GroupOptions?((i=t.oldData)?this.host.groupBy(i):this.host.removeGroupBy(),n=!1):t.type===f.GroupOutlinePosition?this.host.groupOutlinePosition(t.oldData):t.type===f.ExpandGroupByLevel?(o=t.oldData,u=o.level,r=o.indexs,a=o.expand,r.forEach(function(t){e.host.expandGroupItem(u,t,a)})):t.type===f.ExpandGroupItem?(r=(o=t.oldData).level,c=o.index,o=o.expand,this.host.expandGroupItem(r,c,o)):t.type===f.OptionChanged?(r=t.key,i=t.oldData,this.host.options[r]=i):t.type===f.ThemeChanged?this.host.applyTableTheme(t.oldData):t.type===f.PinnedRowsChanged?this.host.tze():t.type===f.PinnedColumnsChanged?this.host.nze(t.oldData.oldFilterColumnsMap,t.oldData.oldFilterColumnsMap):t.type===f.DefaultRowHeightChanged?this.host.setDefaultRowHeight(t.oldData.size,t.oldData.sheetArea):t.type===f.ApplyFreeHeaderChanged?this.host.applyFreeHeaderArea(t.oldData):t.type===f.ResetViewChanged?(o=(c=t.oldData).columnInfoOptions,r=c.includeDefaultColumns,i=c.options,this.host.resetNewView(o,r,i)):t.type===f.RemoveRow?(this.host.eze(),n=!1):t.type===f.RemoveColumns?this.host.Cze(t.oldCol,t.oldCount):t.type===f.AddColumns?this.host.beo(t.oldCol,t.oldCount):t.type===f.ResetGroupOutline?(this.host.refreshBindColumns(),this.host.WDi()):t.type===f.SortMapChanged?(this.lze(t),n=!1):t.type===f.StoreTreeData||t.type===f.ApplyTreeData?(this.dze(t),n=!1):t.type===f.ChangeCollapse?this.host.vze(t.oldData):t.type===f.SetDataView&&this.host.kTi(this.view),n},b.prototype.dze=function(t){var t=t.type;t===f.ApplyTreeData?this.host.nDi():t===f.StoreTreeData&&this.host.uDi()},b.prototype.lze=function(t){var t,e,t=t.oldData,e;this.host.Mf.rowFilter().Nze(t)},b.prototype.ize=function(t){var e,e,n,n,n,t,n,t;t.type===f.RemoveCrosRowFilterInfo?this.host.Mf.rowFilter().oze(t.changedData):t.type===f.UpdateFilterOnMoved?(n=t.changedData.map(function(t){return{oldIndex:t.newIndex,newIndex:t.oldIndex}}),this.host.Mf.rowFilter().nTi(n)):t.type===f.FilterColumn?(n=this.host.bze(t.changedData.columnName),a(n)||(this.host.Mf.vCn(),this.host.zA({cmd:"clearFilter",cmdOption:{colIndex:n}}),this.host.Mf.FCn())):t.type===f.SetFilterItemMap&&(t=(n=t.changedData).columnName,n=n.checkedValues,null!==(t=this.host.bze(t)))&&(this.host.Mf.ki(u.Events.RangeFiltering,g(this.host.Mf,t,n,null)),this.host.Mf.IB.removeFilterItems(t),null!=(e=null==(e=this.host.Mf)?void 0:e.IB)&&e.setFilterItemMap&&this.host.Mf.IB.setFilterItemMap(t,n),this.host.Mf.IB.filter(t),this.host.Mf.ki(u.Events.RangeFiltered,g(this.host.Mf,t,n,null)))},b.prototype.PKi=function(){var e,t,t,t,n,e=this,n;(null!=(t=null==(t=this.view)?void 0:t.LEe())?t:[]).forEach(function(t){e.xKi({sourceType:d.DataManager,changeData:t})}),null!=(t=this.view)&&t.PEe()},e.UndoHelper=b},"./dist/plugins/tableSheet/tableSheet.entry.js":function(t,e,n){var i,o,i=this&&this.__createBinding||(Object.create?function(t,e,n,i){void 0===i&&(i=n);var o=Object.getOwnPropertyDescriptor(e,n);o&&("get"in o?e.__esModule:!o.writable&&!o.configurable)||(o={enumerable:!0,get:function(){return e[n]}}),Object.defineProperty(t,i,o)}:function(t,e,n,i){t[i=void 0===i?n:i]=e[n]}),o=this&&this.__exportStar||function(t,e){for(var n in t)"default"===n||Object.prototype.hasOwnProperty.call(e,n)||i(e,t,n)};Object.defineProperty(e,"__esModule",{value:!0}),o(n("./dist/plugins/tableSheet/tableSheet.interface.js"),e),n("./dist/plugins/tableSheet/data.dataManager.js"),n("./dist/plugins/tableSheet/arrayFunction.js"),o(n("./dist/plugins/tableSheet/actionColumn.js"),e),n("./dist/plugins/tableSheet/tableSheet-theme.js"),o(n("./dist/plugins/tableSheet/tableSheet.js"),e),n("./dist/plugins/tableSheet/tableSheet-action.js"),n("./dist/plugins/tableSheet/tablesheet-command-register.js"),o(n("./dist/plugins/tableSheet/tablesheet-override.js"),e),o(n("./dist/plugins/tableSheet/tableSheet-panel/tableSheetPanel.js"),e)},"./dist/plugins/tableSheet/tableSheet.interface.js":function(t,e,n){var i,u,r,o,a,c,s,l,n,n,n,n;function f(t){this.Hc(t)}Object.defineProperty(e,"__esModule",{value:!0}),e.CustomSheetTab=e.GroupLayoutMode=e.GroupOutlinePosition=e.TriggerWhenType=e.ColumnTypes=void 0,i=n("Core"),u=i.util.Z0,r=i.util.V0,(n=o=e.ColumnTypes||(e.ColumnTypes={})).Number="Number",n.Text="Text",n.Formula="Formula",n.Checkbox="Checkbox",n.Date="Date",n.Currency="Currency",n.Percent="Percent",n.Phone="Phone",n.Email="Email",n.URL="URL",n.Lookup="Lookup",n.CreatedTime="CreatedTime",n.ModifiedTime="ModifiedTime",n.Attachment="Attachment",n.Select="Select",n.Barcode="Barcode",(n=a=e.TriggerWhenType||(e.TriggerWhenType={})).onNew="onNew",n.onNewAndUpdate="onNewAndUpdate",(n=c=e.GroupOutlinePosition||(e.GroupOutlinePosition={}))[n.none=0]="none",n[n.groupCell=1]="groupCell",n[n.rowHeader=2]="rowHeader",n[n.groupCellAll=3]="groupCellAll",(n=s=e.GroupLayoutMode||(e.GroupLayoutMode={}))[n.tabular=0]="tabular",n[n.outline=1]="outline",n[n.condensed=2]="condensed",f.prototype.Hc=function(t){this.Zc=t,this.jft=i.SheetTabVisible.visible,this._gi=i.SheetType.customSheetTab},f.prototype.hg=function(t){this.onSetHost(t)},f.prototype.onSetHost=function(t){},f.prototype.L0=function(t,e){this.onDispose(t,e)},f.prototype.onDispose=function(t,e){},f.prototype.Zg=function(t){},f.prototype.pM=function(t){this.parent=t},f.prototype.getParent=function(){return this.parent},f.prototype.name=function(t){if(0===arguments.length)return this.Zc;this.Zc=t},f.prototype.visible=function(t){if(0===arguments.length)return this.jft;this.jft=!0===t?i.SheetTabVisible.visible:!1===t?i.SheetTabVisible.hidden:t},f.prototype.b2=function(t,e){if(0===arguments.length)return this.ni;this.ni=t},f.prototype.ow=function(){},f.prototype.tw=function(){},f.prototype.aw=function(){},f.prototype.$C=function(t){},f.prototype.Wb=function(t,e){},f.prototype.Kb=function(t){},f.prototype.Fb=function(t,e){},f.prototype.onHorizontalDockScroll=function(t,e){},f.prototype.Gb=function(t){},f.prototype.WC=function(t,e,n){this.parent._hScrollbar.w4()},f.prototype.QC=function(t,e,n){this.parent._vScrollbar.w4()},f.prototype.Cg=function(){this.onDoResize()},f.prototype.onDoResize=function(){},f.prototype.suspendPaint=function(){},f.prototype.resumePaint=function(){},f.prototype.isPaintSuspended=function(){return!1},f.prototype.bind=function(t,e,n){},f.prototype.unbind=function(t,e){},f.prototype.unbindAll=function(){},f.prototype.D3=function(t,e,n){},f.prototype._3=function(t,e){},f.prototype.E3=function(){},f.prototype.suspendEvent=function(){},f.prototype.resumeEvent=function(){},f.prototype.isEventSuspended=function(){return!1},f.prototype.bC=function(){},f.prototype.ki=function(t,e,n){},f.prototype.isEditing=function(){return!1},f.prototype.ol=function(t,e,n,i){return!0},f.prototype.currentTheme=function(t){},f.prototype.FM=function(){},f.prototype.toJSON=function(t){},f.prototype.fromJSON=function(t,e,n,i,o){},f.prototype.uw=function(){return{top:0,left:0}},f.prototype.ot=function(){return new i.Rect(0,0,0,0)},f.prototype.hitTest=function(t,e,n,i){return null},f.prototype.e1e=function(){},f.prototype.parameter=function(t){var e,n,i,o;if(0===arguments.length){for(i in n={},e=this.o1e)e.hasOwnProperty(i)&&(n[i]=r(e[i]));return n}if(t){for(i in o={},t)t.hasOwnProperty(i)&&(o[i]=u(t[i]));this.o1e=o}},e.CustomSheetTab=f},"./dist/plugins/tableSheet/tableSheet.js":function(a,l,s){var f,y,o,L,g,d,G,c,N,A,b,B,h,O,R,P,Q,Y,v,W,t,M,t,H,k,V,F,_,J,I,X,m,D,j,C,K,w,q,$,tt,et,nt,it,ot,ut,rt,at,E,Z,ct,st,i,lt,ft,dt,ht,gt,bt,r,vt,Mt,u,t;function It(t,e){return Array.isArray(t)?e?t.toString():Nt(t):t}function mt(t,e){return t&&Dt(e?"["+t+"]":t)}function Nt(t){var e=t;return e=Array.isArray(t)?"["+t.toString()+"]":e}function Dt(t){var e=t;return e=t&&"["===t[0]&&"]"===t[t.length-1]?t.substring(1,t.length-1).split(","):e}function jt(e,n,t){var i,o,u;e.value=e.value||n.value,e.type=e.type||n.type,e.caption=e.caption||n.caption,i=e.type!==n.type,o=n.type===d.ColumnTypes.CreatedTime||n.type===d.ColumnTypes.ModifiedTime,u=e.type===d.ColumnTypes.Formula,(t=t||Object.keys(n)).forEach(function(t){if(!e.hasOwnProperty(t)){if(i){if("defaultValue"===t||"dataType"===t||"style"===t||"_"===t[0])return;if(o&&("trigger"===t||"readonly"===t))return;if(!u&&"name"===t)return}e[t]=n[t]}})}function Ct(t){return k(t)||t!==d.GroupLayoutMode.outline&&t!==d.GroupLayoutMode.condensed?d.GroupLayoutMode.tabular:t}function wt(t){return"header"===t||"footer"===t?t:E}function pt(t,i,o){Object.defineProperty(t,i,{get:function(){return this["_"+i]},set:function(t){var e,e,n,e,e=this["_"+i];e!==t&&(o?(n=(e=o(i,t,e))[0],e=e[1],this["_"+i]=n,e&&e()):this["_"+i]=t)},configurable:!0})}function e(t){return"gc-defined-column-type-icon-".concat(t.toLowerCase())}function p(t){return t&&"="===t[0]}function xt(t){return t&&-1<t.indexOf(".")}function T(t){return"number"==typeof t}function S(t){return"string"==typeof t}function z(t){return"boolean"==typeof t}function U(t,e){return T(t[e])}function yt(t){return t instanceof Function}function At(t,e){var n,i,o,u,r;if(t.collapsed)for(n=t.start+1;n<=t.end;n++)(i=e[n])?i.visible=!1:e[n]={visible:!1};else if(t.children)for(o=0,u=t.children;o<u.length;o++)At(r=u[o],e)}function Tt(t,e){var e,n,i,o,u,r,e=e&&e.grouping&&e.grouping.tree;if(e)for(n=(n=t.rows)||(t.rows=[]),o=0,u=i=e.children;o<u.length;o++)At(r=u[o],n)}function St(t){var e,t,n,e,i,o;if(t)return e=t.step||[1,1],t=t.style,n=k(e[0])?1:e[0],e=k(e[1])?1:e[1],t?(o=i=void 0,Array.isArray(t)?(i=t[0],o=t[1]):i=t,{fillRowStep:n,fillRowStyle:i,blankRowStep:e,blankRowStyle:o}):void 0}function zt(t,e,n,i){var t,o;t[e]||(t[e]={}),(t=t[e])[n]||(t[n]={}),(o=t[n]).style=i}function Lt(t,e,n,i){for(var o,u,r,a,c,s,l,f,o=t.JS(e,i),u=o.row,r=o.col,a=0,c=o.rowCount;a<c;a++)for(s=0,l=o.colCount;s<l;s++)f=t.getActualStyle(u+a,r+s,i).toJSON(),V(f)||zt(n,u+a,r+s,f)}function Ot(e,t,n){t&&(e.oNi=!0,t.rules.forEach(function(t){t.ranges.forEach(function(t){Lt(e,t,n)})}),e.oNi=!1)}function kt(t){return t.NMi}function x(e,t,n){return function(t){e.repaint(),n&&n()}}function Et(t){var e,n,i,o,u,r,a,r,e=t.type,n=t.comparisonOperator;switch(U(A.ComparisonOperators,n)&&(n=A.ComparisonOperators[n]),e){case"number":o=t.value1,u=t.value2,r=t.isIntegerValue,i=(0,N.createNumberValidator)(n,o,u,r);break;case"date":o=t.value1,u=t.value2,i=(0,N.createDateValidator)(n,o,u);break;case"time":o=t.value1,u=t.value2,i=(0,N.createTimeValidator)(n,o,u);break;case"textLength":o=t.value1,u=t.value2,i=(0,N.createTextLengthValidator)(n,o,u);break;case"formula":a=t.formula,i=(0,N.createFormulaValidator)(a);break;case"formulaList":a=t.formula,i=(0,N.createFormulaListValidator)(a);break;case"list":r=t.source,i=(0,N.createListValidator)(r)}return i}function Zt(t,e){var n,i,o,u,r,a,c,s,l,e,n,n=e.errorMessage,i=e.errorStyle,o=e.errorTitle,u=e.highlightStyle,r=e.ignoreBlank,a=e.inCellDropdown,c=e.inputMessage,s=e.inputTitle,l=e.showErrorMessage,e=e.showInputMessage;k(n)||t.errorMessage(n),U(N.ErrorStyle,i)&&t.errorStyle(N.ErrorStyle[i]),k(o)||t.errorTitle(o),k(u)||(n={},U(N.HighlightType,u.type)&&(n.type=N.HighlightType[u.type]),k(u.color)||(n.color=u.color),U(N.HighlightPosition,u.position)&&(n.position=N.HighlightPosition[u.position]),k(u.image)||(n.image=u.image),t.highlightStyle(n)),k(r)||t.ignoreBlank(r),k(a)||t.inCellDropdown(a),k(c)||t.inputMessage(c),k(s)||t.inputTitle(s),k(l)||t.showErrorMessage(l),k(e)||t.showInputMessage(e)}function Ut(t){var e=Et(t);return Zt(e,t),e}function Gt(t,e,n){var i,o,u,r,a,c,s,l,c,s,f,l,d,h,g,b,v,M,a,c,s,l,f,g,a,c,s,M,d,h,b,v,l,f,g,a,c,I,m,N,D,j,C,j,w,p,x,y,p,o,i,i=e.ruleType,o=e.style,u=o;switch(!o||o instanceof L.Style||(u=new L.Style(o)),i){case"cellValueRule":a=e.comparisonOperator,c=e.value1,s=e.value2,l=e.comparisionOperator,k(a)&&(a=l),U(A.ComparisonOperators,a)&&(a=A.ComparisonOperators[a]),r=t.addCellValueRule(a,c,s,u,n);break;case"specificTextRule":a=e.comparisonOperator,c=e.text,l=e.comparisionOperator,k(a)&&(a=l),U(A.TextComparisonOperators,a)&&(a=A.TextComparisonOperators[a]),r=t.addSpecificTextRule(a,c,u,n);break;case"formulaRule":s=e.formula,r=t.addFormulaRule(s,u,n);break;case"dateOccurringRule":f=e.type,U(A.DateOccurringType,f)&&(f=A.DateOccurringType[f]),r=t.addDateOccurringRule(f,u,n);break;case"top10Rule":f=e.type,l=e.rank,U(A.Top10ConditionType,f)&&(f=A.Top10ConditionType[f]),r=t.addTop10Rule(f,l,u,n);break;case"uniqueRule":r=t.addUniqueRule(u,n);break;case"duplicateRule":r=t.addDuplicateRule(u,n);break;case"averageRule":f=e.type,U(A.AverageConditionType,f)&&(f=A.AverageConditionType[f]),r=t.addAverageRule(f,u,n);break;case"twoScaleRule":d=e.minType,h=e.minValue,g=e.minColor,b=e.maxType,v=e.maxValue,M=e.maxColor,U(A.ScaleValueType,d)&&(d=A.ScaleValueType[d]),U(A.ScaleValueType,b)&&(b=A.ScaleValueType[b]),r=t.add2ScaleRule(d,h,g,b,v,M,n);break;case"threeScaleRule":d=e.minType,h=e.minValue,g=e.minColor,a=e.midType,c=e.midValue,s=e.midColor,b=e.maxType,v=e.maxValue,M=e.maxColor,U(A.ScaleValueType,d)&&(d=A.ScaleValueType[d]),U(A.ScaleValueType,a)&&(a=A.ScaleValueType[a]),U(A.ScaleValueType,b)&&(b=A.ScaleValueType[b]),r=t.add3ScaleRule(d,h,g,a,c,s,b,v,M,n);break;case"dataBarRule":d=e.minType,h=e.minValue,b=e.maxType,v=e.maxValue,l=e.color,f=e.gradient,g=e.showBarOnly,a=e.showBorder,c=e.borderColor,s=e.barDirection,M=e.negativeData,U(A.ScaleValueType,d)&&(d=A.ScaleValueType[d]),U(A.ScaleValueType,b)&&(b=A.ScaleValueType[b]),r=t.addDataBarRule(d,h,b,v,l,n),z(f)&&r.gradient(f),z(g)&&r.showBarOnly(g),z(a)&&r.showBorder(a),S(c)&&r.borderColor(c),U(A.BarDirection,s)&&r.dataBarDirection(A.BarDirection[s]),M&&(d=M.negativeFillColor,h=M.useNegativeFillColor,b=M.negativeBorderColor,v=M.useNegativeBorderColor,l=M.axisColor,f=M.axisPosition,S(d)&&r.negativeFillColor(d),z(h)&&r.useNegativeFillColor(h),S(b)&&r.negativeBorderColor(b),z(v)&&r.useNegativeBorderColor(v),S(l)&&r.axisColor(l),U(A.DataBarAxisPosition,f))&&r.axisPosition(A.DataBarAxisPosition[f]);break;case"iconSetRule":if(g=e.iconSetType,a=e.reverseIconOrder,c=e.showIconOnly,I=e.icons,m=e.iconCriteria,U(A.IconSetType,g)&&(g=A.IconSetType[g]),r=t.addIconSetRule(g,n),z(a)&&r.reverseIconOrder(a),z(c)&&r.showIconOnly(c),I)for(N=r.icons(),D=0;D<I.length;D++)C=(j=I[D]).iconSetType,j=j.iconIndex,U(A.IconSetType,C)&&(C=A.IconSetType[C]),N[D]={iconSetType:C,iconIndex:j};if(m)for(w=r.iconCriteria(),D=0;D<m.length;D++)x=(p=m[D]).isGreaterThanOrEqualTo,y=p.iconValueType,p=p.iconValue,U(A.IconValueType,y)&&(y=A.IconValueType[y]),w[D]=new A.IconCriterion(x,y,p)}r&&(o=e.priority,i=e.stopIfTrue,T(o)&&r.priority(o),z(i))&&r.stopIfTrue(i)}function Bt(t,e){return t.filter(function(t){return t.name===e})[0]}function Rt(t){return!k(t)&&!k(t.icons)&&0<t.icons.length}function Pt(t){for(var e=[];0<t;)e[--t]=t;return e}function Qt(t,e){var n,i,o,u;if(!k(t))for(I(t)||(t=[t]),n=0;n<t.length;n++)for(i=t[n],o=0;o<e.length;o++)if(u=e[o],i.field===u.value&&u.isGroup)return!0;return!1}function Yt(e){["columnStart","columnEnd","rowStart","rowEnd","repeatColumnStart","repeatColumnEnd","repeatRowStart","repeatRowEnd"].forEach(function(t){e[t]=function(){return-1}})}function Wt(t,e){!1===e?e=L.SheetTabVisible.hidden:!0===e&&(e=L.SheetTabVisible.visible),void 0!==e&&t.visible(e)}function n(t,e,n){this.X$={},this._gi=L.SheetType.tableSheet,this.uNi=0,this.jIi=0,this.cNi=[[]],this.x0r=this.getColumnIndexInTable;var i=this;this.qni=new v.UndoHelper(this),i.Mf=new Y.OverWriteWorkSheet(t||$t(),this,this.qni),i.Hc(e,n)}function Ht(t){return t instanceof u}function Vt(t){return t.ctrlKey}function Ft(t){return t.shiftKey}function _t(t){return t.metaKey}function Jt(t){var e,n=(L.mt.Qf()?_t:Vt)(t),t=Ft(t);return{isCtrl:n&&!t,isShift:t&&!n}}function Xt(t,e,n){return n.isPinnedRow(e)}function Kt(t,e,n){return n.isNewRow(e)?0:n.isInsertedRow(e)?1:n.isUpdatedRow(e)?2:-1}function qt(t,e,n){return n.hasWarning(e)}function $t(){var t="TableSheet"+u._ID;return u._ID++,t}function te(t){for(var e=$t(),n=0;n<t.length;)t[n].name()===e?(e=$t(),n=0):n++;return e}f=this&&this.__assign||function(){return(f=Object.assign||function(t){for(var e,n,i,o,n=1,i=arguments.length;n<i;n++)for(o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},y=this&&this.__spreadArray||function(t,e,n){if(n||2===arguments.length)for(var i=0,o=e.length,u;i<o;i++)!u&&i in e||((u=u||Array.prototype.slice.call(e,0,i))[i]=e[i]);return t.concat(u||Array.prototype.slice.call(e))},Object.defineProperty(l,"__esModule",{value:!0}),l.BuiltInRowActions=l.TableSheet=l.ActionType=l.copyOriginalColumnPropertiesToCurrentColumn=l.hze=l.tIi=l.getSR=void 0,o=s("Common"),L=s("Core"),g=s("Tables"),d=s("./dist/plugins/tableSheet/tableSheet.interface.js"),G=s("./dist/plugins/tableSheet/actionColumn.js"),c=s("./dist/plugins/tableSheet/tablesheet-override.js"),N=s("Validation"),A=s("ConditionalFormatting"),b=s("DataManager"),B=s("./dist/plugins/tableSheet/tableSheet-row-action.js"),h=s("AutoMerge"),O=s("./dist/plugins/tableSheet/tableSheet-grouping.js"),R=s("./dist/plugins/tableSheet/tableSheet-panel/tableSheetPanel.js"),P=s("./dist/plugins/tableSheet/tableSheet-status-bar.js"),Q=s("SheetsCalc"),Y=s("./dist/plugins/tableSheet/overr-write-worksheet.js"),v=s("./dist/plugins/tableSheet/tableSheet-undo.js"),W=s("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-popup.js"),t=new o.Common.ResourceManager(c.SR,"TableSheet"),l.getSR=t.getResource.bind(t),M=o.Common.lt.tv,t=o.Common.lt,H=L.mt.j,k=t.ht,V=t._h,F=t.Dh,_=t.y0,J=t.tv,I=t.Nh,X=Number.MAX_VALUE,m="composedPinDirtyNewWarningRow",D="pinRow",j="dirtyStatus",C="warningInfo",K="removeRow",w="saveRow",q="resetRow",tt="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjEyIiBoZWlnaHQ9IjEyIiBmaWxsPSJ0cmFuc3BhcmVudCIvPgo8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTcgOUg1TDUgNS45NjA0NmUtMDhIN0w3IDlaTTYgMTBDNi41NTIyOCAxMCA3IDEwLjQ0NzcgNyAxMUM3IDExLjU1MjMgNi41NTIyOCAxMiA2IDEyQzUuNDQ3NzIgMTIgNSAxMS41NTIzIDUgMTFDNSAxMC40NDc3IDUuNDQ3NzIgMTAgNiAxMFoiIGZpbGw9IiNFNjUyNDkiLz4KPC9zdmc+Cg==",et="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjEyIiBoZWlnaHQ9IjEyIiBmaWxsPSJ0cmFuc3BhcmVudCIvPgo8cGF0aCBkPSJNNC4zOTI3MyA2LjcwMTNMMCA1LjQyNjIxTDAuNjY5MDkxIDMuMjg2ODlMNS4wNjE4MiA0Ljg3MzY3TDQuOTMwOTEgMEg3LjE1NjM2TDcuMDEwOTEgNC45NDQ1MUwxMS4zMzA5IDMuMzg2MDdMMTIgNS41Mzk1NUw3LjUzNDU1IDYuODI4ODFMMTAuNDE0NSAxMC42NjgyTDguNjEwOTEgMTJMNS45MDU0NSA3LjkxOTcyTDMuMjg3MjcgMTEuOTAwOEwxLjQ2OTA5IDEwLjYxMTZMNC4zOTI3MyA2LjcwMTNaIiBmaWxsPSIjNjY2NjY2Ii8+Cjwvc3ZnPgo=",nt="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjEyIiBoZWlnaHQ9IjEyIiBmaWxsPSJ0cmFuc3BhcmVudCIvPgo8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTYgNVYwSDVWNUgwVjZINVYxMUg2VjZIMTFWNUg2WiIgZmlsbD0iIzY2NjY2NiIvPgo8L3N2Zz4K",it="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgY2xpcC1wYXRoPSJ1cmwoI2NsaXAwXzI0XzY3NDcpIj4KPHJlY3Qgd2lkdGg9IjEyIiBoZWlnaHQ9IjEyIiBmaWxsPSJ0cmFuc3BhcmVudCIvPgo8cGF0aCBkPSJNMC42NjEyNiAxMkMwLjU2OTg4IDExLjk5OTggMC40Nzk1MzMgMTEuOTgwNyAwLjM5NTk2MyAxMS45NDM3QzAuMzEyMzk0IDExLjkwNjcgMC4yMzc0MzEgMTEuODUyOCAwLjE3NTg0IDExLjc4NTNDMC4xMTMxMzcgMTEuNzE4MyAwLjA2NTI4OTUgMTEuNjM4OSAwLjAzNTQzNjcgMTEuNTUyMkMwLjAwNTU4Mzg2IDExLjQ2NTUgLTAuMDA1NjAwMjYgMTEuMzczNCAwLjAwMjYxNzUyIDExLjI4MjFMMC4xNjM5ODUgOS41MDc2OUw3LjYxODUgMi4wNTU4MUw5Ljk0ODEyIDQuMzg0NzdMMi40OTU1OCAxMS44MzZMMC43MjExOTYgMTEuOTk3NEMwLjcwMTI3MSAxMS45OTkyIDAuNjgxMjY5IDEyLjAwMDEgMC42NjEyNiAxMlYxMlpNMTAuNDEzMSAzLjkxOTExTDguMDg0MTYgMS41OTAxNUw5LjQ4MTE0IDAuMTkzMTcxQzkuNTQyMzEgMC4xMzE5MzMgOS42MTQ5NSAwLjA4MzM1MiA5LjY5NDkxIDAuMDUwMjA2M0M5Ljc3NDg3IDAuMDE3MDYwNiA5Ljg2MDU3IDAgOS45NDcxMyAwQzEwLjAzMzcgMCAxMC4xMTk0IDAuMDE3MDYwNiAxMC4xOTkzIDAuMDUwMjA2M0MxMC4yNzkzIDAuMDgzMzUyIDEwLjM1MTkgMC4xMzE5MzMgMTAuNDEzMSAwLjE5MzE3MUwxMS44MTAxIDEuNTkwMTVDMTEuODcxMyAxLjY1MTMyIDExLjkxOTkgMS43MjM5NiAxMS45NTMxIDEuODAzOTJDMTEuOTg2MiAxLjg4Mzg4IDEyLjAwMzMgMS45Njk1OSAxMi4wMDMzIDIuMDU2MTRDMTIuMDAzMyAyLjE0MjcgMTEuOTg2MiAyLjIyODQgMTEuOTUzMSAyLjMwODM2QzExLjkxOTkgMi4zODgzMiAxMS44NzEzIDIuNDYwOTYgMTEuODEwMSAyLjUyMjEzTDEwLjQxMzggMy45MTg0NUwxMC40MTMxIDMuOTE5MTFWMy45MTkxMVoiIGZpbGw9IiM2NjY2NjYiLz4KPC9nPgo8ZGVmcz4KPGNsaXBQYXRoIGlkPSJjbGlwMF8yNF82NzQ3Ij4KPHJlY3Qgd2lkdGg9IjEyIiBoZWlnaHQ9IjEyIiBmaWxsPSJ0cmFuc3BhcmVudCIvPgo8L2NsaXBQYXRoPgo8L2RlZnM+Cjwvc3ZnPgo=",ot=$="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjEyIiBoZWlnaHQ9IjEyIiBmaWxsPSJ0cmFuc3BhcmVudCIvPgo8cGF0aCBkPSJNNC4xNjIyNSAwLjAwMTIyMzY5QzMuOTg3ODYgMC4wMDA5NDU5MjkgMy44MTU4NSAwLjA0ODI3MDUgMy42NjAwMiAwLjEzOTM5NkMzLjUwNDIgMC4yMzA1MjEgMy4zNjg5MiAwLjM2MjkwNiAzLjI2NTA0IDAuNTI1OTE3QzMuMTYxMTUgMC42ODg5MjggMy4wOTE1OCAwLjg3ODAyIDMuMDYxODkgMS4wNzhDMy4wMzIyIDEuMjc3OTkgMy4wNDMyMyAxLjQ4MzI4IDMuMDk0MDkgMS42Nzc0MUw0LjAyNDUzIDUuMjMxNjVMMy4wMzk0MiA3LjUyNDQ3QzMuMDExMDEgNy41OTA0MSAyLjk5NzU3IDcuNjYzNzIgMy4wMDAzNiA3LjczNzQxQzMuMDAzMTYgNy44MTExMSAzLjAyMjEgNy44ODI3NCAzLjA1NTM5IDcuOTQ1NTJDMy4wODg2OSA4LjAwODI5IDMuMTM1MjIgOC4wNjAxMSAzLjE5MDU3IDguMDk2MDZDMy4yNDU5MiA4LjEzMjAxIDMuMzA4MjUgOC4xNTA4OSAzLjM3MTY0IDguMTUwOUw1LjYyODM1IDguMTUwMjhWMTEuMzg4OUw2IDEyTDYuMzcxNjUgMTEuMzg4OVY4LjE1MDI4TDguNjI4MzYgOC4xNTA5QzguNjkxNzUgOC4xNTA4OCA4Ljc1NDA4IDguMTMyMDEgOC44MDk0MyA4LjA5NjA2QzguODY0NzggOC4wNjAxMSA4LjkxMTMxIDguMDA4MjkgOC45NDQ2MSA3Ljk0NTUyQzguOTc3OSA3Ljg4Mjc0IDguOTk2ODQgNy44MTExMSA4Ljk5OTY0IDcuNzM3NDFDOS4wMDI0MyA3LjY2MzcyIDguOTg4OTkgNy41OTA0MSA4Ljk2MDU4IDcuNTI0NDdMNy45NzYgNS4yMzIyNkw4Ljg5ODU1IDEuNjgyM0M4Ljk0ODc0IDEuNDg4NyA4Ljk1OTM2IDEuMjg0MTMgOC45Mjk1NSAxLjA4NDkxQzguODk5NzUgMC44ODU2ODkgOC44MzAzNSAwLjY5NzMzMSA4LjcyNjg5IDAuNTM0ODYxQzguNjIzNDQgMC4zNzIzOTEgOC40ODg3OCAwLjI0MDMwNSA4LjMzMzY4IDAuMTQ5MTQxQzguMTc4NTcgMC4wNTc5NzY2IDguMDA3MzEgMC4wMTAyNTY4IDcuODMzNTQgMC4wMDk3ODgyMkw0LjE2MjI1IDBMNC4xNjIyNSAwLjAwMTIyMzY5WiIgZmlsbD0iIzY2NjY2NiIvPgo8L3N2Zz4K",ut="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB2ZXJzaW9uPSIxLjEiIHdpZHRoPSIxMDAwIiBoZWlnaHQ9IjEwMDAiIHZpZXdCb3g9IjAgMCAxMDAwIDEwMDAiIHhtbDpzcGFjZT0icHJlc2VydmUiPgo8ZGVzYz5DcmVhdGVkIHdpdGggRmFicmljLmpzIDMuNS4wPC9kZXNjPgo8ZGVmcz4KPC9kZWZzPgo8cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJyZ2JhKDI1NSwyNTUsMjU1LDApIi8+CjxnIHRyYW5zZm9ybT0ibWF0cml4KDAgMjAuNjYxMiAyMC42NjEyIDAgNDk5Ljk5OTUgNTAwLjAwMDkpIiBpZD0iNDk5NTE3Ij4KPHBhdGggc3R5bGU9InN0cm9rZTogbm9uZTsgc3Ryb2tlLXdpZHRoOiAxOyBzdHJva2UtZGFzaGFycmF5OiBub25lOyBzdHJva2UtbGluZWNhcDogYnV0dDsgc3Ryb2tlLWRhc2hvZmZzZXQ6IDA7IHN0cm9rZS1saW5lam9pbjogbWl0ZXI7IHN0cm9rZS1taXRlcmxpbWl0OiA0OyBpcy1jdXN0b20tZm9udDogbm9uZTsgZm9udC1maWxlLXVybDogbm9uZTsgZmlsbDogcmdiKDAsMCwwKTsgZmlsbC1ydWxlOiBub256ZXJvOyBvcGFjaXR5OiAxOyIgdmVjdG9yLWVmZmVjdD0ibm9uLXNjYWxpbmctc3Ryb2tlIiB0cmFuc2Zvcm09IiB0cmFuc2xhdGUoLTI0LCAtMjQpIiBkPSJNIDE0IDM2IHEgLTUgMCAtOC41IC0zLjUgVCAyIDI0IHEgMCAtNSAzLjUgLTguNSBUIDE0IDEyIHEgNC4zIDAgNy4zMjUgMi40NSBxIDMuMDI1IDIuNDUgNC4xNzUgNS45IEggNDYgdiA3LjMgaCAtNS4zIFYgMzYgaCAtNi4zIHYgLTguMzUgaCAtOC45IHEgLTEuMTUgMy40NSAtNC4xNzUgNS45IFQgMTQgMzYgWiBtIDAgLTguNiBxIDEuNDUgMCAyLjQyNSAtMC45NzUgcSAwLjk3NSAtMC45NzUgMC45NzUgLTIuNDI1IHEgMCAtMS40NSAtMC45NzUgLTIuNDI1IFEgMTUuNDUgMjAuNiAxNCAyMC42IHEgLTEuNDUgMCAtMi40MjUgMC45NzUgUSAxMC42IDIyLjU1IDEwLjYgMjQgcSAwIDEuNDUgMC45NzUgMi40MjUgcSAwLjk3NSAwLjk3NSAyLjQyNSAwLjk3NSBaIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9nPgo8L3N2Zz4=",rt="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMjRweCIgdmlld0JveD0iMCAwIDI0IDI0IiB3aWR0aD0iMjRweCIgZmlsbD0iIzAwMDAwMCI+PGcgZmlsbD0ibm9uZSI+PHBhdGggZD0iTTAgMGgyNHYyNEgwVjB6Ii8+PHBhdGggZD0iTTAgMGgyNHYyNEgwVjB6IiBvcGFjaXR5PSIuODciLz48L2c+PHBhdGggZD0iTTIwIDhoLTNWNi4yMWMwLTIuNjEtMS45MS00Ljk0LTQuNTEtNS4xOUM5LjUxLjc0IDcgMy4wOCA3IDZ2Mkg0djE0aDE2Vjh6bS04IDljLTEuMSAwLTItLjktMi0ycy45LTIgMi0yIDIgLjkgMiAyLS45IDItMiAyek05IDhWNmMwLTEuNjYgMS4zNC0zIDMtM3MzIDEuMzQgMyAzdjJIOXoiLz48L3N2Zz4=",at="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iNDgiIHdpZHRoPSI0OCI+PHBhdGggZD0ibTExLjY1IDQ0IDMuMjUtMTQuMDVMNCAyMC41bDE0LjQtMS4yNUwyNCA2bDUuNiAxMy4yNUw0NCAyMC41bC0xMC45IDkuNDVMMzYuMzUgNDQgMjQgMzYuNTVaIi8+PC9zdmc+",E=void 0,Z=null,i=(st=".gcSheetInternal")+(ct=".tableSheet"),lt="gc.spread.contextMenu.defineColumn",ft="gc.spread.contextMenu.insertColumns",dt="gc.spread.defineColumn.submit",ht=L.util.Z0,gt=L.util.V0,l.tIi=It,l.hze=mt,l.copyOriginalColumnPropertiesToCurrentColumn=jt,bt=function(){},r={},l.BuiltInRowActions=r,vt={name:m,icons:[$,et,nt,it,tt]},L.Events.RowOperation="RowOperation",(t=Mt=l.ActionType||(l.ActionType={}))[t.remove=0]="remove",t[t.save=1]="save",t[t.reset=2]="reset",t[t.addNewEmptyRow=3]="addNewEmptyRow",n.prototype.Iwt=function(t,e,n){var i,o,u,r,a,c,s,l,f,i=e.abt.getDataView(),o=i&&i.gLt,u=this.getColumnIndexInTable(e.ubt),r=u,a=i.getColumnInfos();for(e.sbt&&(r=this.getColumnIndexInTable(e.sbt)),c=u;c<=r;c++)s=-1===c?"*":a[c].value,o._Et(t,s,n);if(l=this.rNi,n)this.rNi.push(e);else for(f=0;f<l.length;f++)l[f]===e&&(l.splice(f,1),f--)},n.prototype.startRow=function(){return 0},n.prototype.startColumn=function(){return 0},n.prototype.endRow=function(){return this.Mf.getRowCount()-(this.options.allowAddNew?2:1)},n.prototype.endColumn=function(){return this.Mf.getColumnCount()-1},n.prototype.hasTotalsRow=function(){return!1},n.prototype.hasHeadersRow=function(){return!1},n.prototype.tableName=function(){return this.name()},n.prototype.source=function(){return this.Mf.Uw()},n.prototype.getColumnName=function(t,e){var n,i,t,n,i,t=this.Mf.getDataSource().kve(t);return t?It(t.caption,e)||t.value:null},n.prototype.getColumnIndexInTable=function(t){for(var e,n,i,o,e,n,i,o=this.Mf.getDataSource().getColumnInfos().length-1;0<=o;o--)if(t===this.getColumnName(o)||t===this.getColumnName(o,!0))return o;return-1},n.prototype.showFooter=function(t){return!1},n.prototype.dataRange=function(){var t,e,t=this,e=this.Mf.getRowCount();return t.options.allowAddNew&&e--,(0,L.Ec)(0,0,e,t.endColumn()+1)},n.prototype.headerIndex=function(){return 0},n.prototype.cSt=function(t,e){var n,i;for(null!==t&&(t=H(t)),n=0;n<this.Tx;n++)if(n!==e&&t===(i=this.getColumnName(n)))return!0;return!1},n.prototype.Hc=function(t,e){var r,a,r=this,a=r.Mf;a.$L.push({tF:function(t,e){var n,i,o,e,i,u;return void 0===e&&(e={}),n=1,i=r.keo(),o=e.spacingIndex,e=e.isAfter,(0,O.isGroupOutlineCondensedLayout)(i)&&(n=0),i=X,e&&(u=a.ncr&&a.ncr(t,o,-1,e))&&"number"==typeof u.level&&(i=u.level+1),r.sMi(t,i,n,o,e)}}),r.rNi=[],this.suspendPaint(),r.aNi(),r.setDefaultRowHeight(30),r.setDefaultRowHeight(30,L.SheetArea.colHeader),a.setRowCount(0),a.setColumnCount(0),a.zwt("@","=A1",0,0,!0),a.zwt("AutoId","=MAX(OFFSET($A$1,0,COLUMN()-1,ROW()-1,1))+1",0,0,!0),(0,c.overrideProperties)(r),(0,c.overrideMethods)(r),r.v7(),t&&a.setDataSource(t),r.Zp(e),r.sNi(),r.MNi(),r.INi=new O.Grouping,r.rowActionOptions(r.gNi),r.applyTableTheme(g.TableThemes.light2),r.leV(),this.resumePaint()},n.prototype.leV=function(){},n.prototype.reset=function(){var t,e,n,i,o,e=this,n=e.Mf,i=e.UMi,o=e.getDataView();o&&(null!=(t=o.PEe)&&t.call(o),e.rze()),n&&n.reset(),i&&i.detach(),e.E3(),e.Hc(),e.lMi=E,e.jFt=E,e.jIi=0,e.uNi=0},n.prototype.hg=function(t){this.Mf.hg(t)},n.prototype.L0=function(t,e){var n,i,o,n=this.Mf;if(t&&this.z2i(!0),this.wze&&this.wze.close(),n.L0(t,e),t)for(i in this.E3(),this.UMi&&this.UMi.detach(),this.X$)this.X$[i]&&(o=this.X$[i]).onDestroy(!0)},n.prototype.Zg=function(t){this.Mf.Zg(t)},n.prototype.setAccessibilityPrefixSuffixContent=function(t,e){this.Mf.setAccessibilityPrefixSuffixContent(t,e)},n.prototype.pM=function(t){this.Mf.pM(t),this.NNi()},n.prototype.getParent=function(){return this.Mf.getParent()},n.prototype.name=function(t){var e=this.Mf;return e.name.apply(e,arguments)},n.prototype.visible=function(t){var e=this.Mf;return e.visible.apply(e,arguments)},n.prototype.b2=function(t,e){return this.Mf.b2(t,e)},n.prototype.ow=function(){this.Mf.ow()},n.prototype.aw=function(){this.Mf.aw()},n.prototype.tw=function(){this.Mf.tw()},n.prototype.Wb=function(t,e){this.Mf.Wb(t,e)},n.prototype.Kb=function(t){this.Mf.Kb(t)},n.prototype.Fb=function(t,e){this.Mf.Fb(t,e)},n.prototype.onHorizontalDockScroll=function(t,e){this.Mf.onHorizontalDockScroll(t,e)},n.prototype.Gb=function(t){this.Mf.Gb(t)},n.prototype.WC=function(t,e,n){this.Mf.WC(t,e,n)},n.prototype.$C=function(t){this.Mf.$C(t)},n.prototype.QC=function(t,e,n){this.Mf.QC(t,e,n)},n.prototype.Cg=function(){this.Mf.Cg()},n.prototype.suspendPaint=function(){this.Mf.suspendPaint()},n.prototype.resumePaint=function(){var t=this.Mf.getDataSource();t&&this.lNi(t.visibleLength()),this.Mf.resumePaint()},n.prototype.isPaintSuspended=function(){return this.Mf.isPaintSuspended()},n.prototype.bind=function(t,e,n){this.Mf.bind(t,e,n)},n.prototype.unbind=function(t,e){this.Mf.unbind(t,e)},n.prototype.unbindAll=function(){this.Mf.unbindAll()},n.prototype.D3=function(t,e,n){this.Mf.D3(t+i,e,n)},n.prototype._3=function(t,e){this.Mf._3(t+i,e)},n.prototype.E3=function(){this.Mf.E3(),this.Mf.unbind(i)},n.prototype.suspendEvent=function(){this.Mf.suspendEvent()},n.prototype.resumeEvent=function(){this.Mf.resumeEvent()},n.prototype.isEventSuspended=function(){return this.Mf.isEventSuspended()},n.prototype.bC=function(){this.Mf.bC()},n.prototype.ki=function(t,e,n){this.Mf.ki(t,e,n)},n.prototype.isEditing=function(){return this.Mf.isEditing()},n.prototype.ol=function(t,e,n,i){return this.Mf.ol(t,e,n,i)},n.prototype.currentTheme=function(t){var e=this.Mf;return e.currentTheme.apply(e,arguments)},n.prototype.FM=function(){(0,l.getSR)(),this.Mf.FM()},n.prototype.DNi=function(t){var e,t=t||this.gNi;return J(t).map(function(t){return delete t.subActionOptions,t})},n.prototype.fNi=function(t,e){var n,i,o,u,r,a,c,s,l,f,d,d,n=[m,D,j,C,K,w,q],i=[$,tt,et,nt,it],o=["ROW_ACTION_PIN_ROW","ROW_ACTION_WARNING_INFO","ROW_ACTION_NEW_ROW","ROW_ACTION_INSERTED_ROW","ROW_ACTION_UPDATED_ROW"],u=e?o:i,r=e?i:o;if(I(t))for(a=0;a<t.length;a++)if(s=(c=t[a]).name,l=c.icons,-1<n.indexOf(s)&&l)for(f=0;f<l.length;f++)"string"==typeof(d=l[f])&&-1<(d=u.indexOf(d))&&(l[f]=r[d]);return t},n.prototype.dNi=function(t,e){var n,i,o,u,r,u,u,a,r,c,s,l,f,d,c,s,r,n=this,i=n.Mf;return t&&t.saveAsView?(e.colHeaderData.dataTable||(e.colHeaderData.dataTable={}),Lt(i,(0,L.Ec)(0,0,i.getRowCount(1),i.getColumnCount(1)),e.colHeaderData.dataTable,1),n.iAi(e),e.data.dataTable||(e.data.dataTable={}),Ot(i,e.conditionalFormats,e.data.dataTable),delete e.conditionalFormats,delete e.validations):k(e)||n.iAi(e),o={},u=n.getDataView(),r=t&&t.gp,u&&(o.dataView={name:u.name,tableName:u.gLt.name},o.grouping=n.INi.toJSON(),r)&&(o.grouping.groupInfos=M(n.lMi)),u=i.rowFilter(),o.rowFilter=u.toJSON(),r&&Tt(e,o),u=n.options,(a={}).allowAddNew=u.allowAddNew,u.hasOwnProperty("defaultStackRowHeight")&&(a.defaultStackRowHeight=u.defaultStackRowHeight),u.hasOwnProperty("menuItemVisibility")&&(a.menuItemVisibility=u.menuItemVisibility),a.sheetTabColor=u.sheetTabColor,(r=u.alternatingRowOptions)?(c=r.style,s=void 0,Array.isArray(c)?s=[l=c[0]&&c[0].toJSON(),f=c[1]&&c[1].toJSON()]:c&&(s=c.toJSON()),a.alternatingRowOptions={step:r.step,style:s}):null===r&&(a.alternatingRowOptions=null),u.hasOwnProperty("isDesignMode")&&(a.isDesignMode=u.isDesignMode),u.hasOwnProperty("showRowNumber")&&(a.showRowNumber=u.showRowNumber),u.hasOwnProperty("enableDefineColumn")&&(a.enableDefineColumn=u.enableDefineColumn),u.hasOwnProperty("defineColumnCommand")&&(a.defineColumnCommand=u.defineColumnCommand),u.hasOwnProperty("submitDefineColumnCommand")&&(a.submitDefineColumnCommand=u.submitDefineColumnCommand),u.hasOwnProperty("columnTypeItems")&&(a.columnTypeItems=u.columnTypeItems),u.hasOwnProperty("groupLayout")&&(d=u.groupLayout)&&["mode","position"].forEach(function(t){var e=d[t];d.hasOwnProperty(t)&&(a.groupLayout||(a.groupLayout={}),a.groupLayout[t]=e)}),o.options=a,o.rowActionOptions=n.fNi(n.DNi()),n.hl&&(o.currentTheme=n.hl.toJSON()),t&&t.cb===L.OpenSaveFileType.excel&&e&&(c=St(o.options.alternatingRowOptions),s=o.currentTheme,c)&&(c.fillRowStyle||c.blankRowStyle)&&s&&(s.KQr=!0,s.firstRowStripSize=c.fillRowStep,s.firstRowStripStyle=c.fillRowStyle,s.secondRowStripSize=c.blankRowStep,s.secondRowStripStyle=c.blankRowStyle),r=i.defaults,o.defaults={rowHeight:r.rowHeight,colHeaderRowHeight:r.colHeaderRowHeight},o.printInfo=i.printInfo&&i.printInfo().toJSON(),o.freeHeaderArea=n.hNi,e&&(e.sheetType=n._gi),o.parameter=n.o1e,o},n.prototype.toJSON=function(t){var e,n,n,e=this,n=e.Mf;return n.options.sheetTabColor=this.options.sheetTabColor,t&&t.cb===L.OpenSaveFileType.excel&&(e.lno=!0),(n=n.toJSON(t)).addition=e.dNi(t,n),(n=e.Keo(n,t)).sheetType=this._gi,t&&t.cb===L.OpenSaveFileType.excel&&(e.lno=E),n},n.prototype.Keo=function(t,e){var n,i,o,u,r,n=this,i=n.Mf;return e&&e.cb===L.OpenSaveFileType.excel&&n.Teo(!0)&&(o=new L.Worksheet(n.Mf.name()),(u=t.addition)&&!k(u.currentTheme)&&((r=new g.TableTheme).fromJSON(u.currentTheme),o.suspendPaint(),n.qeo(o,M(n.options.alternatingRowOptions),r),o.resumeEvent()),delete t.spacing,o.fromJSON(t,!1,E,E,!0),n.$eo()?n.Qto(o,i):n.Yto(o,i),n.Wto(o),(t=o.toJSON(e)).addition=u),t},n.prototype.Wto=function(t){for(var e,n,i,o,u,e=this,n=e.getDataView().getColumnInfos(),i=0;i<n.length;i++)if(!1!==(o=n[i]).visible){(u=t.getStyle(-1,i)).textIndent=e.lMi.length,t.setStyle(-1,i,u);break}},n.prototype.Hto=function(t){var e,n,i,o,u,t,r,e,n,i=this.getDataView().getColumnInfos(),o=new L.Style;return o.backColor="white",t=(u=t.Ut.Vsr).Ysr,{columnInfos:i,spacingStyle:o,rows:r=Object.keys(t).reverse()}},n.prototype.Yto=function(t,e){var n,i,o,u,r,a,c,s,l,f,d,h,g,b,v,M,I,m,N,D,j,C,w,p,x,y,A,T,S,z,z,z,S;if(t.Csr)for(o=(i=(n=this).Hto(e)).columnInfos,u=i.spacingStyle,a=0,c=r=i.rows;a<c.length;a++){if(s=c[a],l=Number(s),0<(f=2*e.Rsr(l,!0)))for(t.addRows(l+1,f),d=0;d<f;d+=2)if(h=Math.floor(d/2),b=(g=l+d+1)+1,v=e.Xsr(l,d,!0)/2,t.setRowHeight(g,v),t.setRowHeight(b,v),M=e.ncr(l,h,-1,!0),!k(M))if(I={rowSpacingIndex:h,isAfterRowSpacing:!0},M.isSpacing){for(m=e.getActualStyle(l,-1,E,E,I),u.borderTop=m.borderTop,N=0;N<o.length;N++)t.setStyle(g,N,u);t.setRowHeight(g,2*v),t.deleteRows(b,1)}else for(m=e.getActualStyle(l,-1,E,E,I),N=0;N<o.length;N++)(D=m.clone()).borderBottom=Z,t.setStyle(g,N,D),(j=m.clone()).borderTop=Z,t.setStyle(b,N,j),C=e.ncr(l,h,N,!0),k(C)||(w=e.Jt.qt(l,N,E,E,I),(p=(0,O.composeHeaderStyle)(C,w)).hAlign=(0,O.getHorizontalAlign)(p,L.HorizontalAlign.right),x=C.caption,k(x)||(p.borderBottom=Z,t.setStyle(g,N,p),t.setValue(g,N,C.caption)),(y=(0,O.composeValueStyle)(C,w)).hAlign=(0,O.getHorizontalAlign)(y,L.HorizontalAlign.right),A=C.value,k(A))||(y.borderTop=Z,t.setStyle(b,N,y),t.setValue(b,N,C.value));if(0<(f=2*e.Rsr(l)))for(t.addRows(l,f),d=0;d<f;d+=2)if(h=Math.floor(d/2),b=(g=l+d)+1,v=e.Xsr(l,h)/2,t.setRowHeight(g,v),t.setRowHeight(b,v),M=e.ncr(l,h,-1))for(m=e.getActualStyle(l,-1,E,E,I={rowSpacingIndex:h,isAfterRowSpacing:!1}),T=!1,N=0;N<o.length;N++)(D=m.clone()).borderBottom=Z,t.setStyle(g,N,D),(j=m.clone()).borderTop=Z,t.setStyle(b,N,j),S=!T&&!1!==o[N].visible,w=e.Jt.qt(l,N,E,E,I),y=void 0,S&&(T=!0,p=(0,O.composeHeaderStyle)(M.groupLevelOptions,w),z=M.field,k(z)||(p.textIndent=M.level,p.borderBottom=Z,t.setStyle(g,N,p),t.setValue(g,N,M.field)),y=(0,O.composeValueStyle)(M.groupLevelOptions,w),z=M.value,k(z)||(y.textIndent=M.level,y.borderTop=Z,t.setStyle(b,N,y),t.setValue(b,N,M.value))),C=e.ncr(l,h,N),k(C)||S||((z=(0,O.composeHeaderStyle)(C,w)).hAlign=(0,O.getHorizontalAlign)(z,L.HorizontalAlign.right),x=C.caption,k(x)||(z.borderBottom=Z,t.setStyle(g,N,z),t.setValue(g,N,C.caption)),(S=(0,O.composeValueStyle)(C,w)).hAlign=(0,O.getHorizontalAlign)(S,L.HorizontalAlign.right),A=C.value,k(A))||(S.borderTop=Z,t.setStyle(b,N,S),t.setValue(b,N,C.value))}},n.prototype.Qto=function(t,e){var n,i,o,u,r,a,c,s,l,f,d,h,g,b,v,M,I,m,N,D,j,C,w,p,p;if(t.Csr)for(o=(i=(n=this).Hto(e)).columnInfos,u=i.spacingStyle,a=0,c=r=i.rows;a<c.length;a++){if(s=c[a],l=Number(s),0<(f=e.Rsr(l,!0)))for(t.addRows(l+1,f),d=0;d<f;d++)if(h=l+d+1,g=e.Xsr(l,d,!0),t.setRowHeight(h,g),b=e.ncr(l,d,-1,!0),!k(b))if(v={rowSpacingIndex:d,isAfterRowSpacing:!0},b.isSpacing)for(M=e.getActualStyle(l,-1,E,E,v),u.borderTop=M.borderTop,I=0;I<o.length;I++)t.setStyle(h,I,u);else for(M=e.getActualStyle(l,-1,E,E,v),I=0;I<o.length;I++)t.setStyle(h,I,M),m=e.ncr(l,d,I,!0),k(m)||(N=e.Jt.qt(l,I,E,E,v),(D=(0,O.composeValueStyle)(m,N)).hAlign=(0,O.getHorizontalAlign)(D,L.HorizontalAlign.right),j=(0,O.getValueFromCondensed)(m),k(j))||(t.setStyle(h,I,D),t.setValue(h,I,j));if(0<(f=e.Rsr(l)))for(t.addRows(l,f),d=0;d<f;d++)if(h=l+d,g=e.Xsr(l,d),t.setRowHeight(h,g),b=e.ncr(l,d,-1))for(M=e.getActualStyle(l,-1,E,E,v={rowSpacingIndex:d,isAfterRowSpacing:!1}),C=!1,I=0;I<o.length;I++)t.setStyle(h,I,M),w=!C&&!1!==o[I].visible,N=e.Jt.qt(l,I,E,E,v),D=void 0,w&&(C=!0,D=(0,O.composeValueStyle)(b.groupLevelOptions,N),p=(0,O.getGroupHeaderValueFromCondensed)(b),k(p)||(D&&(D.textIndent=b.level,t.setStyle(h,I,D)),t.setValue(h,I,p))),m=e.ncr(l,d,I),k(m)||w||((p=(0,O.composeValueStyle)(m,N)).hAlign=(0,O.getHorizontalAlign)(p,L.HorizontalAlign.right),j=(0,O.getValueFromCondensed)(m),k(j))||(t.setStyle(h,I,p),t.setValue(h,I,j))}},n.prototype.TNi=function(t){var e,n,i,o,o,o,i,u,o,i,r,a,c,n;if(t){if(n=(e=this).Mf,i=t.options,k(t.currentTheme)||((o=new g.TableTheme).fromJSON(t.currentTheme),e.applyTableTheme(o)),o=e.getDefaultOptions(),e.options.allowAddNew=i.allowAddNew,e.options.defaultStackRowHeight=i.defaultStackRowHeight,e.options.menuItemVisibility=i.menuItemVisibility,e.options.isDesignMode=i.isDesignMode,e.options.sheetTabColor=i.sheetTabColor,e.options.showRowNumber=i.showRowNumber,e.options.enableDefineColumn=i.enableDefineColumn,e.options.defineColumnCommand=(k(i.defineColumnCommand)?o:i).defineColumnCommand,e.options.submitDefineColumnCommand=(k(i.submitDefineColumnCommand)?o:i).submitDefineColumnCommand,e.options.columnTypeItems=(k(i.columnTypeItems)?o:i).columnTypeItems,e.options.groupLayout=(k(i.groupLayout)?o:i).groupLayout,e.bNi(i.alternatingRowOptions,i.alternatingRowStyles),k(t.actionColumn)||e.actionColumn.fromJSON(t.actionColumn),e.rowActionOptions(e.fNi(t.rowActionOptions,!0)),o=n.defaults,(i=t.defaults)&&(o.rowHeight=i.rowHeight,o.colHeaderRowHeight=i.colHeaderRowHeight),n.printInfo&&t.printInfo&&((o=new(u=s("Print")).PrintInfo).fromJSON(t.printInfo),n.printInfo(o)),i=t.stateRules)for(r=0,a=i;r<a.length;r++)(c=a[r]).isRow?e.addRowStateRule(c.state,c.style):e.addColumnStateRule(c.state,c.style);(n=t.freeHeaderArea)&&e.applyFreeHeaderArea(n),e.o1e=t.parameter}},n.prototype.fromJSON=function(t,e,n,i,o){var u,r,a,c,s,l,f,d,h,d,f,e,n,i,u=this,r=u.Mf,a=i&&i.gp,c=i&&i.sb,s=i&&i.cb,l=t.addition,f=l.options;a||c&&s===L.OpenSaveFileType.excel?(c&&s===L.OpenSaveFileType.excel&&(k(l.currentTheme)||((d=new g.TableTheme).fromJSON(l.currentTheme),u.applyTableTheme(d)),f)&&(u.options.sheetTabColor=f.sheetTabColor),a&&(u.options||(u.options={}),u.bNi(f.alternatingRowOptions,f.alternatingRowStyles),l.grouping&&(l.grouping.tree&&u.INi.fromJSON(r,l.grouping,!0),l.grouping.groupInfos)&&u.Vto(l.grouping.groupInfos),f.groupLayout&&u.Fto(f.groupLayout),u.options.allowAddNew=!1),t.rowFilter=E,r.fromJSON(t,e,n,i,o),a&&(l.options.showRowNumber?(r.setColumnCount(h=1,L.SheetArea.rowHeader),r.setColumnVisible(0,!0,L.SheetArea.rowHeader),r.setColumnWidth(0,r.defaults.rowHeaderColWidth,L.SheetArea.rowHeader)):(r.setColumnCount(0,L.SheetArea.rowHeader),1!==(d=r.printInfo()).showRowHeader()&&d.showRowHeader(1)))):c&&s===L.OpenSaveFileType.sjs?(r.ub(t.name+"",!0),r.b2(t.isSelected),r.g8(t.zoomFactor||1),u.TNi(l),u.ANi(l)):(r.ub(t.name+"",!0),r.b2(t.isSelected),r.g8(t.zoomFactor||1),Wt(r,t.visible),u.TNi(l),f=l&&l.dataView,e=r.parent&&r.parent.dataManager(),(i=(n=f&&e.tables[f.tableName])&&n.views[f.name])&&u.kTi(i,!1))},n.prototype.ANi=function(t,e){var n,i,o,u,i,i,i,n=this,i=n.Mf,o=t.dataView,u,i=i.parent.dataManager(),i=o&&i.tables[o.tableName],i=i&&i.views[o.name];n.fromJSONAfterViewFetchedImp(t,i,e)},n.prototype.fromJSONAfterViewFetchedImp=function(t,e,n){var i,o,e;e&&(o=(i=this).Mf,i.suspendPaint(),i.restoreView(e,n),i.INi.fromJSON(i.Mf,t.grouping),e=o.rowFilter(),t.rowFilter&&e.fromJSON(t.rowFilter),i.vNi(i.options.isDesignMode),i.resumePaint())},n.prototype.resetNewView=function(t,e,n){var i,o,u,r,a,c,s,i,r,a,i=this.getDataView(),o=i.gLt,u=i.name,r=i.ILt,a=i.includeDefaultColumns,c=i.LEt,s=i.SRe(),i=i.toJSON(),r={columnInfoOptions:r,includeDefaultColumns:a,options:c};o.removeView(u),(a=o.addView(u,t,e,n)).fromJSON(i),a.ARe(s),this.qni.addUndoChanges({type:v.TableSheetUndoType.ResetViewChanged,oldData:r}),this.restoreView(a)},n.prototype.restoreView=function(t,e){for(var n,i,o,u,e,r,e,u,a,a,n=this,i=n.Mf,o=0;o<t.lFt.length;o++)t.lFt[o]>=t.length()&&(t.lFt.splice(o,1),o--);n.suspendPaint(),t.visibleLength()!==t.length()&&(t.gFt(),t.hde()),(u=t.getColumnInfos()).some(function(t){return t.isGroup})&&(u=u.filter(function(t){return!t.isGroup}),t.setColumnInfos(u)),e?n.refreshBindColumns(E,t):n.kTi(t),e=t.DLt&&0<t.DLt.length,r=t._Lt&&0<t._Lt.length,e&&(t.isHierarchy()?t.KFt(t.DLt):t.JFt(t.DLt)),u=Qt(e=t.NLt,u),e&&!u&&t.filter(e),0<(a=t.lFt.length)&&(i.frozenRowCount(a),n.uNi=a),0<(a=t.CLt.length)&&(i.frozenColumnCount(a),n.jIi=a),t.MFt&&(n.groupBy(t.MFt),e&&u&&t.filter(e),r)&&t.HFt(t._Lt),n.resumePaint()},n.prototype.bNi=function(t,e){t?this.options.alternatingRowOptions={step:t.step,style:this.jNi(t.style)}:e?this.options.alternatingRowOptions={step:[1,1],style:this.jNi(e)}:null!==t&&null!==e||(this.options.alternatingRowOptions=null)},n.prototype.Vto=function(t){this.lMi=t},n.prototype.Fto=function(t){this.options.groupLayout=t},n.prototype.jNi=function(t){var e,n,e,e=Array.isArray(t)?(n=new L.Style,e=new L.Style,t[0]&&n.fromJSON(t[0],!1,null),t[1]&&e.fromJSON(t[1],!1,null),[n,e]):((n=new L.Style).fromJSON(t,!1,null),n);return e},n.prototype.wh=function(t,e,n){var i,o,o,i=this,o=i.Mf;return n&&n.fileType===L.OpenSaveFileType.excel?(o.options.sheetTabColor=i.options.sheetTabColor,o.wh(t,e,n)):((o=o.wh(t,e,n)).lossless.addition=i.dNi(),o)},n.prototype.yh=function(t,e,n,i){var o,u,r,t,t,r,r,r,t,o,r,o=this,u=o.Mf,r=t.lossless.addition;i||((t=L.util.o1(t,"sheetViews&sheetView"))&&(t.zoomScale&&u.zoom(t.zoomScale/100),"1"===t.tabSelected)&&u.b2(!0,!1),o.TNi(r)),t=r&&r.dataView,r=u.parent&&u.parent.dataManager(),(r=(r=t&&r.tables[t.tableName])&&r.views[t.name])&&o.kTi(r,i),i||(r=(o=(t=u.parent)&&t.v2)&&o.sheetsState)&&(r[u.name()]=2)},n.prototype.uw=function(){return this.Mf.uw()},n.prototype.ot=function(){return this.Mf.ot()},n.prototype.hitTest=function(t,e,n,i){return this.Mf.hitTest(t,e,n,i)},n.prototype.getSheet=function(){return this.Mf},n.prototype.getSelections=function(t){return t===L.SheetArea.colHeader?this.Mf.Ut.getSelections(1):this.Mf.getSelections()},n.prototype.zoom=function(t){return 0===arguments.length?this.Mf.zoom():this.Mf.zoom(t)},n.prototype.autoFitRow=function(t){this.Mf.autoFitRow(t)},n.prototype.getColumnWidth=function(t,e,n){return this.Mf.getColumnWidth(t,e,n)},n.prototype.getRowHeight=function(t,e,n){return this.Mf.getRowHeight(t,e,n)},n.prototype.getColumnCount=function(t){return this.Mf.getColumnCount(t)},n.prototype.getRowCount=function(t){return this.Mf.getRowCount(t)},n.prototype.setDataView=function(t){this.kTi(t)},n.prototype.uLi=function(t){var e,n,e=this.Mf,n;(t||this.getDataView())&&e.setRowCount(t.visibleLength()+(this.options.allowAddNew?1:0))},n.prototype.Ski=function(t){var e=this.Mf;t&&(e.setColumnCount(t.getColumnInfos().length),this.uLi(t))},n.prototype.rze=function(){this.Mf.parent.Wp.clear()},n.prototype.kTi=function(t,e){var n,i,o,n,n,i=this.Mf;i.xT||(this.suspendPaint(),i.suspendCalcService(),n=(null==(o=this.getDataView())?void 0:o.name)===(null==t?void 0:t.name),t&&this.z2i(!n),!n&&o&&(null!=(n=o.PEe)&&n.call(o),this.rze()),this.beforeSetDataView(t),i.autoGenerateColumns=!1,i.setDataSource(t),this.Ski(t),t&&(this.refreshBindColumns(),e||(this.actionColumn.options().visible&&this.zNi(),n=t.iRt&&t.iRt.length,this.options.allowAddNew&&n===i.getRowCount()&&this.CNi(),this.wNi(),this.yNi())),this.afterSetDataView(t),i.resumeCalcService(!1),this.resumePaint())},n.prototype.beforeSetDataView=function(t){},n.prototype.afterSetDataView=function(t){},n.prototype.yNi=function(){var o=this,n=o.getDataView();o.G2i=function(t,e){o.mNi(e.row)},o.U2i=function(t,e){o.LNi(e.row,e.changingFields)},o.B2i=function(t,e){o.xNi(e.row)},o.Q2i=function(t,e){o.SNi(e.row)},o.R2i=function(t,e){var n,i,n,i=o.getDataView().tableName;e.tableName===i&&o.ZNi(e.row)},o.Y2i=function(t,e){o.ONi(e.row,e.reason,e.type)},o.H2i=function(t,e){o.ENi()},o.W2i=function(t,e){o.GNi(n,e.isDataLengthChanged,e.isDataRowOrderChanged,e.isColumnCountChanged)},n.bind(b.Events.ItemInserted,o.G2i),n.bind(b.Events.ItemUpdated,o.U2i),n.bind(b.Events.ItemReset,o.B2i),n.bind(b.Events.ItemSaved,o.Q2i),n.bind(b.Events.ItemRemoved,o.R2i),n.bind(b.Events.ChangesSubmittedRowFailed,o.Y2i),n.bind(b.Events.ChangesSubmitted,o.H2i),n.bind(b.Events.ChangesCancelled,o.W2i)},n.prototype.z2i=function(t){var e=this,n=e.getDataView();n&&n.gLt&&(e.G2i&&n.unbind(b.Events.ItemInserted,e.G2i),e.U2i&&n.unbind(b.Events.ItemUpdated,e.U2i),e.B2i&&n.unbind(b.Events.ItemReset,e.B2i),e.Q2i&&n.unbind(b.Events.ItemSaved,e.Q2i),e.R2i&&n.unbind(b.Events.ItemRemoved,e.R2i),e.Y2i&&n.unbind(b.Events.ChangesSubmittedRowFailed,e.Y2i),e.H2i&&n.unbind(b.Events.ChangesSubmitted,e.H2i),e.W2i&&n.unbind(b.Events.ChangesCancelled,e.W2i),t)&&n.wpt()},n.prototype.pNi=function(t){var e,n,t,n,e,n=this.Mf.getDataSource(),t=n.kve(t)||{},n="string"==typeof t.value&&!n.GPt(t.value)&&!t.isCross;return!!(t.readonly||t.isGroup||t.isPrimaryKey||n)},n.prototype.jOt=function(t){var e,n,i,e,n,i;return(this.Mf.getDataSource().kve(t)||{}).isPrimaryKey},n.prototype.kNi=function(t){for(var e,n,i,i,e=1,n=0;n<t.length;n++)(i=t[n].caption)&&e<(i="string"==typeof i?1:i.length)&&(e=i);return e},n.prototype.UNi=function(){var t=0,e=this.hNi;return t=e?e.rowCount:t},n.prototype.QNi=function(t,e,n){var i,o,u,i=this.Mf,o,u;i.getRowCount(L.SheetArea.colHeader)!==t+n&&(i.setRowCount(t+n,L.SheetArea.colHeader),i.ki(L.Events.TableSheetColHeaderRowCountChanged,null,!0)),i.setColumnCount(e,L.SheetArea.colHeader)},n.prototype.RNi=function(t){var e,n,i,o,u,e=this.Mf,n=e.getRowCount(L.SheetArea.colHeader),i=e.getColumnCount(L.SheetArea.colHeader);for(e.clear(0,0,n,i,L.SheetArea.colHeader,L.StorageType.data|L.StorageType.style),e.Cb.clear(L.SheetArea.colHeader),o=0;o<n;o++)if(e.setCellType(o,-1,void 0,L.SheetArea.colHeader),t)for(u=0;u<i;u++)this.wDi(o,u,null,E,L.SheetArea.colHeader)},n.prototype.PNi=function(){var t,e,n,i,o,o,u,o,r,a,c,s,l,f,d,h,t=this.hNi,e=this.Mf;if(t){for(n=e.Ut,i=L.SheetArea.colHeader,o=e.Z_(-1,-1,i),n.W_(i).fromJSON(t.data,!0,{}),e.setStyle(-1,-1,o,i),n.Q_(!0,i).fromJSON(t.rows),u=(o=n.K_(i)).spans,t.spans&&t.spans.length?o.fromJSON(t.spans):u&&u.length&&o.removeSpan(0,u.length),(o=t.data.dataTable)&&(e.Xbt=t.sharedFormulas,(0,Q.dMt)(e,o,e.getRowCount(i),e.getColumnCount(i),i)),(r=new L.CellTypes.Text).isImeAware=function(){return!1},a=0;a<t.rowCount;a++)e.getCell(a,-1,i).cellType(r).locked(!1);for(c=this.UNi(),s=n.getColumnCount(),n.setFreeAreaRowCount(c,i),n.setFreeAreaColumnCount(s,i),t.columnCount=s,l=0;l<s;l++)if(f=e.Z_(-1,l,i))for(a=0;a<c;a++)(d=e.Z_(a,l,i))?((h=new L.Style).h5(f),h.h5(d),n.setStyle(a,l,h,i)):n.setStyle(a,l,f,i)}else n=e.Ut,i=L.SheetArea.colHeader,n.setFreeAreaRowCount(0,i)},n.prototype.YNi=function(t,e,n,i){var o,u,o,u,r,o=this.hNi;o&&((r=(r=(u=(u=(o=(o=(u=(u=o.data)||(o.data={})).dataTable)||(u.dataTable={}))[t])||(o[t]={}))[e])||(u[e]={}))[n]=i)},n.prototype.WNi=function(t,e){var n,i,n=this.rNi;for(I(t)&&(t=t.join(",")),I(e)&&(e=e.join(",")),i=0;i<n.length;i++)n[i].ubt===t&&(n[i].ubt=e),n[i].sbt===t&&(n[i].sbt=e)},n.prototype.HNi=function(t){var e,n,n,i,o,u,r,a,e=this.Mf,n=e.getDataSource();if(n&&(n=n.kve(t))){for(i=[],u=e.Ut.getFreeAreaRowCount(o=1),r=e.getRowCount(1);u<r;u++)i.push(e.getText(u,t,1));this.WNi(n.caption,i),a=i.every(function(t,e,n){return 0===e||t===n[0]}),n.caption=a?i[0]:i}},n.prototype.VNi=function(t,e,n){var i,o,u,r,a,c,s;if(1<t){for(o=(i=this.Mf).getRowCount(L.SheetArea.colHeader),u=0;u<n.length;u++)if(r=n[u].caption||n[u].value){for(a="string"==typeof r?[r]:r,c=0;c<a.length;c++)i.setValue(e+c,u,a[c],L.SheetArea.colHeader);for(s=a[c-1];c<o;c++)i.setValue(e+c,u,s,L.SheetArea.colHeader)}i.autoMerge(new L.Range(e,-1,t,-1),h.AutoMergeDirection.rowColumn,h.AutoMergeMode.restricted,L.SheetArea.colHeader)}},n.prototype.getFilterButtonVisibleFromColumnInfo=function(t,e,n){var e,i,e,e=e[t],i=!1!==e.allowSort||!1!==e.allowFilterByValue||!1!==e.allowFilterByList;return i=k(e.allowSort)&&k(e.allowFilterByValue)&&k(e.allowFilterByList)&&(e=n.getValue(0,t))&&"object"==typeof e&&!o.Common.ct.W0(e)&&k(e._error||e._code)?!1:i},n.prototype.yze=function(t){var e,n,i=this.Mf.rowFilter();return i&&i.Dit(t)},n.prototype.zze=function(t){var e,n,t,e,n=this.Mf,t={tableName:E,colIndex:t};n.Qy().execute({cmd:"clearFilter",sheetName:n.name(),cmdOption:t})},n.prototype.pze=function(t){var e,n,i=this.Mf.rowFilter();return i&&i.getSortState(t)!==L.SortState.none},n.prototype.xze=function(t){var e,n,i,e,e=this,n,i;return e.Mf.rowFilter().Sze(t),(e=e.getDataView()).UGe(),e.PGe(e.kve(t))},n.prototype.Lze=function(t){var e=this,t=e.fDi(t),e=e.getDataView();return t&&(t.GFt||e.$Lt(e.ife(t),!0))},n.prototype.Oze=function(t){var e,n=this.getDataView();return n.uii(n.kve(t))},n.prototype.iGe=function(o,u){var r,a,r=this,a=r.getDataView();return!a.UFe(o)&&(u=u||a.kve(o),r.suspendPaint(),a.iUe(u,function(){var t,e,n,i,n,t,e,t=a.Dit();r.yze(o)&&r.zze(o),e=!t||!a.Dit(),i=n=!1,r.pze(o)&&(i=!0,n=r.xze(o),e||!n||t||(e=!0)),n=!1,r.Lze(o)&&(n=r.Oze(o)),i&&a.Dit()&&(a.dFt(),a.vFt(!0)),e&&i&&a.gFt(),n?(t=a.autoSort,a.autoSort=!0,r.removeGroupBy(),a.autoSort=t):(a.VGe(!0),r.groupBy(a.groupBy()),a.zGe(!0)),r.Zze(o)?(r.togglePinnedColumns([o]),e=r.bze(r.tDi(u)),r.fno(e)):r.fno(o)},function(){r.refreshBindColumns(),r.Seo(),r.kze(o)}),r.resumePaint(),!0)},n.prototype.fno=function(t,e,n){var i=this;i.Mf.KBe(t,e=void 0===e?1:e,E,n),!1!==n&&i.qni.addUndoChanges({type:v.TableSheetUndoType.RemoveColumns,oldCol:t,oldCount:e})},n.prototype.JEe=function(e,t,n){var i,o,i=this,o=i.getDataView();return(n=n||o.getColumn(e))&&t&&jt(t,n),!(o.UFe(e)||!i.Ceo(t,n)||(i.suspendPaint(),o.aUe(n,t,function(t){t.isFilterUpdated?(i.zze(e),o.VGe(!0),i.groupBy(o.groupBy()),o.zGe(!0)):t.isSortUpdated?(o.VGe(!0),i.groupBy(o.groupBy()),o.zGe(!0)):t.isGroupUpdated&&(o.VGe(!0),o.zGe(!0))},function(){i.refreshBindColumns()}),i.resumePaint(),0))},n.prototype.qFe=function(t,e){var n,i,o,u,n=this;return!!n.Ceo(e)&&(i=n.getDataView(),n.suspendPaint(),o=i.hur(t),u=n.Mf.getColumnCount()>o,i.JGe(t,e,function(){u&&n.dno(o)},function(){n.refreshBindColumns(),u&&n.Seo()}),n.resumePaint(),!0)},n.prototype.dno=function(t,e,n){var i=this;i.Mf.WBe(t,e=void 0===e?1:e,E,{clearUndoNotInCommand:n}),!1!==n&&i.qni.addUndoChanges({type:v.TableSheetUndoType.AddColumns,oldCol:t,oldCount:e})},n.prototype.FPt=function(t){var e;return null==(e=this.getDataView())?void 0:e.FPt(t,!0)},n.prototype.Ceo=function(t,e){var n,i,o,u,r,a,n=this,i;return!(!(!(0,b.isNullOrEmpty)(t.type)||e&&(0,b.isNullOrEmpty)(e.type))||(o="Formula"===t.type&&!p(t.value),u="Formula"!==t.type&&p(t.value),r="Lookup"===t.type&&!n.FPt(t.value),a="Lookup"!==t.type&&!p(t.value)&&(xt(t.value)||n.FPt(t.value)),o)||u||r||a)&&(!e||n.getDataView().Zcr(e,t))},n.prototype.isDisableDefineColumn=function(t){return this.UFe(t)},n.prototype.UFe=function(t){var e,n,e,n;return this.getDataView().UFe(t)},n.prototype.refreshBindColumns=function(t,e){var n,i,o,u,r,e,a,c,s,l,f,d,h,g,b,d,h,g,b,h,d,v,M,I,m,b,g,n=this.Mf,i=e||n.getDataSource();if(i){for(n.suspendPaint(),n.suspendEvent(),(o=n.conditionalFormats)&&o.clearRule(),n.b8&&n.b8.d5(),u=i.getColumnInfos(),r=this.kNi(u),e=this.UNi(),this.QNi(r,u.length,e),this.RNi(t),this.PNi(),this.VNi(r,e,u),a=n.rowFilter(),this.cno(n,a,u),c=[[]],s=n.k9(L.SheetArea.viewport),l=0;l<u.length;l++){if(b=g=h=void 0,"string"==typeof(d=(f=u[l]).value)?p(d)?g=d:h=d:"function"==typeof d&&(b=d.name),d={name:h||g||b},h=f.caption,1===r&&h&&(d.displayName=It(h)),k(f.width)||(d.width=f.width),k(f.visible)||(d.visible=f.visible),n.bindColumn(l,d),this.JNi(-1,l,f.headerStyle,L.SheetArea.colHeader),g=f.style,this.Teo(!0)&&l===s&&(b=f.zno)&&((g=this.Lno(g)).cellType=b),this.JNi(-1,l,g),n.qgt.nM=i.gLt,n.qgt.getField=i.gLt.POt.getField,f.validator&&(h=f.validator instanceof N.DefaultDataValidator?f.validator:Ut(f.validator),n.setDataValidator(-1,l,h)),(d=f.conditionalFormats)&&o)for(v=[new L.Range(-1,l,-1,1)],M=0,I=d;M<I.length;M++)(m=I[M])instanceof A.ConditionRuleBase?(m.ranges([new L.Range(-1,l,-1,1)]),n.conditionalFormats.addRule(m)):Gt(o,m,v);b=this.pNi(l),n.getRange(-1,l,-1,1).locked(b),n.getRange(-1,l,-1,1,L.SheetArea.colHeader).locked(!1),g=this.getFilterButtonVisibleFromColumnInfo(l,u,n),a.filterButtonVisible(l,g),f.isCross?c[0].push(l):0<c[0].length&&c.unshift([])}for(l=0;l<u.length;l++)f=u[l],k(f.headerFit)||this.FNi(l,f.headerFit);this.XNi(),this.tLi(),n.resumeEvent(),n.resumePaint(),this.KNi(c),this.UMi&&this.UMi.updatePanelLayout(),this.applyStyleRules()}},n.prototype.pno=function(){var t,e,n,i,o,u,t=this.Mf,e=t.getDataSource();if(e&&e.tdr){for(t.suspendPaint(),n=0;n<e.tdr.length;n++)(i=e.tdr[n]).K1r&&t.KBe(n,1,L.SheetArea.viewport,!1);for(e.tdr=E,o=e.getColumnInfos(),n=0;n<o.length;n++)(u=o[n]).ume&&t.WBe(n,1,L.SheetArea.viewport,{clearUndoNotInCommand:!1});t.resumePaint()}},n.prototype.cno=function(t,e,n){e.Fct((0,L.Ec)(-1,0,-1,n.length)),t.lct=Z},n.prototype._Ni=function(t){var e,n,i,e,t,e=this;return e.qNi()&&(n=e.getDataView(),T(e=(i=e.Mf.rowFilter()).tAi[t]))&&(t=n.getHierarchyNode(e,!0),!k(t))?t.level:0},n.prototype.$Ni=function(){return this.getDataView().getHierarchyColumnIndex(!0)},n.prototype.isShowHierarchyOutlineColumn=function(t){return this.qNi(t)},n.prototype.qNi=function(t){var e,n=this.getDataView();return!k(n)&&n.hasHierarchyColumn(t)},n.prototype.isHierarchyParent=function(){var t,e=this.getDataView();return e&&e.isHierarchy()&&"Parent"===e.gLt.getHierarchyType()},n.prototype.iDi=function(t,e){var n,i,e,n,n=this,i=n.getDataView();if(!k(i)&&!k(e)&&(e=i.kve(e),!k(e)))return n=n.tDi(e),!k(i.h7t(n,t))},n.prototype.wNi=function(){var t,e,n,i,t=this,e=t.Mf;t.qNi()&&(i=(n=t.getDataView().SPt(!0)).options,t.suspendPaint(),e.outlineColumn.options(f(f({columnIndex:n.index,showIndicator:!0},i),{collapseIndicator:i&&i.collapseIndicator?i.collapseIndicator:R.ARROW_RIGHT,expandIndicator:i&&i.expandIndicator?i.expandIndicator:R.ARROW_DOWN})),e.showRowOutline(!1),e.rowOutlines.KMi=!0,t.resumePaint())},n.prototype.Rno=function(t){var e,n,i,o,u,e=this,n=e.Mf,i,o=e.getDataView().visibleLength();for(e.suspendPaint(),e.options.allowAddNew&&n.getCell(o,t).textIndent(E),u=0;u<o;u++)n.getCell(u,t).textIndent(E);e.resumePaint()},n.prototype.nDi=function(){var t,e,n,i,o,u,r,t=this,e=t.Mf;if(t.qNi()){for(i=(n=t.getDataView()).visibleLength(),o=!1,u=0;u<i;u++)e.outlineColumn.getCollapsed(u)&&(o=(r=n.getHierarchyNode(u)).eDi=!0);return o}},n.prototype.jze=function(){var t,e,n,i,o,u,t=this,e=t.Mf;if(!t.qNi())return{};for(n={},o=(i=t.getDataView()).visibleLength(),u=0;u<o;u++)n[u]=e.rowOutlines.getCollapsed(u);return n},n.prototype.vze=function(t){var e,n,i,o,u,e=this,n=e.Mf;if(e.qNi())for(o=(i=e.getDataView()).visibleLength(),e.expandAllHierarchyLevels(),e.XNi(),u=0;u<o;u++)n.outlineColumn.setCollapsed(u,t[u])},n.prototype.oDi=function(){var t,e,n,i,o,u,t=this,e=t.Mf;if(t.qNi())for(i=(n=t.getDataView()).visibleLength(),o=0;o<i;o++)(u=n.getHierarchyNode(o)).eDi&&(u.eDi=!1,e.outlineColumn.setCollapsed(o,!0))},n.prototype.uDi=function(t){var e=this;e.qNi()&&(e.suspendPaint(),!1!==t&&e.expandAllHierarchyLevels(),e.XNi(),!1!==t&&e.oDi(),e.resumePaint())},n.prototype.XNi=function(){var t,e,n,i,o,u,r,a,c,u,s,t=this,e=t.Mf;if(t.qNi()){for(n=t.getDataView(),i=t.$Ni(),t.suspendPaint(),o=n.visibleLength(),(u=e.outlineColumn.options())&&!k(u.columnIndex)&&t.Rno(u.columnIndex),t.options.allowAddNew&&e.getCell(o,i).textIndent(E),r=0;r<o;r++)a=n.getHierarchyNode(r),c=k(a)?E:a.level,e.getCell(r,i).textIndent(c);if(e.outlineColumn.refresh(),(u=n.SPt())&&u.options&&u.options.showCheckbox){for(r=0;r<o;r++)a=n.getHierarchyNode(r),s=!k(a)&&a.cDi,e.outlineColumn.ect(r,s);e.outlineColumn.refresh()}t.resumePaint()}},n.prototype.rDi=function(t){var e=this;e.suspendPaint(),e.uDi(!!t),e.resumePaint()},n.prototype.aDi=function(t){var e=this;return!(!e.qNi()||!T(t))&&e.Mf.outlineColumn.getCheckStatus(t)},n.prototype.sDi=function(){var t,e,n,i,o,t=this;if(t.qNi())for(e=t.Mf,i=0,o=(n=t.getDataView()).visibleLength();i<o;i++)n.getHierarchyNode(i).cDi=e.outlineColumn.getCheckStatus(i)},n.prototype.getHierarchyMaxLevel=function(){return this.MDi()},n.prototype.MDi=function(){for(var t,e,n,i,o,u,t,e=this.getDataView(),n=e.visibleLength(),i=-1,o=0;o<n;o++)i<(u=e.getHierarchyNode(o).level)&&(i=u);return i},n.prototype.showHierarchyItems=function(t){this.IDi(t,!1)},n.prototype.hideHierarchyItems=function(t){this.IDi(t,!0)},n.prototype.IDi=function(t,e){var n=this,i=n.Mf;n.qNi()&&(i.suspendPaint(),t.forEach(function(t){n.gDi(t)&&i.outlineColumn.setCollapsed(t,e)}),i.outlineColumn.refresh(),i.resumePaint())},n.prototype.NDi=function(t){var e,n,i,o,e=this;if(e.qNi()){for(n=e.Mf,i=e.lDi(),n.suspendPaint(),o=0;o<i;o++)n.outlineColumn.setCollapsed(o,t);n.outlineColumn.refresh(),n.resumePaint()}},n.prototype.expandAllHierarchyLevels=function(){this.NDi(!1)},n.prototype.collapseAllHierarchyLevels=function(){this.NDi(!0)},n.prototype.lDi=function(){var t,e,n;return this.getDataView().visibleLength()},n.prototype.gDi=function(t){return t<this.lDi()},n.prototype.expandHierarchyLevel=function(t){var e,n,i,o,u,r,e=this;if(e.qNi()){for(n=e.Mf,i=e.getDataView(),o=e.lDi(),e.suspendPaint(),u=0;u<o;u++)(r=i.getHierarchyNode(u).level)<t?n.outlineColumn.setCollapsed(u,!1):n.outlineColumn.setCollapsed(u,!0);n.outlineColumn.refresh(),e.resumePaint()}},n.prototype.promoteHierarchyLevel=function(t){var e=this;e.qNi()&&(e.getDataView().promoteHierarchyLevel(t),e.rDi())},n.prototype.demoteHierarchyLevel=function(t,e){var n=this;n.qNi()&&(n.getDataView().demoteHierarchyLevel(t,e),n.rDi())},n.prototype.moveUp=function(t){var e,n,i,e=this,n=e.getDataView();n.hasHierarchyRowOrder()&&(e.qni.addUndoChanges({type:v.TableSheetUndoType.StoreTreeData}),i=e.nDi(),n.moveUp(t),e.rDi(i),e.qni.addUndoChanges({type:v.TableSheetUndoType.ApplyTreeData}))},n.prototype.moveDown=function(t){var e,n,i,e=this,n=e.getDataView();n.hasHierarchyRowOrder()&&(e.qni.addUndoChanges({type:v.TableSheetUndoType.StoreTreeData}),i=e.nDi(),n.moveDown(t),e.rDi(i),e.qni.addUndoChanges({type:v.TableSheetUndoType.ApplyTreeData}))},n.prototype.hasHierarchyRowOrder=function(){return this.qX()},n.prototype.qX=function(){var t,e;return this.getDataView().hasHierarchyRowOrder()},n.prototype.addHierarchyItemBefore=function(t,e){var n,i,o,u,n=this,i=n.Mf,o=n.getDataView();o.hasHierarchyRowOrder()&&(this.qni.addUndoChanges({type:v.TableSheetUndoType.StoreTreeData}),u=n.nDi(),i.ffe(t,1,3,{rowExpand:!1,clearUndoNotInCommand:!1}),o.addHierarchyItemBefore(t,e||{}),n.uDi(u),this.qni.addUndoChanges({type:v.TableSheetUndoType.ApplyTreeData}))},n.prototype.addHierarchyItemAfter=function(t,e){var n,i,o,u,o,n=this,i=n.Mf,o=n.getDataView();o.hasHierarchyRowOrder()&&(this.qni.addUndoChanges({type:v.TableSheetUndoType.StoreTreeData}),u=n.nDi(),o=o.addHierarchyItemAfter(t,e||{}),i.ffe(t+o,1,3,{rowExpand:!1,clearUndoNotInCommand:!1}),n.uDi(u),this.qni.addUndoChanges({type:v.TableSheetUndoType.ApplyTreeData}))},n.prototype.addHierarchyItemAbove=function(t,e){var n,i,o,u,n=this,i=n.Mf,o=n.getDataView();o.hasHierarchyRowOrder()&&(this.qni.addUndoChanges({type:v.TableSheetUndoType.StoreTreeData}),u=n.nDi(),i.ffe(t,1,3,{rowExpand:!1,clearUndoNotInCommand:!1}),o.addHierarchyItemAbove(t,e||{}),n.uDi(u),this.qni.addUndoChanges({type:v.TableSheetUndoType.ApplyTreeData}))},n.prototype.addHierarchyItemBelow=function(t,e){var n,i,o,u,o,n=this,i=n.Mf,o=n.getDataView();o.hasHierarchyRowOrder()&&(this.qni.addUndoChanges({type:v.TableSheetUndoType.StoreTreeData}),u=n.nDi(),o=o.addHierarchyItemBelow(t,e||{}),i.ffe(t+o,1,3,{rowExpand:!1,clearUndoNotInCommand:!1}),n.uDi(u),this.qni.addUndoChanges({type:v.TableSheetUndoType.ApplyTreeData}))},n.prototype.KNi=function(t){var e,n,i,o,u,e=this.Mf,n=this.cNi||[[]];for(e.suspendPaint(),i=0;i<n.length;i++)0<(o=n[i]).length&&e.cellStates.getCollection(L.CellStatesType.readonly)&&(u=new L.Range(-1,o[0],-1,o.length),e.cellStates.remove(u,L.CellStatesType.readonly));for(this.cNi=t,i=0;i<t.length;i++)0<(o=t[i]).length&&e.cellStates.add(new L.Range(-1,o[0],-1,o.length),L.CellStatesType.readonly,new L.Style({backColor:"#e6e6e6"}));e.resumePaint()},n.prototype.applyStyleRules=function(){var t,e,n,i,o,u,r,u,a,r,c,s,u,l,f,t=this.Mf,e,n=t.getDataSource().getStyleRules();if(n)for(o in i=t.conditionalFormats,this.clearStyleRuleConditionalFormats(),n)n[o]&&(r=(u=n[o]).style,u=u.rule,r)&&u&&(a=void 0,a=r instanceof L.Style?r:new L.Style(r),r=u.formula,c=u.state,s=u.direction,u=u.area,l=[new L.Range(-1,-1,-1,-1)],f={},r?Gt(i,f={ruleType:"formulaRule",formula:r,style:a},l):T(c)&&T(s)&&(1!==s&&3!==s||this.addRowStateRule(c,a),2===s&&u===L.SheetArea.colHeader?this.addColumnHeaderStateRule(c,a):2!==s&&3!==s||this.addColumnStateRule(c,a)))},n.prototype.DDi=function(t){var e,n,i,o,u,r,a,c,s,r;if(t&&(n=(e=this.Mf).conditionalFormats))for(o=(i=n.getRules()).length-1;0<=o;o--)r=(u=i[o]).ruleType(),c=(a=u.ranges())[0],s=r===A.RuleType.rowStateRule||r===A.RuleType.columnStateRule,r=r===A.RuleType.formulaRule&&-1===c.col&&-1===c.colCount,(s||r)&&t(u,n)},n.prototype.clearStyleRuleConditionalFormats=function(){this.DDi(function(t,e){e.removeRule(t)})},n.prototype.refreshStyleRuleCache=function(){this.DDi(function(t,e){e.RX(t),e.fE(t)})},n.prototype.fDi=function(t,e){var n,i,o,n,i=this.Mf.getDataSource();return i&&(o=i.kve(t,!0,e))||null},n.prototype.bze=function(t){for(var e,n,i,o,n=this,i=n.endColumn(),o=0;o<i+1;o++)if((null==(e=n.fDi(o))?void 0:e.name)===t)return o;return null},n.prototype.dDi=function(t){return this.tDi(this.fDi(t))},n.prototype.tDi=function(t){var t;if(t)return"string"==typeof(t=t.value)?t:"function"==typeof t?t.name:void 0},n.prototype.Lno=function(t){var e,t,e=new L.Style;return t&&(this.hDi(t.dropDowns),t=t instanceof L.Style?t:new L.Style(t),e.h5(t,!0),this.Deo(e))&&this.jeo(e),e},n.prototype.JNi=function(t,e,n,i){var o,n,o=this.Mf;k(i)||i===L.SheetArea.viewport?i=3:n&&(n.cellType&&"98"!==n.cellType.typeName&&"107"!==n.cellType.typeName&&delete n.cellType,delete n.tabStop,delete n.imeMode,delete n.cellButtons,delete n.dropDowns,delete n.mask),n=this.Lno(n),o.setStyle(t,e,n,i)},n.prototype.Ywt=function(t,e,n){var i,o=this.getDataView();return o&&o.Ywt(t,e,n)},n.prototype.nMt=function(t,e){var n,i=this.getDataView();return i&&i.nMt(t,e)},n.prototype.hDi=function(t){var e,n,i,o,e,n,i,o=this.Mf.parent.dataManager();t&&t.forEach(function(t){var t,e,t,n,t=t.option;t&&t.dataSource&&(e=t.dataSource,(e=t.dataSource instanceof b.View?t.dataSource={datamanagerTable:e.tableName,columns:e.yLt}:e).datamanagerTable)&&(t=o.tables[e.datamanagerTable],e.viewName=e.datamanagerTable+Math.random().toString(16).substring(1,8),(n=t.GRt(e.viewName,e.columns)).fetch())})},n.prototype.enablePinRow=function(){for(var t,e,n,i,i,o,u,t,e,r,a,c,s,l,f,d,f,t=this,e=t.Mf,n=10,i=t.groupBy(),i=i&&Array.isArray(i)&&0<i.length,o=t.qNi(),u,t=t.getDataView().lFt,e=e.getSelections(),r=t&&t.length,a=0,c=0,s=0,l=e;s<l.length;s++)(f=l[s])&&(d=f.row,c+=f=f.rowCount,(d=-1===d?0:d)<r)&&(a+=Math.min(f+d,r)-d);return!(i||o||10<r-a+(c-a))},n.prototype.enablePinColumn=function(){for(var t,e,n,i,i,o,t,o,t,e,u,r,a,c,s,l,f,l,t=this,e=t.Mf,n=10,i=t.groupBy(),i=i&&Array.isArray(i)&&0<i.length,o=e.getActiveColumnIndex(),t=t.getDataView(),o=t&&t.kve(o),t=t.CLt,e=e.getSelections(),u=t&&t.length,r=0,a=0,c=0,s=e;c<s.length;c++)(l=s[c])&&(f=l.col,a+=l=l.colCount,(f=-1===f?0:f)<u)&&(r+=Math.min(l+f,u)-f);return!(i||o&&o.isCross||10<u-r+(a-r))},n.prototype.FNi=function(t,e){var n,i,e,n=this.Mf;switch(e){case"vertical":i=L.ColumnHeaderFitMode.vertical;break;case"stack":i=L.ColumnHeaderFitMode.stack;break;default:i=L.ColumnHeaderFitMode.normal}e=n.defaults.colHeaderRowHeight,n.GB(t,i),e!==n.defaults.colHeaderRowHeight&&this.qni.addUndoChanges({type:v.TableSheetUndoType.DefaultRowHeightChanged,oldData:{size:e,sheetArea:L.SheetArea.colHeader},newData:{size:n.defaults.colHeaderRowHeight,sheetArea:L.SheetArea.colHeader}})},n.prototype.getDataView=function(){return this.Mf.getDataSource()},n.prototype.setDefaultRowHeight=function(t,e){var n,i,n=this.Mf.defaults,i=n.rowHeight;e===L.SheetArea.colHeader?(i=n.colHeaderRowHeight,n.colHeaderRowHeight=t):n.rowHeight=t,i!==t&&this.qni.addUndoChanges({type:v.TableSheetUndoType.DefaultRowHeightChanged,oldData:{size:i,sheetArea:e},newData:{size:t,sheetArea:e}})},n.prototype.getDefaultRowHeight=function(t){var e=this.Mf.defaults;return(t=k(t)?3:t)===L.SheetArea.corner||t===L.SheetArea.colHeader?e.colHeaderRowHeight:e.rowHeight},n.prototype.MNi=function(){var t;this.gNi=[vt]},n.prototype.rowActionOptions=function(t){var e,n,e=this;return I(t)&&(n=y([],this.gNi,!0),e.clearRowActions(),e.gNi=e.DNi(t),e.rebuildRowActionOptions(),e.NNi(),e.bDi(),this.qni.addUndoChanges({type:v.TableSheetUndoType.RowAction,oldData:n,newData:this.gNi})),e.DNi()},n.prototype.rebuildRowActionOptions=function(){var t,e,n,i,o,u,t=this,e,n=Bt(t.gNi,m);Rt(n)&&(i=r[D],o=r[j],u=r[C],i.icons=n.icons.slice(0,1),(i=J(i)).command=n.command,o.icons=n.icons.slice(1,4),u.icons=n.icons.slice(4,5),n.subActionOptions=[t.nLi(i),t.nLi(o),t.nLi(u)])},n.prototype.NNi=function(){var t=this,e=t.gNi;k(t.getParent())||L.Commands.iMi(t.getParent().commandManager(),e)},n.prototype.tLi=function(){var n,i,o,t,e,n=this,i=n.getDataView();if(i)for(n.eLi={},o=i.getColumn(),t=function(t){var e=o[t],t=e.value;if("string"!=typeof t||n.eLi.hasOwnProperty(t))return"continue";Object.defineProperty(n.eLi,t,{get:function(){return i.Nve(n.eLi.oLi,e)}})},e=0;e<o.length;e++)t(e)},n.prototype.nLi=function(t){return t.name===D?t.iLi=Xt:t.name===j?t.iLi=Kt:t.name===C&&(t.iLi=qt),t},n.prototype.bDi=function(){var e,t,n,i,o,u,o,r,a,c,s,l,e=this,t=e.Mf,n=e.gNi,i=n.length,o=e.options.showRowNumber?1:0,u=1;for(t.suspendPaint(),t.setColumnCount(1+i+o,L.SheetArea.rowHeader),o=e.groupOutlinePosition(),t.setColumnVisible(0,o===d.GroupOutlinePosition.rowHeader||o===d.GroupOutlinePosition.groupCellAll,L.SheetArea.rowHeader),e.tLi(),r={isPinnedRow:function(t){return e.ADi(t)},isInsertedRow:function(t){return e.vDi(t)},isUpdatedRow:function(t){return e.jDi(t)},isNewRow:function(t){return e.zDi(t)},hasWarning:function(t){return e.CDi(t)},getDataItem:function(t){return e.eLi.oLi=t,e.eLi}},a=0;a<i;a++)s=1+a,S((c=n[a]).tooltip)&&e.wDi(-1,s,c.tooltip),e.nLi(c),(l=new B.RowAction(c,r)).build(e.yDi(c)),t.setCellType(-1,s,l,L.SheetArea.rowHeader);t.resumePaint()},n.prototype.clearRowActions=function(){var t,e,n,i,o,u,r,a,t=this,e=t.Mf,n=t.gNi,i=n.length,o=1;for(e.suspendPaint(),u=0;u<i;u++)a=1+u,(r=n[u]).tooltip&&t.wDi(-1,a,null),e.setStyle(-1,a,null,L.SheetArea.rowHeader);e.resumePaint()},n.prototype.yDi=function(t){var e=this,n=t.name===j||t.name===m,t=t.name===w||t.name===q,i=e.mDi.bind(e),e=e.LDi.bind(e);return n?{enabled:i}:t?{enabled:e,visible:i}:{enabled:i,visible:i}},n.prototype.tIe=function(t){var e;return!this.jFt||this.VDi(t)},n.prototype.Zze=function(t){var e,n;return t<this.Mf.frozenColumnCount()},n.prototype.ADi=function(t){var e,n;return t<this.Mf.frozenRowCount()},n.prototype.xDi=function(t){for(var e,n,i,o,e,n=this.gNi,i=1,o=0;o<n.length&&n[o].name!==t;o++);return o===n.length?-1:1+o},n.prototype.SDi=function(){var t=this,e=t.xDi(C);return e=-1===e?t.xDi(m):e},n.prototype.zA=function(t){return t.sheetName=this.name(),this.getParent().commandManager().execute(t)},n.prototype.CDi=function(t,e){var n,i,n=this;if(T(t))return(i=n.SDi())===(e=T(e)?e:i)&&!k(n.ZDi(t))},n.prototype.ZDi=function(t){var e,n=this.getDataView();return n&&n.getErrorInfo(t)},n.prototype.ODi=function(t){var e,t=this.ZDi(t);if(!k(t))return t.reason},n.prototype.wDi=function(t,e,n,i,o){void 0===o&&(o=L.SheetArea.rowHeader);var u,r=this.Mf;i&&r.getCell(t,e,o).tag(n),r.Ut.$n(t,e,n,o)},n.prototype.Deo=function(t){return t&&t.cellType&&"8"===t.cellType.typeName},n.prototype.jeo=function(t){var t=t.cellType;t.Bto=!0,t.AQe=!0,t._Qe=function(t,e,n){var i,t,t,i=this,t=t.NMi.getDataView(),t=t&&t.BQe(e,n);return t?{id:i._id,visited:t.visited}:Z},t.DQe=function(t,e,n){var t=t.NMi.getDataView();t&&t.GQe(e,n,{visited:!0})}},n.prototype.Aeo=function(t){var e,n,i,e=this,n=e.getDataView();if(n&&!(S(t)&&(t=e.bze(t),k(t))||(i=n.kve(t,!0,!0),k(i))))return(i=f({},i)).caption=It(i.caption),p(i.value)&&(i.formula=i.value,"formula"!==i.dataType||(0,b.isNullOrEmpty)(i.name)||p(i.name)?i.value=Z:i.value=i.name),i},n.prototype.Eze=function(t,e,i){var a,n,o,u,a=this,n=a.getParent();n.iy(),a.wze&&a.wze.close(),o=T(i)&&a.getDataView()?a.Aeo(i):{},t-=(u=n.uC()).left,e-=u.top,a.wze=new W.Uze(n.j2(),L.util.gi(n&&n.Ti),{columnTypeItems:a.options.columnTypeItems,column:o,getSheet:function(){return a.Mf},getLookup:function(t){var e;return(null==(e=a.getDataView())?void 0:e.jGe(t))||[]},getSampleValue:function(n){var i,o,t,e,t,u,i=a.getDataView();if(i){if(p(t=n.formula||n.value))return a.aji(t);if(t=function(){for(var t=0,e=i.visibleLength();t<e&&(o=i.ufe(n,t),k(o));t++);},(e=xt(n.value))&&(t(),!k(o)))return o;if("number"===(u=n.dataType||i.OGe(n)))return 0;if("date"===u)return new Date;e||t()}return o},getColumnBy:function(t){return a.Aeo(t)},getColumnFields:function(){var t,e,n,i,o,u,r,t=a.getDataView(),e=[];if(t)for(o=n=t.getColumnInfos(!(i=0),!0);i<o.length;i++)(r=(u=o[i]).value)&&"string"==typeof r&&-1===r.indexOf("=")&&!u.cross&&e.push(r);return e},getTriggerFields:function(){var t,e,n,i,o,u,r,t=a.getDataView(),e=[];if(t)for(o=n=t.getColumnInfos(!(i=0),!0);i<o.length;i++)!(r=(u=o[i]).value)||"string"!=typeof r||-1!==r.indexOf("=")||-1!==r.indexOf(".")||u.cross||u.isRelationship||e.push(r);return e},isRelationship:function(t){var e=a.getDataView();return e&&e.FPt(t,!0)},onOpening:function(){n.focus(!1)},onOpened:function(){a.getSheet().rt.u2()},onSaving:function(e){var t,n;e.column&&(e.column.caption=mt(e.column.caption)),e.originalColumn&&(e.originalColumn.caption=mt(e.originalColumn.caption)),e.col=i,a.options.submitDefineColumnCommand===dt?!1!==(t=a.zA(e))&&a.wze.close():(e.command=e.cmd,e.cmd=a.options.submitDefineColumnCommand,t=a.zA(e),"function"==typeof e.onCompleted?(n=e.onCompleted,delete e.onCompleted,n(function(t){t&&t.success&&(e.cmd=e.command,a.zA(e)),a.wze&&a.wze.close()})):!1!==t&&a.wze.close())},onClosed:function(){a.wze=Z,n.focus()}}),a.wze.rQ(t,e)},n.prototype.getChanges=function(){var t,e,t,e=this.Mf.getDataSource();if(e)return e.getChanges()},n.prototype.submitChanges=function(){var i,t,n,i=this,t,n=this.Mf.getDataSource();return n?(i.beforeSubmitChanges(),new Promise(function(e){n.submitChanges(function(t,e,n){i.ONi(t,e,n)}).then(function(t){i.ENi(t),e()})})):Promise.resolve()},n.prototype.beforeSubmitChanges=function(){},n.prototype.ONi=function(t,e,n){var i,o,i,o=this.Mf;"delete"===n&&o.ffe(t,1,3,{rowExpand:!1,clearUndoNotInCommand:!1})},n.prototype.ENi=function(t){var e,n,i,e,e=this,n=e.Mf,i,e=n.getDataSource().visibleLength()+(e.options.allowAddNew?1:0);n.setRowCount(e),n.repaint()},n.prototype.cancelChanges=function(){var t,e,n,i,o,t,e=this.Mf.getDataSource();e&&(i=(n=e.isDataLengthChanged())||e.isDataRowOrderChanged(),o=(this.getChanges()||[]).some(function(t){return"addColumn"===t.type||"removeColumn"===t.type}),this.beforeCancelChanges(),e.cancelChanges(),this.GNi(e,n,i,o))},n.prototype.beforeCancelChanges=function(){},n.prototype.GNi=function(t,e,n,i){var o,u,r,a,o=this.Mf;this.suspendPaint(),r=(u=o.getRowCount())-1,a=o.getColumnCount(),o.Ut.clear(0,0,r,a,L.StorageType.data,[]),o.clearPendingChanges({row:0,col:0,rowCount:r,colCount:a,clearType:L.ClearPendingChangeType.dirty|L.ClearPendingChangeType.insert|L.ClearPendingChangeType.delete}),e?(this.uLi(t),this.EDi()):n&&this.EDi(),i?this.refreshBindColumns():this.qNi()&&this.rDi(),this.Teo()&&this.WDi(),this.resumePaint()},n.prototype.EDi=function(){var t,e,n,i,o,u,t,e;if(!this.getDataView().autoFilter){for(i=(n=this.Mf).rowFilter().Mct.slice(),n.suspendPaint(),o=0;o<i.length;o++)u=i[o],n.Qy().execute({cmd:"clearFilter",sheetName:n.name(),cmdOption:{colIndex:u}});n.resumePaint()}},n.prototype.yy=function(t,e){var n,i=this.Mf;i.getParent().ib.yy(i,t||0,e||0),i.tw()},n.prototype.GDi=function(t){var e=this,n=e.Mf,i=e.uNi;t<i&&i===n.getViewportTopRow(1)&&e.yy(t)},n.prototype.wy=function(t,e){var n,i=this.Mf;i.getParent().ib.wy(i,t||0,e||0),i.ow()},n.prototype.pDi=function(t){var e=this,n=e.Mf,i=e.jIi;t<i&&i===n.getViewportLeftColumn(1)&&e.wy(t)},n.prototype.togglePinnedRows=function(t){var e,n,n,e,n=this.Mf.getDataSource();if(n&&!this.jFt&&!this.qNi())return this.qni.startAddComboChanges(),n=n.togglePinnedRows(t),this.qni.addUndoChanges({type:v.TableSheetUndoType.PinnedRowsChanged}),this.tze(),this.qni.endAddComboChanges(),n},n.prototype.tze=function(){var t,e,t=this.Mf,e=this.Mf.getDataSource().togglePinnedRows();if(-1===e)throw(0,l.getSR)().EXP_TooManyPinRecords;e!==this.uNi&&(this.suspendPaint(),t.frozenRowCount(e),this.GDi(e),this.resumePaint(),this.uNi=e)},n.prototype.togglePinnedColumns=function(t){var e,n,i,i,i,o,n,t,i,e=this.Mf,n=e.getDataSource();if(n&&!this.jFt)return i=this.kDi(),0<=(i=t.indexOf(i))&&t.splice(i,1),i=this.UDi(),o=e.rowFilter().BDi(),this.qni.startAddComboChanges(),n=n.togglePinnedColumns(t),this.nze(i,o,n),t=this.UDi(),i=e.rowFilter().BDi(),this.qni.addUndoChanges({type:v.TableSheetUndoType.PinnedColumnsChanged,oldData:{oldFilterColumnsMap:M(t),oldSortColumnsMap:M(i)}}),this.qni.endAddComboChanges(),n},n.prototype.nze=function(t,e,n){var n,i,n=null!=n?n:this.Mf.getDataSource().togglePinnedColumns(),i=this.Mf;if(-1===n)throw(0,l.getSR)().EXP_TooManyPinRecords;this.QDi(t),i.rowFilter().RDi(e),this.suspendPaint(),this.refreshBindColumns(),i.frozenColumnCount(n),this.kze(),this.pDi(n),this.resumePaint(),this.jIi=n},n.prototype.UDi=function(){var t,e,n,t,t,i,o,u,r,a,t=this.Mf,e,n=t.getDataSource().oPt,t=t.rowFilter(),t=t&&t.Mct,i={};if(t&&t.length)for(o=-1,u=0,r=t;u<r.length;u++)a=r[u],i[o=n&&n.length?n[a]:a]=a;return i},n.prototype.QDi=function(t){var e,n,i,e,o,u,r,a,c,s,c,l,f,e=this.Mf,n,i=e.getDataSource().oPt,e=e.rowFilter(),o=e&&e.Mct,u=e&&e.rrt,r={};for(a in t)t.hasOwnProperty(a)&&(s=t[c=+a],(c=i&&0<i.length?i.indexOf(c):c)!==s)&&(o[o.indexOf(s)]=c,l=u[s],delete u[s],r[c]=l);for(f in r)r.hasOwnProperty(f)&&(u[f]=r[f])},n.prototype.PDi=function(){var t=this.uNi,e=this.jIi;this.suspendPaint(),0<t&&this.togglePinnedRows(Pt(t)),0<e&&this.togglePinnedColumns(Pt(e)),this.resumePaint()},n.prototype.nPt=function(){var t,e,n;this.Mf.getDataSource().nPt()},n.prototype.nDe=function(){var t,e,n,i,o,u,r,t=this,e=t.Mf,n=t.getDataView(),i=e.H3,o=[];if(o=i.J$?Object.keys(i.J$).reduce(function(t,e){var e,e,e,e=null!=(e=i.J$[e])?e:{},e=e.name||e.dataField;return e&&t.push(e),t},[]):Object.keys(i.Pq).map(function(t){return i.Pq[t]}),u=n.getColumnInfos().map(function(t){return n.ife(t)}),o.length!==u.length)t.eDe();else for(r=0;r<o.length;r++)if(o[r]!==u[r])return void t.eDe()},n.prototype.WDi=function(t,e,n){var i=this.Mf,o=this.jFt,u=this.lMi,r=i.getDataSource();o&&r.lde(t)&&(this.suspendPaint(),!1!==e&&this.nDe(),this.Mf.icr&&this.Mf.icr(),this.INi.Yeo(this.options.groupLayout.mode),this.INi.Jeo(this.options.groupLayout),this.INi.Hc(r.FFt,i.getRowCount()-1-(this.options.allowAddNew?1:0),i,u,n),this.resumePaint())},n.prototype.groupBy=function(t){var e,n,i,t,e=this.Mf,n=e.getDataSource();if(!n||0===arguments.length||!I(t)||this.qNi())return n&&n.groupBy();this.suspendPaint(),e.suspendCalcService(),this.PDi(),this.jFt=!0,i=M(this.lMi),this.lMi=M(t),n.Agr(!this.Teo()),n.groupBy(this.lMi),this.WDi(E,!1),this.eDe(),(t=e.rowFilter())&&t.Gze(),e.resumeCalcService(!1),this.resumePaint(),this.UMi&&this.UMi.updatePanelLayout(),this.qni.addUndoChanges({type:v.TableSheetUndoType.GroupOptions,oldData:i,newData:this.lMi})},n.prototype.eDe=function(t,e){var n,i,o,u,o;void 0===t&&(t=d.GroupLayoutMode.tabular),u=(o=(i=(n=this.Mf).getDataSource()).getColumnInfos()).length,o=o.filter(function(t){return t.isGroup}).length,this.pno(),n.setColumnCount(u),this.refreshBindColumns(e),0<o?(n.Cb.clear(L.SheetArea.viewport),t===d.GroupLayoutMode.tabular&&n.autoMerge((0,L.Ec)(-1,0,-1,o),h.AutoMergeDirection.column,h.AutoMergeMode.restricted)):this.Teo()&&n.Cb.clear(L.SheetArea.viewport)},n.prototype.removeGroupBy=function(){var t,e,n,e,e,t=this.Mf,e=t.getDataSource();e&&!this.qNi()&&(n=M(this.lMi),this.jFt=!1,this.lMi=E,this.suspendPaint(),t.suspendCalcService(),this.detailColumnsVisible(!0),e.removeGroupBy(),e=e.getColumnInfos().length,t.setColumnCount(e),this.refreshBindColumns(),t.Cb.clear(L.SheetArea.viewport),this.Mf.icr(),this.INi.L0(t),(e=t.rowFilter())&&e.Gze(),t.resumeCalcService(!1),this.resumePaint(),this.UMi&&this.UMi.updatePanelLayout(),this.qni.addUndoChanges({type:v.TableSheetUndoType.GroupOptions,oldData:n}))},n.prototype.detailColumnsVisible=function(t){var e,n,i,o,u,r,e=this.Mf,n=e.getDataSource();if(n){if(i=n.detailColumnsVisible(),0===arguments.length)return i;if((t===i||t||!this.Teo(!0))&&(n.detailColumnsVisible(t),n.BFt)){for(this.suspendPaint(),o=n.getColumnInfos(),u=0;u<o.length;u++)(r=o[u]).isGroup||e.setColumnVisible(u,r.visible);this.HDi(),this.resumePaint()}}},n.prototype.HDi=function(){var t=this.lMi,e=this.detailColumnsVisible();t&&!e&&this.expandGroup(t[t.length-1].field,!1)},n.prototype.groupOutlinePosition=function(t){var e,n;if(0===arguments.length)return this.INi.TMi();e=this.INi.TMi(),n=this.Mf,this.suspendPaint(),this.INi.TMi(n,t),this.resumePaint(),this.qni.addUndoChanges({type:v.TableSheetUndoType.GroupOutlinePosition,oldData:e,newData:t})},n.prototype.Beo=function(t,e){var n,i,o;this.jFt&&(i=(n=this.options.groupLayout).mode,o=this.getDataView())&&(this.suspendPaint(),t&&(i===d.GroupLayoutMode.tabular?(o.Agr(!0),this.hno()):(0,O.isGroupOutlineCondensedLayout)(i)&&(this.gno(),o.Agr(!1),this.detailColumnsVisible()||this.detailColumnsVisible(!0))),this.WDi(E,!1,e),this.eDe(i,t),this.resumePaint())},n.prototype.bno=function(){var t,e,n,i,t=this.getDataView(),e=0;if(t)for(n=t.getColumn();e<n.length&&(i=n[e]).isGroup;e++);return e},n.prototype.gno=function(){var t=this;t.fno(0,t.bno(),!1)},n.prototype.hno=function(){var t=this;t.dno(0,t.bno(),!1)},n.prototype.Seo=function(){this.Teo(!0)&&(this.WDi(),this.qni.addUndoChanges({type:v.TableSheetUndoType.ResetGroupOutline}))},n.prototype._to=function(){return this.options.groupLayout},n.prototype.Teo=function(t){return(!t||this.jFt)&&(0,O.isGroupOutlineCondensedLayout)(this.keo())},n.prototype.$eo=function(){return(0,O.isGroupCondensedLayout)(this.keo())},n.prototype.keo=function(){var t=this._to();return t&&Ct(t.mode)},n.prototype.Jto=function(){var t=this._to();return t&&wt(t.position)},n.prototype.Heo=function(){return"footer"!==this.Jto()},n.prototype.expandGroup=function(t,e){var n,i,o,n=this.lMi;n&&!(i=e&&t===n[n.length-1].field&&!this.detailColumnsVisible())&&(this.suspendPaint(),o={expand:!e,indexs:[]},this.INi.uMi(n,t,e,function(t,e){o.level=t,o.indexs.push(e)}),this.qni.addUndoChanges({type:v.TableSheetUndoType.ExpandGroupByLevel,oldData:o,newData:{field:t,expand:e}}),this.resumePaint())},n.prototype.expandGroupItem=function(e,n,i){var o,t,u,r,o=this,t=this.lMi;t&&!(u=i&&e===t.length-1&&!this.detailColumnsVisible())&&(this.suspendPaint(),r={index:n,level:e},this.INi.aMi(e,n,i,function(t){r.expand=t,o.qni.addUndoChanges({type:v.TableSheetUndoType.ExpandGroupItem,oldData:r,newData:{index:n,level:e,expand:i}})}),this.resumePaint())},n.prototype.VDi=function(t){return this.sMi(t,X,0)},n.prototype.DMi=function(t,e){return!(-1<t)||this.sMi(e,t,0)},n.prototype.sMi=function(t,e,n,i,o){return this.lno||this.INi.sMi(t,e,n,i,o)},n.prototype.nIe=function(t,e,n){var i,n,n,i=this;return!(!i.jFt||n!==L.SheetArea.viewport||!(n=i.Mf.getDataSource())||((n=n.kve(e))&&n.isGroup?i.DMi(n.OFt,t):i.VDi(t)))},n.prototype.MMi=function(t){return this.INi.MMi(t)},n.prototype.IMi=function(t,e){return this.INi.IMi(t,e)},n.prototype.gMi=function(t){return this.INi.gMi(t)},n.prototype.FDi=function(t){var e,e,e=this.Mf.getDataSource();return!!e&&(e=e.kve(t))&&e.isGroup},n.prototype.hMi=function(t){var e,e,e=this.Mf.getDataSource();return!!e&&(e=e.kve(t))&&e.DFt},n.prototype.JDi=function(t){var e,e,e=this.Mf.getDataSource();return!!e&&(e=e.kve(t))&&e.GFt},n.prototype.applyTableTheme=function(t){var e,n,i,e=this.Mf,n=this.hl;this.hl=t,i=M(this.options.alternatingRowOptions),this.suspendPaint(),this.qeo(e,i,t),this.options.alternatingRowOptions=i,this.resumePaint(),this.qni.addUndoChanges({type:v.TableSheetUndoType.ThemeChanged,oldData:n,newData:this.hl})},n.prototype.qeo=function(t,e,n){var i,o,u,u,u,i,t,o,i=new L.Style,o=new L.Style,u=n&&n.headerRowStyle();u&&o.h5(u),(u=n&&n.wholeTableStyle())&&(i.h5(u),o.h5(u)),i.cellPadding===E&&(i.cellPadding="5"),t.setDefaultStyle(i),o.showEllipsis===E&&(o.showEllipsis=!0),o.cellPadding===E&&(o.cellPadding="5"),t.setDefaultStyle(o,L.SheetArea.colHeader),(e=e||M(this.options.alternatingRowOptions))&&(u=n&&n.firstRowStripStyle(),Array.isArray(e.style)||(e.style=[]),e.style[0]=u?this.XDi(u):E,i=n&&n.firstRowStripSize(),Array.isArray(e.step)||(e.step=[]),e.step[0]=k(i)?1:i,(t=n&&n.secondRowStripStyle())&&(Array.isArray(e.style)||(e.style=[]),e.style[1]=this.XDi(t)),o=n&&n.secondRowStripSize(),k(o)||(Array.isArray(e.step)||(e.step=[]),e.step[1]=o))},n.prototype.XDi=function(t){var e=new L.Style;return e.backColor=t.backColor,e.foreColor=t.foreColor,e.font=t.font,e.borderLeft=t.borderLeft,e.borderTop=t.borderTop,e.borderRight=t.borderRight,e.borderBottom=t.borderBottom,e.textDecoration=t.textDecoration,e},n.prototype.applyFreeHeaderArea=function(t){if(0===arguments.length)return this.hNi;this.qni.addUndoChanges({type:v.TableSheetUndoType.ApplyFreeHeaderChanged,oldData:this.hNi,newData:t}),this.hNi=t,this.refreshBindColumns()},n.prototype.printInfo=function(t){var e,t,e=this.Mf;return t&&(t.columnStart(-1),t.columnEnd(-1),t.rowStart(-1),t.rowEnd(-1),t.repeatColumnStart(-1),t.repeatColumnEnd(-1),t.repeatRowStart(-1),t.repeatRowEnd(-1)),(t=e.printInfo&&e.printInfo.apply(e,arguments))&&Yt(t),t},n.prototype.addRow=function(n){var i,o,u,r,i=this,o=this.Mf,u=o.getRowCount();return i.options.allowAddNew&&(u-=1),r=this.getDataView(),new Promise(function(t,e){r.insertItem(u,n,function(){r.saveItem(u,function(){i.mNi(u),t()},x(o,u,e))},x(o,u,e))})},n.prototype.iM=function(t,e){var n=this;n.suspendPaint(),n.WDi(e),n.XNi(),n.resumePaint()},n.prototype.iDe=function(t){var e,n;this.Mf.gL(t,-1)},n.prototype.removeRow=function(n){var i,o,t,i=this,o=this.Mf;return i.zDi(n)?Promise.resolve():(t=this.jze(),this.qni.addUndoChanges({type:v.TableSheetUndoType.ChangeCollapse,oldData:t}),this.qni.addUndoChanges({type:v.TableSheetUndoType.RemoveRow}),new Promise(function(e,t){i.getDataView().removeRowData(n,1,function(t){i.ZNi(n,t.removedCount),i.qni.addUndoChanges({type:v.TableSheetUndoType.PinnedRowsChanged}),e()},x(o,n,t))}))},n.prototype.eze=function(){var t,e,n,i,t=this,e=t.Mf.getRowCount(),n=t.getDataView().length(),i=this.options.allowAddNew;t.suspendPaint(),t.Mf.ffe(e,n-e+(i?1:0),3,{rowExpand:!1,clearUndoNotInCommand:!1}),t.WDi(),t.uDi(),t.resumePaint()},n.prototype.Cze=function(t,e){var n=this;n.Mf.WBe(t,e||1,L.SheetArea.viewport,{clearUndoNotInCommand:!1}),n.kze(t)},n.prototype.beo=function(t,e){var n=this;n.Mf.KBe(t,e||1,L.SheetArea.viewport,!1),n.kze(t)},n.prototype.kze=function(t){var e,n,e=this;e.isShowHierarchyOutlineColumn()&&(n=e.Mf.outlineColumn.options().columnIndex,k(t)?e.$Ni()!==n:t<n)&&e.wNi()},n.prototype.KDi=function(t,e){var n,e,n=this.Mf;n.QBe(t,e||1,3,!1),t<(e=n.frozenRowCount())&&n.frozenRowCount(e-1)},n.prototype.ZNi=function(t,e){var n=this;n.suspendPaint(),n.KDi(t,e),n.WDi(),n.XNi(),n.resumePaint()},n.prototype.saveRow=function(i){var o=this;return o.zDi(i)?Promise.resolve():new Promise(function(t,e){var n=o.Mf;o.getDataView().saveItem(i,function(){o.SNi(i),t()},x(n,i,e))})},n.prototype.SNi=function(t){var e=this;e.suspendPaint(),e.resumePaint()},n.prototype.resetRow=function(t){var e=this;e.zDi(t)||e.getDataView().resetItem(t,function(){e.xNi(t)})},n.prototype.xNi=function(t){var e=this;e.iDe(t),e.iM(t)},n.prototype.addRowStateRule=function(t,e){var n,i,o,u;t!==L.RowColumnStates.invalid&&4!==t&&t!==L.RowColumnStates.edit&&(o=(i=(n=this).Mf).conditionalFormats)&&(u=[new L.Range(-1,-1,-1,-1)],o.addRowStateRule(t,e,u))},n.prototype.addColumnStateRule=function(t,e){var n,i,o,u;t!==L.RowColumnStates.invalid&&t!==L.RowColumnStates.dirty&&t!==L.RowColumnStates.edit&&(o=(i=(n=this).Mf).conditionalFormats)&&(u=[new L.Range(-1,-1,-1,-1)],o.addColumnStateRule(t,e,u))},n.prototype.addColumnHeaderStateRule=function(t,e){var n,i,o,u,t;if((4===t||1024===t||2048===t||4096===t)&&(o=(i=(n=this).Mf).conditionalFormats)){switch(u=function(t){e.decoration&&e.decoration.icons&&1===e.decoration.icons.length&&""===e.decoration.icons[0].src&&(e.decoration.icons[0].src=t)},t){case 4:u(rt);break;case 1024:u(ot);break;case 2048:u(ut);break;case 4096:u(at)}return t=new c.ColumnHeaderStateRule(A.RuleType.columnStateRule,t,e),o.addRule(t)}},n.prototype.iAi=function(t){var t=t.conditionalFormats;t&&t.rules&&(t.rules=t.rules.filter(function(t){var e=[4,1024,2048,4096];return!(15===t.ruleType&&-1<e.indexOf(t.state))}))},n.prototype.removeRowStateRule=function(t){t!==L.RowColumnStates.invalid&&4!==t&&t!==L.RowColumnStates.edit&&this._Di(t,!0)},n.prototype.removeColumnStateRule=function(t){t!==L.RowColumnStates.invalid&&t!==L.RowColumnStates.dirty&&t!==L.RowColumnStates.edit&&this._Di(t,!1)},n.prototype._Di=function(t,e){var n,i,o,u,r,a,c,n,i,o=this.Mf.conditionalFormats;if(o)for(r=0,a=u=o.getRules();r<a.length;r++)!(c=a[r]).isStateRule()||c.isRow()!==e||t&&t!==c.state()||o.removeRule(c)},n.prototype.getStateRules=function(){var t,e,t=this.Mf.conditionalFormats;return t?(e=t.getRules()).filter(function(t){var t=t.ruleType();return t===A.RuleType.rowStateRule||t===A.RuleType.columnStateRule}):[]},n.prototype.clearStateRules=function(){var t,e,n,i,o,t=this.Mf.conditionalFormats;if(t)for(n=(e=t.getRules()).length-1;0<=n;n--)(o=(i=e[n]).ruleType())!==A.RuleType.rowStateRule&&o!==A.RuleType.columnStateRule||t.removeRule(i)},n.prototype.qDi=function(){return this.Mf._1},n.prototype.hasRowState=function(t,e){var e=this.qDi().getState(e,0,t,!0,[{row:-1,rowCount:-1,col:-1,colCount:-1}]);return T(e)&&e===t},n.prototype.$Di=function(e,n,t,i){var o,u,r,a,o=this,u=this.getDataView(),r=this.Mf,a={};a[n]=t,u.updateItem(e,a,function(t){i&&i.hasAddNewRowData&&!o.jFt||o.LNi(e,n)},x(r,e),i)},n.prototype.LNi=function(t,e){var n,i,n=this,i=n.Mf;i.Mno()?i.jno(e):n.iM(t,e)},n.prototype.iTi=function(u,r,a){var c,s,t,c=this.getDataView();c&&(s=this.Mf,(t=c.getColumnInfos()).forEach(function(t,e){var n,i,o,n,n=t.defaultValue,i=a&&a[c.ife(t)];k(i)&&!k(n)&&(i=yt(n)?n():n,n)&&"="===n[0]&&((o=s.Uw()).DJ=c.length(),n=s.Lp.parse(o,n,0,0),i=s.Lp.evaluateExpression(o,n,{row:u,col:e})),r(i,e,t)}))},n.prototype.aji=function(t){var e,n,i,e,n=this.getDataView();return i=n?n.nMt(n.Ywt(t),0):i},n.prototype.TLt=function(t,e,n){return t.TLt(e,n)},n.prototype.EPt=function(){var t=this,e=t.jIi,n=t.getDataView();0<e&&(t.tTi=n.EPt(),t.togglePinnedColumns(Pt(e)))},n.prototype.jPt=function(){var t,e,n,e,t=this,e=t.tTi,n,e=t.getDataView().jPt(e);0<e.length&&t.togglePinnedColumns(e),delete t.tTi},n.prototype.DPt=function(t,e){var n=this.getDataView();if(n)return n.DPt(t,e)},n.prototype.OPt=function(t,e,n){var i,t,i=this.getDataView();i&&(this.qni.startAddComboChanges(),t=i.OPt(t,e,n),this.Mf.rowFilter().nTi(t),this.kTi(i),this.qni.addUndoChanges({type:v.TableSheetUndoType.SetDataView}),this.Teo(!0)&&this.qni.addUndoChanges({type:v.TableSheetUndoType.ResetGroupOutline}),this.qni.endAddComboChanges())},n.prototype.eTi=function(t,e){var n,i={},o;return this.iTi(t,function(t,e,n){yt(o=n.value instanceof Function?n.value.name:n.value)||p(o)||xt(o)||(i[o]=t)},e),i},n.prototype.oTi=function(t,e){var n,i,o,n,n=this,i=this.getDataView(),o=this.Mf,n=n.eTi(t,e);i.insertItem(t,n,function(){},x(o,t))},n.prototype.mNi=function(t){var e,n,e=this,n=e.Mf.getRowCount();e.Mf.ffe(n<1?t:t-1,1,3,{rowExpand:!1,clearUndoNotInCommand:!1}),e.refreshStyleRuleCache(),e.iM(t)},n.prototype.vDi=function(t){var e;return 0<this.qDi().getState(t,-1,L.RowColumnStates.inserted)},n.prototype.jDi=function(t){var e;return 0<this.qDi().getState(t,-1,256)},n.prototype.zNi=function(){var t,e,n,t=this,e=t.Mf;e.suspendPaint(),n=e.getColumnCount(),e.WBe(n,1,3,{clearUndoNotInCommand:!1}),t.uTi(!0,""),e.rowFilter().filterButtonVisible(n,!1),e.resumePaint()},n.prototype.cTi=function(){var t,e,n,t=this,e=t.Mf;e.suspendPaint(),n=t.kDi(),e.getValue(0,n,L.SheetArea.colHeader)===t.actionColumn.options().title&&e.KBe(n,1,3,!1),e.resumePaint()},n.prototype.uTi=function(t,e){var o,n,i,t,e,u,r,a,r,a,r,a,o=this,n=o.Mf;n.suspendPaint(),i=o.kDi(),!t&&n.getValue(0,i,L.SheetArea.colHeader)!==e||(t=new L.Style,e=[],(a=(r=(u=o.actionColumn.options()).actionButtons.remove)&&r.visible)&&e.push({useButtonStyle:!0,caption:r.title,imageType:r.icon,enabled:o.mDi.bind(o),visible:"auto"!==a||o.mDi.bind(o),position:L.ButtonPosition.left,command:function(t,e,n,i){o.Mf.ki(L.Events.RowOperation,{sheet:o,sheetName:o.name(),actionType:Mt.remove,row:e}),o.removeRow(e).catch(bt)}}),(a=(r=u.actionButtons.save)&&r.visible)&&e.push({useButtonStyle:!0,caption:r.title,imageType:r.icon,enabled:o.LDi.bind(o),visible:"auto"!==a||o.LDi.bind(o),position:L.ButtonPosition.left,command:function(t,e,n,i){o.Mf.ki(L.Events.RowOperation,{sheet:o,sheetName:o.name(),actionType:Mt.save,row:e}),o.saveRow(e).catch(bt)}}),(a=(r=u.actionButtons.reset)&&r.visible)&&e.push({useButtonStyle:!0,caption:r.title,imageType:r.icon,enabled:o.LDi.bind(o),visible:"auto"!==a||o.LDi.bind(o),position:L.ButtonPosition.left,command:function(t,e,n,i){o.Mf.ki(L.Events.RowOperation,{sheet:o,sheetName:o.name(),actionType:Mt.reset,row:e}),o.resetRow(e)}}),0<e.length&&(t.cellButtons=e),t.locked=!1,n.setStyle(-1,i,t),o.JNi(-1,i,u.headerStyle,L.SheetArea.colHeader),n.setValue(0,i,u.title,L.SheetArea.colHeader),n.setColumnWidth(i,u.width)),n.resumePaint()},n.prototype.mDi=function(t){return!this.zDi(t)},n.prototype.LDi=function(t){var e,n,i,e,e=this,n=e.jDi(t),i=e.vDi(t),e=e.zDi(t);return n||i&&!e},n.prototype.CNi=function(){var t,e,t=this.Mf,e=t.getRowCount();t.ffe(e=e<2?e:e-2,1,3,{rowExpand:!1,clearUndoNotInCommand:!1})},n.prototype.rTi=function(){var t=this.Mf;t.QBe(t.getRowCount()-1,1,3,!1)},n.prototype.aNi=function(){var t,t,t=this.Mf;t.suspendDirty(),t.options.allowCellOverflow=!1,t.options.rowHeaderAutoText=L.HeaderAutoText.blank,t.options.clipBoardOptions=L.ClipboardPasteOptions.values,t.options.isProtected=!0,(t=t.options.protectionOptions).allowSort=!0,t.allowFilter=!0,t.allowResizeColumns=!0,t.allowDragInsertColumns=!0,t.allowOutlineRows=!0},n.prototype.lNi=function(t){var e,n,i,o,e=this,n=e.Mf;if(e.options.allowAddNew&&t++,t!==n.getRowCount())for(i in n.Ut.cj(t),n.Ww=!0,this.X$)this.X$[i]&&(o=this.X$[i]).xts()},n.prototype.aTi=function(t,e){var n,i,i,o,n=this,i=n.getDataView(),i=i&&i.iRt&&i.iRt.length,o=n.Mf.getRowCount();n.zDi(t)&&o!==i+1&&(n.Mf.ki(L.Events.RowOperation,{sheet:n,sheetName:n.name(),actionType:Mt.addNewEmptyRow,row:t+1}),n.CNi(),n.WDi(),n.XNi())},n.prototype.pTi=function(t,e){var n,t,n=this,t=t.value;return!("string"==typeof t&&-1<t.indexOf(".")&&(t=t.split(".")[0],k(n.getDataView().ufe({value:t},e))))},n.prototype.Gno=function(){var t,e,n,i,o,u,r,a,t=this,e=t.Mf,n=t.getDataView(),i=n.visibleLength(),o=t.Bno;for(u in o)o.hasOwnProperty(u)&&(r=o[u],a=Number(u),t.zDi(a)||i<=a?t.oTi(a,r):n.updateItem(a,r,function(){},x(e,a)));t.Bno=E},n.prototype.tDe=function(){var t,e,n,i,t=this,e=t.Mf,n=e.getRowCount()-(t.options.allowAddNew?1:0),i=t.getDataView().visibleLength();n<=i?(e.setRowCount(i-(t.options.allowAddNew?0:1)),e.ffe(n-1,1,3,{rowExpand:!1,clearUndoNotInCommand:!1})):(e.setRowCount(i),t.options.allowAddNew&&e.ffe(i,1,3,{rowExpand:!1,clearUndoNotInCommand:!1}))},n.prototype.v7=function(){var h=this,g=h.Mf;function n(t,e){var n;h.nPt(),h.WDi(),h.HDi(),h.XNi(),h.getParent().options.calcOnDemand?(n=h.getDataView().gLt)&&n.o_t(E,!0):setTimeout(function(){var t,t,t=null==(t=h.getDataView())?void 0:t.gLt;t&&(g.suspendCalcService(),t.o_t(E,!0),g.resumeCalcService(!1))},10)}g.D3(L.Events.EditStarting+i,function(t,e){e.col===h.kDi()&&(e.cancel=!0)}),g.D3(L.Events.EditEnded+i,function(t,e){var n,i,i,n=e.row,i=e.ignoreValueChange;if(!i){if((0,b.isNullOrEmpty)(e.editingText)&&h.zDi(n)){if(i=h.fDi(e.col),k(i.defaultValue)||!h.pTi(i,n))return;h.oTi(n)}h.aTi(n,!0)}}),g.D3(L.Events.ClipboardPasting+i,function(t,e){var n=e.cellRange,i=n.col,n=n.col+n.colCount-1,o=h.kDi(),u;i<=o&&o<=n&&(e.cancel=!0),e.cancel||(h.Bno={},h.getDataView().mme())}),g.D3(L.Events.ClipboardPasted+i,function(t,e){h.Gno();var e=e.cellRange,e=e.row+e.rowCount-1;h.aTi(e),h.getDataView().gme(),h.iM(e)}),g.D3(L.Events.DragFillBlock+i,function(t,e){var n,i,o,i,n,n=e.fillRange,i=e.fillDirection,o=n.col+n.colCount-1;0===i&&(-1!==n.row&&-1!==n.rowCount&&(o+=1),n=(i=e.sheet.getSelections()[0]).col+i.colCount-1,o=Math.max(o,n)),o===h.kDi()&&(e.cancel=!0),e.cancel||(h.Bno={},h.getDataView().mme())}),g.D3(L.Events.DragFillBlockCompleted+i,function(t,e){h.Gno();var e=e.fillRange,e=e.row+e.rowCount-1;h.aTi(e),h.getDataView().gme(),h.iM(e)}),g.D3(L.Events.DragDropBlock+i,function(t,e){var n,i,o,u,r,a,c,s,l,f,d,n=e.fromCol,i=e.toCol,o=e.colCount,u=e.insert,r=e.fromRow,a=e.toRow,c=n+o-1,s=i+o-1,l=h.kDi();c===l||s===l?e.cancel=!0:u&&-1===r&&-1===a&&(h.OPt(n,f=n<i?i-1:i,o),h.UMi&&h.UMi.updatePanelLayout(),g.setSelection(-1,d=n<i?i-o:i,-1,o),e.cancel=!0),e.cancel||(h.Bno={},h.getDataView().mme())}),g.D3(L.Events.DragDropBlockCompleted+i,function(t,e){h.Gno();var e=e.toRow+e.rowCount-1;h.aTi(e),h.getDataView().gme(),h.iM(e)}),g.D3(L.Events.ValueChanged+i,function(t,e){h.aTi(e.row)}),g.D3(L.Events.CellChanged+i,function(t,e){var n,i,o,u,o,n=e.row,i=e.col,o=e.sheetArea,u=e.newValue;o===L.SheetArea.colHeader&&(o=n<g.Ut.getFreeAreaRowCount(o),"value"===e.propertyName?o?h.YNi(n,i,"value",u):h.HNi(i):"formula"===e.propertyName&&o&&h.YNi(n,i,"formula",u))}),g.D3(L.Events.RangeChanged+i,function(t,e){var n,i,o,u,r,a,u,c;if(6===e.action)for(i=0,o=n=e.changedCells;i<o.length;i++)r=(u=o[i]).row,a=u.col,(u=u.sheetArea)===L.SheetArea.colHeader&&((c=r<g.Ut.getFreeAreaRowCount(u))?h.YNi(r,a,"value",g.getValue(r,a,u)):h.HNi(a))}),g.D3(L.Events.ColumnWidthChanged+i,function(t,e){var n,i,n=g.getDataSource(),i=n.getColumnInfos();!e.header&&!k(i)&&0<i.length&&e.colList.forEach(function(t){var e=i[t]&&f({},i[t]);k(e)||(e.width=g.Do(t,1),n.JEe(e))})}),g.D3(L.Events.ColumnChanged+i,function(t,e){var n,i,o,n;"isVisible"!==e.propertyName||1!==e.sheetArea&&3!==e.sheetArea||(n=g.getDataSource())&&(i=n.kve(e.col))&&(h.qni.startAddComboChanges(),o=!1!==i.visible,n.JEe(f(f({},i),{visible:e.newValue})),o!==e.newValue&&h.Teo(!0)&&(n=g.k9(L.SheetArea.viewport),e.newValue?e.col===n:e.col<n)&&h.Seo(),h.qni.endAddComboChanges())}),g.D3(L.Events.RangeFiltering+i,function(){h.yno=!0}),g.D3(L.Events.RangeFiltered+i,function(t,e){h.yno=!1,n(t,e)}),g.D3(L.Events.RangeFilterClearing+i,function(){h.nAi=!0}),g.D3(L.Events.RangeFilterCleared+i,function(t,e){h.nAi=!1,n(t,e)}),g.D3(L.Events.RangeSorting+i,function(){h.nDi()}),g.D3(L.Events.RangeSorted+i,function(t,e){(h.FDi(e.col)||h.JDi(e.col))&&(h.WDi(E,!1),h.HDi()),h.uDi()}),g.D3(L.Events.OutlineColumnCheckStatusChanged+i,function(t,e){h.sDi()})},n.prototype.Ob=function(t,e,n,i){var u,o,o,r,a,c,s,u=this;"allowAddNew"===t?!0===e&&!1===n?u.CNi():!1===e&&!0===n&&u.rTi():"alternatingRowOptions"===t?u.Mf.repaint():"defaultStackRowHeight"===t?u.Mf.zw():"isDesignMode"===t?u.vNi(e):"sheetTabColor"===t?(o=u.getParent(),k(o.getHost())||o.AC(!1)):"enableDefineColumn"===t?(u.Mf.suspendPaint(),u.Mf.options.addColumnButtonOption=e?{visible:!0,width:Z,command:u.options.defineColumnCommand,style:Z,tooltip:(0,l.getSR)().defineColumn}:{visible:!1,width:Z,command:ft,style:Z,tooltip:Z},u.Mf.resumePaint()):"defineColumnCommand"===t?(u.Mf.suspendPaint(),u.Mf.options.addColumnButtonOption?u.Mf.options.addColumnButtonOption.command=e:u.Mf.options.addColumnButtonOption={visible:!0,width:Z,command:u.options.defineColumnCommand,style:Z,tooltip:(0,l.getSR)().defineColumn},u.Mf.resumePaint()):"showRowNumber"===t?(u.Mf.suspendPaint(),e?(u.Mf.setColumnCount(u.Mf.getColumnCount(L.SheetArea.rowHeader)+1,L.SheetArea.rowHeader),u.Mf.options.rowHeaderAutoText=L.HeaderAutoText.numbers):(u.Mf.setColumnCount(u.Mf.getColumnCount(L.SheetArea.rowHeader)-1,L.SheetArea.rowHeader),u.Mf.options.rowHeaderAutoText=L.HeaderAutoText.blank),u.Mf.resumePaint()):"groupLayout"===t&&(u.Mf.suspendPaint(),e?(o=Ct(e.mode),r=e.position,a={mode:o},r&&wt(r)&&(a.position=r),c=function(t,e,n){var i,o;return"mode"===t?(i=Ct(e))!==n&&(o=!0):"position"===t&&(i=wt(e)?e:E),[i,function(){u.Beo(o,!0)}]},s=u.options.groupLayout,["mode","position"].forEach(function(t){var e=a[t];pt(s,t,c),s["_"+t]=e}),u.Beo(e.mode!==(n&&n.mode),!0)):u.options.groupLayout=i&&i.groupLayout?f({},i.groupLayout):{mode:d.GroupLayoutMode.tabular},u.Mf.resumePaint()),this.qni.addUndoChanges({type:v.TableSheetUndoType.OptionChanged,oldData:n,newData:e,key:t})},n.prototype.vNi=function(t){var e,n,e=this.Mf;e.suspendPaint(),e.suspendEvent(),t&&((n=new L.CellTypes.Text).isImeAware=function(){return!1}),e.setCellType(-1,-1,n,L.SheetArea.colHeader),e.resumeEvent(),e.resumePaint()},n.prototype.getDefaultOptions=function(){return{allowAddNew:!0,alternatingRowOptions:{step:[1,1],style:new L.Style("lightgrey")},defaultStackRowHeight:null,isDesignMode:!1,menuItemVisibility:{promoteMenuItemVisible:!1,demoteMenuItemVisible:!1,moveUpMenuItemVisible:!1,moveDownMenuItemVisible:!1,addBeforeMenuItemVisible:!1,addAfterMenuItemVisible:!1,addAboveMenuItemVisible:!1,addBelowMenuItemVisible:!1,expandAllLevelMenuItemVisible:!1,collapseAllLevelMenuItemVisible:!1,expandToLevelMenuItemVisible:!1},sheetTabColor:void 0,showRowNumber:!1,defineColumnCommand:lt,submitDefineColumnCommand:dt,enableDefineColumn:!1,groupLayout:{mode:d.GroupLayoutMode.tabular},columnTypeItems:[{name:d.ColumnTypes.Number,text:(0,l.getSR)().columnTypeNumber,iconClass:e(d.ColumnTypes.Number)},{name:d.ColumnTypes.Text,text:(0,l.getSR)().columnTypeText,iconClass:e(d.ColumnTypes.Text)},{name:d.ColumnTypes.Formula,text:(0,l.getSR)().columnTypeFormula,iconClass:e(d.ColumnTypes.Formula)},{name:d.ColumnTypes.Lookup,text:(0,l.getSR)().columnTypeLookup,iconClass:e(d.ColumnTypes.Lookup)},{name:d.ColumnTypes.Date,text:(0,l.getSR)().columnTypeDate,iconClass:e(d.ColumnTypes.Date)},{name:d.ColumnTypes.Checkbox,text:(0,l.getSR)().columnTypeCheckbox,iconClass:e(d.ColumnTypes.Checkbox)},{name:d.ColumnTypes.Select,text:(0,l.getSR)().columnTypeSelect,iconClass:e(d.ColumnTypes.Select)},{name:d.ColumnTypes.Currency,text:(0,l.getSR)().columnTypeCurrency,iconClass:e(d.ColumnTypes.Currency)},{name:d.ColumnTypes.Percent,text:(0,l.getSR)().columnTypePercent,iconClass:e(d.ColumnTypes.Percent)},{name:d.ColumnTypes.Phone,text:(0,l.getSR)().columnTypePhone,iconClass:e(d.ColumnTypes.Phone)},{name:d.ColumnTypes.Email,text:(0,l.getSR)().columnTypeEmail,iconClass:e(d.ColumnTypes.Email)},{name:d.ColumnTypes.URL,text:(0,l.getSR)().columnTypeURL,iconClass:e(d.ColumnTypes.URL)},{name:d.ColumnTypes.CreatedTime,text:(0,l.getSR)().columnTypeCreatedTime,iconClass:e(d.ColumnTypes.CreatedTime)},{name:d.ColumnTypes.ModifiedTime,text:(0,l.getSR)().columnTypeModifiedTime,iconClass:e(d.ColumnTypes.ModifiedTime)},{name:d.ColumnTypes.Attachment,text:(0,l.getSR)().columnTypeAttachment,iconClass:e(d.ColumnTypes.Attachment)},{name:d.ColumnTypes.Barcode,text:(0,l.getSR)().columnTypeBarcode,iconClass:e(d.ColumnTypes.Barcode)}]}},n.prototype.Zp=function(t){var i,o,n,t,i=this,o=i.getDefaultOptions();i.options=L.mt.b0(o,function(t,e,n){i.Ob(t,e,n,o)}),n=i.options,t=F(!0,{},o,t=t||{}),_(t,function(t,e){n.hasOwnProperty(t)&&(n[t]=e)})},n.prototype.sNi=function(){var i=this;i.actionColumn=new G.ActionColumn(function(t,e){var n;i.getDataView()&&(t.visible&&!e.visible?i.cTi():!t.visible&&e.visible?i.zNi():t.visible&&e.visible&&i.uTi(!1,t.title))})},n.prototype.zDi=function(t){var e=this;return e.options.allowAddNew&&t===e.Mf.getRowCount()-1},n.prototype.kDi=function(){var t=this,e=t.Mf;return t.actionColumn.options().visible?e.getColumnCount()-1:-1},n.prototype.id=function(){return this.getDataView().getDirtyIndexes()},n.prototype.e1e=function(){return this.options.sheetTabColor},n.prototype.parameter=function(t){var e,n,i,o;if(0===arguments.length){for(i in n={},e=this.o1e)e.hasOwnProperty(i)&&(n[i]=gt(e[i]));return n}if(t){for(i in o={},t)t.hasOwnProperty(i)&&(o[i]=ht(t[i]));this.o1e=o}},n.prototype.isAlternatingRowStyleColumn=function(t){return!0},n.prototype.getGroupingHeaderHeight=function(t,e){var n=this.options.groupLayout.mode;return n===d.GroupLayoutMode.outline?2*this.Mf.defaults.rowHeight:n===d.GroupLayoutMode.condensed?this.Mf.defaults.rowHeight:0},n.prototype.getGroupingFooterHeight=function(t,e){return this.getGroupingHeaderHeight(t,e)},n.prototype.refreshDataProvider=function(){var t,e;for(t in this.X$)this.X$[t]&&(e=this.X$[t],this.Mf.Cb.IP(),e.clearCache(),e.updateSticky(),e.updateRange(),e.repaintHost())},n.prototype.getValueInOneCell=function(t){var e,n,i,o,e,e=t.row,n=t.col,i=t.rowSpacingIndex,o=t.isAfterRowSpacing,e=this.Mf.getCellType(e,n,L.SheetArea.viewport,{rowSpacingIndex:i,isAfterRowSpacing:o});if(e&&e.getValueInOneCell)return e.getValueInOneCell(Object.assign({sheet:this.Mf},t))},n.prototype.isNewRow=function(t){return this.zDi(t)},n.prototype.onNewRowChanged=function(t,e){this.aTi(t,e)},n.prototype.isGroup=function(){return this.jFt},n.prototype.isGroupOutlineCondensedLayout=function(t){return this.Teo(t)},n.prototype.registerDataProvider=function(t,e){this.X$||(this.X$={}),this.X$[t]=e},n.prototype.unregisterDataProvider=function(t){this.X$[t]&&delete this.X$[t]},n.prototype.notChangeActiveSheetWhenUndo=function(){return 0<Object.keys(this.X$).length},n._ID=1,u=n,l.TableSheet=u,L.Workbook.ch("tableSheet",{init:function(){var t=this;L.Commands.uri(t.commandManager()),t.bind(L.Events.ActiveSheetChanged+ct,function(){(0,P.updateStatusBarOnActiveSheetChanged)(t)})},onCultureChanged:function(){(0,l.getSR)()},createSheetTab:function(t){t.type===L.SheetType.tableSheet?(t.name||(t.name=te(this.qv())),t.sheetTab=new u(t.name)):t.type===L.SheetType.customSheetTab&&(t.sheetTab=new d.CustomSheetTab(t.name))},preFromJson:function(t,e,n){u._ID=1},preFromSJSFormat:function(t,e,n){u._ID=1},dispose:function(){this.unbind(L.Events.ActiveSheetChanged+ct)}}),L.Worksheet.ch("tableSheet",{processKeyDown:function(t){var e,n,e=this.rt,n=this.NMi;n&&n.options.isDesignMode&&e.q9&&(t.r=!0)},processMouseDown:function(t){var e,n,e=t.hitInfo.cellTypeHitInfo;e&&e.isRowAction&&(n=Jt(t.e),e.isShift=n.isShift,e.isCtrl=n.isCtrl),k(kt(this))||(0,P.processStatusBarOnMouseDown)(kt(this).getParent().b3t,t.hitInfo)}}),L.Y8.ch("tablesheet",{priority:7e3,setValue:function(t){var e,n,i,o,u,r,a,c,o,s,l,f,r,f,e=t.row,n=t.col,i=t.value,o=t.sheetArea,u=this.Mf,r=u.H3,a=kt(u);if(o===L.SheetArea.viewport&&r&&r.getSource()&&Ht(a)){if(s=o=void 0,f=a.fDi(n,!(l=c=!1)),a.options.allowAddNew&&r.getRowCount()===e&&(!a.Bno||k(a.Bno[e]))){if((0,b.isNullOrEmpty)(i)&&k(f.defaultValue)||!a.pTi(f,e))return void(t.isValueSet=!0);(s=a.getDataView()).mme(),(l=s.K3e()&&!a.Bno)&&(a.Bno={}),a.Bno?a.Bno[e]={}:a.oTi(e),c=!0,o=s.length()-1}(r=u.Ut.q_(!1,L.SheetArea.colHeader,n)).name&&(i=L.util.V0(i),a.Bno?((f=a.Bno[e])||(a.Bno[e]=f={}),f[r.name]=i):a.$Di(e,r.name,i,{hasAddNewRowData:c,currentModelIndex:o}),t.isValueSet=!0),c&&(l&&(a.oTi(e,a.Bno[e]),a.Bno=E),s.gme())}}}),r[D]={name:D,icons:[$],command:"PinRow"},r[j]={name:j,icons:[et,nt,it]},r[C]={name:C,icons:[tt]},r[K]={name:K,icons:[L.ButtonImageType.minus],command:"RemoveRow"},r[w]={name:w,icons:[L.ButtonImageType.ok],command:"SaveRow"},r[q]={name:q,icons:[L.ButtonImageType.clear],command:"ResetRow"}},"./dist/plugins/tableSheet/tableSheet.res.en.js":function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.columnTypeFormula=e.columnTypeText=e.columnTypeNumber=e.defineColumnExisted=e.defineColumnRequired=e.defineColumnNoResults=e.defineColumnAll=e.defineColumnNone=e.defineColumnFormula=e.defineColumnOthers=e.defineColumnDefaultValue=e.defineColumnConfiguration=e.defineColumnFormatting=e.defineColumnType=e.defineColumnCaption=e.defineColumnValue=e.submit=e.cancel=e.defineColumn=e.Exp_InvalidOperationInProtectForTableSheet=e.CrossColumnDetailList=e.CrossColumnDetailAttributes=e.CrossColumnDetailFilter=e.CrossColumnDetailValueHeader=e.CrossColumnDetailCaption=e.CrossColumnDetailOver=e.CrossColumnDetailValuePlaceHolder=e.CrossColumnDetailValue=e.CrossColumnDetailName=e.CrossColumnDetailFormatter=e.CrossColumnCrossHeader=e.StatusBarToolTipRowCount=e.StatusBarRowCount=e.TextColon=e.GroupPanelAddCalculateColumn=e.GroupPanelItemRemoveAll=e.GroupPanelSummaryLabelPositionFooter=e.GroupPanelSummaryLabelPositionHeader=e.GroupPanelSummaryLabelPosition=e.GroupPanelSummaryLabelRelateTo=e.GroupPanelSummaryLabelSlice=e.GroupPanelSummaryLabelCaption=e.GroupPanelSummaryLabelFormula=e.GroupPanelDropDownCalcField=e.GroupPanelItemAddCalculation=e.GroupPanelItemRemove=e.GroupPanelGroupsHeader=e.GroupPanelFieldsHeader=e.GroupPanelTip=e.EXP_TooManyPinRecords=void 0,e.columnTypeAttachmentRight=e.columnTypeAttachmentTop=e.columnTypeAttachmentLeft=e.columnTypeAttachmentMarginGroup=e.columnTypeCalendarType=e.columnTypeShowEraFirstYear=e.columnTypeCalendar=e.columnTypeDateLocale=e.columnTypeDateType=e.columnTypeDateShowBuiltInDateRange=e.columnTypeDateShowDateRange=e.columnTypeDateShowTime=e.columnTypeDateMonth=e.columnTypeDateYear=e.columnTypeDateDay=e.columnTypeDateCalendarPage=e.columnTypeDateSunday=e.columnTypeDateSaturday=e.columnTypeDateFriday=e.columnTypeDateThursday=e.columnTypeDateWednesday=e.columnTypeDateTuesday=e.columnTypeDateMonday=e.columnTypeDateStartDay=e.columnTypeDateTimePicker=e.columnTypeAllEditableFields=e.columnTypeMask=e.columnTypeFormattingCategory=e.columnTypeLookupIndexes=e.columnTypeLookupFields=e.columnTypeLookupTables=e.columnTypeNumberFormatting=e.columnTypeNumberFormattingSymbol=e.columnTypeNumberFormattingNegativeNumbers=e.columnTypeNumberFormattingThousandSeparator=e.columnTypeNumberFormattingDecimalPlaces=e.columnTypeNumberFormattingSample=e.columnTypeBarcode=e.columnTypeSelect=e.columnTypeAttachment=e.columnTypeModifiedTime=e.columnTypeCreatedTime=e.columnTypeLookup=e.columnTypeURL=e.columnTypeEmail=e.columnTypePhone=e.columnTypePercent=e.columnTypeCurrency=e.columnTypeDate=e.columnTypeCheckbox=void 0,e.columnTypeUrlLabel=e.columnTypeComboBoxItemHeight=e.columnTypeComboBoxEditable=e.columnTypeComboBoxEditorValueType=e.columnTypeComboBoxRemove=e.columnTypeComboBoxAdd=e.columnTypeComboBoxValue=e.columnTypeComboBoxText=e.columnTypeComboBoxItemProperties=e.columnTypeComboBoxItems=e.columnTypeComboBoxEditorValueTypes=e.columnTypeBarcodeLabel=e.columnTypeCheckboxTextAlign=e.columnTypeCheckboxAuto=e.columnTypeCheckboxBoxSize=e.columnTypeCheckboxIsThreeState=e.columnTypeCheckboxCaption=e.columnTypeCheckboxOther=e.columnTypeCheckboxAlign=e.columnTypeCheckboxFalse=e.columnTypeCheckboxIndeterminate=e.columnTypeCheckboxTrue=e.columnTypeCheckboxTextGroup=e.columnTypeCheckboxTitle=e.columnTypeAttachmentSizeUnit=e.columnTypeAttachmentIsDownloadEnabled=e.columnTypeAttachmentIsClearEnabled=e.columnTypeAttachmentIsPreviewEnabled=e.columnTypeAttachmentAcceptValueTypes=e.columnTypeAttachmentAccept=e.columnTypeAttachmentMaxSize=e.columnTypeAttachmentBottom=void 0,e.EXP_TooManyPinRecords="Only support less than 10 pin records.",e.GroupPanelTip="Drag here to set row groups",e.GroupPanelFieldsHeader="Fields",e.GroupPanelGroupsHeader="Groups",e.GroupPanelItemRemove="Remove",e.GroupPanelItemAddCalculation="Add Summary Field",e.GroupPanelDropDownCalcField="Field",e.GroupPanelSummaryLabelFormula="Formula",e.GroupPanelSummaryLabelCaption="Caption",e.GroupPanelSummaryLabelSlice="Slice",e.GroupPanelSummaryLabelRelateTo="Relate to",e.GroupPanelSummaryLabelPosition="Position",e.GroupPanelSummaryLabelPositionHeader="Header",e.GroupPanelSummaryLabelPositionFooter="Footer",e.GroupPanelItemRemoveAll="Remove All",e.GroupPanelAddCalculateColumn="Add Calculated Column",e.TextColon=":",e.StatusBarRowCount="Row Count",e.StatusBarToolTipRowCount="Count of selected rows",e.CrossColumnCrossHeader="Cross",e.CrossColumnDetailFormatter="Formatter",e.CrossColumnDetailName="Name",e.CrossColumnDetailValue="Value",e.CrossColumnDetailValuePlaceHolder="Drag item here to set value",e.CrossColumnDetailOver="Over",e.CrossColumnDetailCaption="Caption",e.CrossColumnDetailValueHeader="Show Cross Value Header",e.CrossColumnDetailFilter="Filter",e.CrossColumnDetailAttributes="Attributes",e.CrossColumnDetailList="List",e.Exp_InvalidOperationInProtectForTableSheet="The range you're trying to change is locked.",e.defineColumn="Define Column",e.cancel="Cancel",e.submit="Submit",e.defineColumnValue="Value",e.defineColumnCaption="Caption",e.defineColumnType="Type",e.defineColumnFormatting="Formatting",e.defineColumnConfiguration="Configuration",e.defineColumnDefaultValue="Default",e.defineColumnOthers="Others",e.defineColumnFormula="Formula",e.defineColumnNone="None",e.defineColumnAll="All",e.defineColumnNoResults="No Results",e.defineColumnRequired="It's required",e.defineColumnExisted="It's existed",e.columnTypeNumber="Number",e.columnTypeText="Text",e.columnTypeFormula="Formula",e.columnTypeCheckbox="Checkbox",e.columnTypeDate="Date",e.columnTypeCurrency="Currency",e.columnTypePercent="Percent",e.columnTypePhone="Phone",e.columnTypeEmail="Email",e.columnTypeURL="URL",e.columnTypeLookup="Lookup",e.columnTypeCreatedTime="CreatedTime",e.columnTypeModifiedTime="ModifiedTime",e.columnTypeAttachment="Attachment",e.columnTypeSelect="Select",e.columnTypeBarcode="Barcode",e.columnTypeNumberFormattingSample="Sample",e.columnTypeNumberFormattingDecimalPlaces="Decimal places",e.columnTypeNumberFormattingThousandSeparator="Use 1000 Separator(,)",e.columnTypeNumberFormattingNegativeNumbers="Negative numbers",e.columnTypeNumberFormattingSymbol="Symbol",e.columnTypeNumberFormatting={negativeNumbers:{"-1234.10":"-1234.10","red:1234.10":"1234.10","(1234.10)":"(1234.10)","red:(1234.10)":"(1234.10)"},numberCategoryFormats:["0","0;[Red]0","0_);(0)","0_);[Red](0)","#,##0","#,##0;[Red]#,##0","#,##0_);(#,##0)","#,##0_);[Red](#,##0)"],japanEmperorReignDateFormat:["[$-411]ge.m.d;@",'[$-411]ggge"\u5e74"m"\u6708"d"\u65e5";@'],japanEmperorReignFirstYearDateFormat:["[$-411]ge.m.d;@",'[$-ja-JP-x-gannen]ggge"\u5e74"m"\u6708"d"\u65e5";@'],accountingSymbol:[["None","",""],["$","$","en-US"],["\xa5(Chinese)","\xa5","zh-cn"],["\xa5(Japanese)","\xa5","ja-jp"],["\u20a9(Korean)","\u20a9","ko-kr"]],percentageFormats:["0%"],currencyFormatWithoutSymbol:["#,##0","#,##0;[Red]#,##0","#,##0_);(#,##0)","#,##0_);[Red](#,##0)"],commonFormats:{Number:{format:"0.00",label:"Number"},Currency:{format:"[$$-409]#,##0.00",label:"Currency"},Accounting:{format:'_("$"* #,##0.00_);_("$"* (#,##0.00);_("$"* "-"??_);_(@_)',label:"Accounting"},ShortDate:{format:"m/d/yyyy",label:"Short Date"},LongDate:{format:"[$-409]dddd, mmmm dd, yyyy",label:"Long Date"},Time:{format:"[$-409]h:mm:ss AM/PM",label:"Time"},Percentage:{format:"0.00%",label:"Percentage"},Fraction:{format:"# ?/?",label:"Fraction"},Scientific:{format:"0.00E+00",label:"Scientific"},Text:{format:"@",label:"Text"},Comma:{format:'_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)',label:"Comma"}}},e.columnTypeLookupTables="Select lookup table",e.columnTypeLookupFields="The field to look up",e.columnTypeLookupIndexes="The record number",e.columnTypeFormattingCategory="Category",e.columnTypeMask={pattern:"Pattern",placeholder:"Placeholder",excludeLiteral:"Exclude Literal",excludePlaceholder:"Exclude Placeholder"},e.columnTypeAllEditableFields="All Editable Fields",e.columnTypeDateTimePicker="DateTime Picker",e.columnTypeDateStartDay="StartDay",e.columnTypeDateMonday="Monday",e.columnTypeDateTuesday="Tuesday",e.columnTypeDateWednesday="Wednesday",e.columnTypeDateThursday="Thursday",e.columnTypeDateFriday="Friday",e.columnTypeDateSaturday="Saturday",e.columnTypeDateSunday="Sunday",e.columnTypeDateCalendarPage="CalendarPage",e.columnTypeDateDay="Day",e.columnTypeDateYear="Year",e.columnTypeDateMonth="Month",e.columnTypeDateShowTime="ShowTime",e.columnTypeDateShowDateRange="ShowDateRange",e.columnTypeDateShowBuiltInDateRange="ShowBuiltInDateRange",e.columnTypeDateType="Type",e.columnTypeDateLocale="locale (location)",e.columnTypeCalendar="Calendar type",e.columnTypeShowEraFirstYear="Use Gannen to display 1st year",e.columnTypeCalendarType={western:"Western",JER:"Japanese Emperor Reign"},e.columnTypeAttachmentMarginGroup="Margin",e.columnTypeAttachmentLeft="Left",e.columnTypeAttachmentTop="Top",e.columnTypeAttachmentRight="Right",e.columnTypeAttachmentBottom="Bottom",e.columnTypeAttachmentMaxSize="Size Limit",e.columnTypeAttachmentAccept="File Type",e.columnTypeAttachmentAcceptValueTypes={txt:"txt",all:"all",pdf:"pdf",image:"jpg,png,gif",excel:"docx,xlsx,pptx"},e.columnTypeAttachmentIsPreviewEnabled="Preview",e.columnTypeAttachmentIsClearEnabled="Clear",e.columnTypeAttachmentIsDownloadEnabled="Download",e.columnTypeAttachmentSizeUnit="KB",e.columnTypeCheckboxTitle="CheckBox CellType",e.columnTypeCheckboxTextGroup="Text",e.columnTypeCheckboxTrue="True",e.columnTypeCheckboxIndeterminate="Indeterminate",e.columnTypeCheckboxFalse="False",e.columnTypeCheckboxAlign="Align",e.columnTypeCheckboxOther="Other",e.columnTypeCheckboxCaption="Caption",e.columnTypeCheckboxIsThreeState="IsThreeState",e.columnTypeCheckboxBoxSize="Box Size",e.columnTypeCheckboxAuto="Auto",e.columnTypeCheckboxTextAlign={top:"Top",bottom:"Bottom",left:"Left",right:"Right"},e.columnTypeBarcodeLabel={showLabel:"Show Label",barcodeType:"Barcode Type",color:"Color",errorCorrectionLevel:"Error Correction Level",backgroundColor:"Background Color",version:"Version",model:"Model",mask:"Mask",connection:"Connection",charCode:"CharCode",connectionNo:"Connection No",charset:"Charset",quietZoneLeft:"Quiet Zone Left",quietZoneRight:"Quiet Zone Right",quietZoneTop:"Quiet Zone Top",quietZoneBottom:"Quiet Zone Bottom",labelPosition:"Label Position",addOn:"AddOn",addOnLabelPosition:"AddOn Label Position",fontFamily:"Font Family",fontStyle:"Font Style",fontWeight:"Font Weight",fontTextDecoration:"Font TextDecoration",fontTextAlign:"Font TextAlign",fontSize:"Font Size",fileIdentifier:"File Identifier",structureNumber:"Structure Number",structureAppend:"Structure Append",ecc00_140Symbol:"Ecc000_140 Symbol Size",ecc200EncodingMode:"Ecc200 Endcoding Mode",ecc200SymbolSize:"Ecc200 Symbol Size",eccMode:"Ecc Mode",compact:"Compact",columns:"Columns",rows:"Rows",groupNo:"GroupNo",grouping:"Grouping",codeSet:"Code Set",fullASCII:"Full ASCII",checkDigit:"Check Digit",nwRatio:"Wide And Narrow Bar Ratio",labelWithStartAndStopCharacter:"Label With Start And Stop Character"},e.columnTypeComboBoxEditorValueTypes="EditorValueType",e.columnTypeComboBoxItems="Items",e.columnTypeComboBoxItemProperties="ItemProperties",e.columnTypeComboBoxText="Text",e.columnTypeComboBoxValue="Value",e.columnTypeComboBoxAdd="Add",e.columnTypeComboBoxRemove="Remove",e.columnTypeComboBoxEditorValueType={text:"Text",index:"Index",value:"Value"},e.columnTypeComboBoxEditable="Editable",e.columnTypeComboBoxItemHeight="Items Height",e.columnTypeUrlLabel={linkColor:"Link Color",visitedLinkColor:"Visited Link Color"}},"./dist/plugins/tableSheet/tablesheet-command-register.js":function(t,e,n){var r,e,l,i,n,o;function f(t){return"string"==typeof t}function u(t){return t.reduce(function(t,e){return t.push(e),o(e.subActionOptions)?t.concat(e.subActionOptions):t},[])}function d(t){return f(t)?t.toUpperCase().charCodeAt(0):t}function a(t){return"ROWACTION_".concat(t)}function h(u){var t=a(u);return l.Commands[t]={canUndo:!1,execute:function(t,e){var n=t.commandManager(),t=t.Ofe(e.sheetName),i=t.Mf,o=e.isDataRange?e.row:i.getActiveRowIndex(),i=i.getDataItem(o);return n.execute(r(r({},e),{cmd:u,index:o,item:i,tableSheet:t})),!0}},t}r=this&&this.__assign||function(){return(r=Object.assign||function(t){for(var e,n,i,o,n=1,i=arguments.length;n<i;n++)for(o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},Object.defineProperty(e,"__esModule",{value:!0}),e=n("Common"),l=n("Core"),i=n("./dist/plugins/tableSheet/tablesheet-command.js"),n=e.Common.lt,o=n.Nh,l.Commands.uri=function(t){var e=l.mt.Qf(),n=!e,e=e;t.register(i.SAVE_ALL,l.Commands[i.SAVE_ALL],"S".charCodeAt(0),n,!0,!1,e),t.register(i.EXPAND_GROUP_ITEM,l.Commands[i.EXPAND_GROUP_ITEM]),t.register(i.EXPAND_GROUP,l.Commands[i.EXPAND_GROUP]),t.register(i.REMOVE_COLUMN_TO_TABLE,l.Commands[i.REMOVE_COLUMN_TO_TABLE]),t.register(i.MODIFY_COLUMN_TO_TABLE,l.Commands[i.MODIFY_COLUMN_TO_TABLE]),t.register(i.DEFINE_COLUMN_TO_TABLE,l.Commands[i.DEFINE_COLUMN_TO_TABLE]),t.register(i.VIEW_REMOVE_COLUMN_INFO,l.Commands[i.VIEW_REMOVE_COLUMN_INFO]),t.register(i.VIEW_UPDATE_COLUMN_INFO,l.Commands[i.VIEW_UPDATE_COLUMN_INFO]),t.register(i.VIEW_Add_COLUMN_INFO,l.Commands[i.VIEW_Add_COLUMN_INFO]),t.register(i.VIEW_MOVE_COLUMN,l.Commands[i.VIEW_MOVE_COLUMN]),t.register(i.GROUP_OPTIONS,l.Commands[i.GROUP_OPTIONS]),t.register(i.RESET_NEW_VIEW,l.Commands[i.RESET_NEW_VIEW]),t.register(i.REMOVE_CROSS_COLUMN,l.Commands[i.REMOVE_CROSS_COLUMN])},l.Commands.iMi=function(t,e){var s=t;u(e).forEach(function(t){var e,t,t,n,i,o,u,t,r,a,c,n,e=t.command,t=t.shortcutKey,t=void 0===t?{}:t,n=t.key,i=t.ctrl,o=t.shift,u=t.alt,t=t.meta;f(e)&&(r=h(e),a=s.getCommand(r),c=s.getCommand(e),n=d(n),c?f(c.shortcutKey)&&s.setShortcutKey(e,null,!1,!1,!1,!1):s.register(e,l.Commands[e]),a?a.shortcutKey!==s.getShortcutKey(n,i,o,u,t)&&s.setShortcutKey(r,n,i,o,u,t):s.register(r,l.Commands[r],n,i,o,u,t))})}},"./dist/plugins/tableSheet/tablesheet-command.js":function(t,e,n){var i,o,u,r,a,c;Object.defineProperty(e,"__esModule",{value:!0}),e.REMOVE_CROSS_COLUMN=e.RESET_NEW_VIEW=e.DEFINE_COLUMN_TO_TABLE=e.MODIFY_COLUMN_TO_TABLE=e.REMOVE_COLUMN_TO_TABLE=e.VIEW_MOVE_COLUMN=e.VIEW_Add_COLUMN_INFO=e.VIEW_UPDATE_COLUMN_INFO=e.VIEW_REMOVE_COLUMN_INFO=e.GROUP_OPTIONS=e.EXPAND_GROUP=e.EXPAND_GROUP_ITEM=e.RESET_ROW=e.SAVE_ROW=e.REMOVE_ROW=e.PIN_ROW=e.SAVE_ALL=void 0,i=n("Core"),o=n("./dist/plugins/tableSheet/tableSheet-action.js"),u="TableSheet",r=function(t){return u+t},e.SAVE_ALL="SaveAll",e.PIN_ROW="PinRow",e.REMOVE_ROW="RemoveRow",e.SAVE_ROW="SaveRow",e.RESET_ROW="ResetRow",e.EXPAND_GROUP_ITEM=u+"ExpandGroupItemCommand",e.EXPAND_GROUP=u+"ExpandGroupCommand",e.GROUP_OPTIONS=u+"SetGroupOptions",e.VIEW_REMOVE_COLUMN_INFO=u+"ViewRemoveColumnInfo",e.VIEW_UPDATE_COLUMN_INFO=u+"ViewUpdateColumnInfo",e.VIEW_Add_COLUMN_INFO=u+"ViewAddColumnInfo",e.VIEW_MOVE_COLUMN=u+"ViewMoveColumn",e.REMOVE_COLUMN_TO_TABLE="RemoveColumn",e.MODIFY_COLUMN_TO_TABLE="ModifyColumn",e.DEFINE_COLUMN_TO_TABLE="DefineColumn",e.RESET_NEW_VIEW=u+"ResetNewView",e.REMOVE_CROSS_COLUMN=u+"CrossColumnOptions",a=i.Commands.zA,i.Commands[e.SAVE_ALL]={canUndo:!(c=function(){}),execute:function(t,e,n){var i,t,o,u,r,i=t.Ofe(e.sheetName),t=i.id();for(i.suspendPaint(),o=0,u=t;o<u.length;o++)r=u[o],i.saveRow(r).catch(c);return i.resumePaint(),!0}},i.Commands[e.SAVE_ROW]={canUndo:!1,execute:function(t,e){var n=e.tableSheet,e=e.index;return n.Mf.ki(i.Events.RowOperation,{sheet:n,sheetName:n.name(),actionType:1,row:e}),n.saveRow(e).catch(c),!0}},i.Commands[e.RESET_ROW]={canUndo:!0,execute:function(t,e,n){return e.sheetName=e.tableSheet.name(),a(t,o.ResetRowAction,e,n)}},i.Commands[e.EXPAND_GROUP_ITEM]={canUndo:!0,execute:function(t,e,n){return a(t,o.ExpandGroupItemAction,e,n)}},i.Commands[e.EXPAND_GROUP]={canUndo:!0,execute:function(t,e,n){return a(t,o.ExpandGroupAction,e,n)}},i.Commands[e.PIN_ROW]={canUndo:!0,execute:function(t,e,n){return e.sheetName=e.tableSheet.name(),a(t,o.PinRowAction,e,n)}},i.Commands[e.REMOVE_ROW]={canUndo:!0,execute:function(t,e,n){return a(t,o.RemoveRowAction,e,n)}},i.Commands[e.MODIFY_COLUMN_TO_TABLE]={canUndo:!0,execute:function(t,e,n){return a(t,o.ModifyColumnToTableAction,e,n)}},i.Commands[e.REMOVE_COLUMN_TO_TABLE]={canUndo:!0,execute:function(t,e,n){return a(t,o.RemoveColumnToTableAction,e,n)}},i.Commands[e.DEFINE_COLUMN_TO_TABLE]={canUndo:!0,execute:function(t,e,n){return a(t,o.DefineColumnToTableAction,e,n)}},i.Commands[e.VIEW_UPDATE_COLUMN_INFO]={canUndo:!0,execute:function(t,e,n){return a(t,o.ViewUpdateColumnInfoAction,e,n)}},i.Commands[e.VIEW_REMOVE_COLUMN_INFO]={canUndo:!0,execute:function(t,e,n){return a(t,o.ViewRemoveColumnInfoAction,e,n)}},i.Commands[e.VIEW_Add_COLUMN_INFO]={canUndo:!0,execute:function(t,e,n){return a(t,o.ViewAddColumnInfoAction,e,n)}},i.Commands[e.VIEW_MOVE_COLUMN]={canUndo:!0,execute:function(t,e,n){return a(t,o.ViewMoveColumnAction,e,n)}},i.Commands[e.GROUP_OPTIONS]={canUndo:!0,execute:function(t,e,n){return a(t,o.ViewGroupOptionsAction,e,n)}},i.Commands[e.RESET_NEW_VIEW]={canUndo:!0,execute:function(t,e,n){return a(t,o.TableSheetResertViewAction,e,n)}},i.Commands[e.REMOVE_CROSS_COLUMN]={canUndo:!0,execute:function(t,e,n){return a(t,o.TableSheetRemoveColumnInfoAction,e,n)}}},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-attachment-margin.js":function(t,e,n){var i,o,u,r,a,n,c,s;function l(t){var e;this.LEt=t}i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var e,n,i,o,n=1,i=arguments.length;n<i;n++)for(o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},Object.defineProperty(e,"__esModule",{value:!0}),e.zQe=void 0,o=n("Core"),u=n("./dist/plugins/tableSheet/tableSheet.js"),r=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-utils.js"),a=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-input-number.js"),n=o.mt,c=n.En,l.prototype.create=function(){var t,e,t=this,e;return(t.ZQe=(0,o.GC$)(c("div"))).addClass("gc-defined-column-attachment-margins-container"),t.ZX()},l.prototype.ZX=function(){var t,t,t,t,e,n,i,e=this,n=e.ZQe,i=e.LEt;return e._marginLeft=new a.AZe({inputId:"attachment-margin-left",minValue:0,label:{text:(0,u.getSR)().columnTypeAttachmentLeft},onChange:function(){e.QQe()}}),e._marginLeft.create().appendTo(n),e._marginLeft.setValue(null!=(t=i.marginLeft)?t:2),e._marginRight=new a.AZe({inputId:"attachment-margin-right",minValue:0,label:{text:(0,u.getSR)().columnTypeAttachmentRight},onChange:function(){e.QQe()}}),e._marginRight.create().appendTo(n),e._marginRight.setValue(null!=(t=i.marginRight)?t:2),e._marginTop=new a.AZe({inputId:"attachment-margin-top",minValue:0,label:{text:(0,u.getSR)().columnTypeAttachmentTop},onChange:function(){e.QQe()}}),e._marginTop.create().appendTo(n),e._marginTop.setValue(null!=(t=i.marginTop)?t:2),e._marginBottom=new a.AZe({inputId:"attachment-margin-bottom",minValue:0,label:{text:(0,u.getSR)().columnTypeAttachmentBottom},onChange:function(){e.QQe()}}),e._marginBottom.create().appendTo(n),e._marginBottom.setValue(null!=(t=i.marginBottom)?t:2),n},l.prototype.QQe=function(){var t,t,t,t,e,e=this;this.LEt.onChange({marginLeft:null==(t=e._marginLeft)?void 0:t.value,marginRight:null==(t=e._marginRight)?void 0:t.value,marginTop:null==(t=e._marginTop)?void 0:t.value,marginBottom:null==(t=e._marginBottom)?void 0:t.value})},l.prototype.setOptions=function(t){var e=this;e.LEt=i(i({},e.LEt),t),e.clear(),e.ZX()},l.prototype.clear=function(){var t=this;t.L0(),t.ZQe.empty()},l.prototype.dispose=function(){var t=this;t.clear(),t.ZQe=r.keyword_null},l.prototype.L0=function(){var t=this;t._marginLeft&&t._marginLeft.dispose(),t._marginLeft=r.keyword_null,t._marginRight&&t._marginRight.dispose(),t._marginRight=r.keyword_null,t._marginTop&&t._marginTop.dispose(),t._marginTop=r.keyword_null,t._marginBottom&&t._marginBottom.dispose(),t._marginBottom=r.keyword_null},e.zQe=l},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-attachment.js":function(t,e,n){var i,o,u,r,a,c,s,n,l,f,d,n;function h(t){var e;this.LEt=t}i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var e,n,i,o,n=1,i=arguments.length;n<i;n++)for(o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},Object.defineProperty(e,"__esModule",{value:!0}),e.YQe=e.AcceptExtensionsValueType=void 0,o=n("Core"),u=n("./dist/plugins/tableSheet/tableSheet.js"),r=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-checkbox.js"),a=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-select.js"),c=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-input-number.js"),s=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-utils.js"),n=o.mt,l=n.En,(n=f=e.AcceptExtensionsValueType||(e.AcceptExtensionsValueType={})).txt=".txt",n.all="",n.pdf=".pdf",n.image=".jpg, .png, .gif",n.excel=".xls, .xlsx, .ppt, .pptx, .doc, .docx",h.prototype.create=function(){var t,e,t=this,e;return(t.WQe=(0,o.GC$)(l("div"))).addClass("gc-defined-column-attachment-container"),t.ZX()},h.prototype.ZX=function(){var t,e,n,t=this,e=t.WQe,n=t.LEt;return t.cb=new a.Kze({inputId:"attachment-file-type",items:[{text:(0,u.getSR)().columnTypeAttachmentAcceptValueTypes.all,value:f.all},{text:(0,u.getSR)().columnTypeAttachmentAcceptValueTypes.txt,value:f.txt},{text:(0,u.getSR)().columnTypeAttachmentAcceptValueTypes.image,value:f.image},{text:(0,u.getSR)().columnTypeAttachmentAcceptValueTypes.pdf,value:f.pdf},{text:(0,u.getSR)().columnTypeAttachmentAcceptValueTypes.excel,value:f.excel}],label:{text:(0,u.getSR)().columnTypeAttachmentAccept},onChange:function(){t.JQe()}}),t.cb.create().appendTo(e),t.cb.setValue(n.fileType||""),t.XQe=new c.AZe({inputId:"attachment-size-limit",minValue:0,label:{text:(0,u.getSR)().columnTypeAttachmentMaxSize},onChange:function(){t.JQe()}}),t.XQe.create().appendTo(e),t.XQe.setValue(n.maxSize||2048),t.KQe=new r.Rze({label:{text:(0,u.getSR)().columnTypeAttachmentIsPreviewEnabled},onChange:function(){t.JQe()}}),t.KQe.create().appendTo(e),t.KQe.setValue(!!n.preview||(0,s.isNullOrUndefined)(n.preview)),t.$Qe=new r.Rze({label:{text:(0,u.getSR)().columnTypeAttachmentIsDownloadEnabled},onChange:function(){t.JQe()}}),t.$Qe.create().appendTo(e),t.$Qe.setValue(!!n.download||(0,s.isNullOrUndefined)(n.download)),t.d5=new r.Rze({label:{text:(0,u.getSR)().columnTypeAttachmentIsClearEnabled},onChange:function(){t.JQe()}}),t.d5.create().appendTo(e),t.d5.setValue(!!n.clear||(0,s.isNullOrUndefined)(n.clear)),e},h.prototype.JQe=function(){var t,t,t,t,t,e,e=this;this.LEt.onChange({fileType:null==(t=e.cb)?void 0:t.value,maxSize:null==(t=e.XQe)?void 0:t.value,preview:null==(t=e.KQe)?void 0:t.value,download:null==(t=e.$Qe)?void 0:t.value,clear:null==(t=e.d5)?void 0:t.value})},h.prototype.setOptions=function(t){var e=this;e.LEt=i(i({},e.LEt),t),e.clear(),e.ZX()},h.prototype.clear=function(){var t=this;t.L0(),t.WQe.empty()},h.prototype.dispose=function(){var t=this;t.clear(),t.WQe=s.keyword_null},h.prototype.L0=function(){var t=this;t.cb&&t.cb.dispose(),t.cb=s.keyword_null,t.XQe&&t.XQe.dispose(),t.XQe=s.keyword_null,t.KQe&&t.KQe.dispose(),t.KQe=s.keyword_null,t.$Qe&&t.$Qe.dispose(),t.$Qe=s.keyword_null,t.d5&&t.d5.dispose(),t.d5=s.keyword_null},e.YQe=h},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-base-options.js":function(t,e,n){var i,a,u,r,c,s,n,o,l;function f(t){var e=this;e.LEt=t,e.xT=[],e.selectBarcode="data_"+t.option.barcodeType,e.fontOptions=[]}Object.defineProperty(e,"__esModule",{value:!0}),e.Meo=void 0,i=n("Core"),a=n("./dist/plugins/tableSheet/tableSheet.js"),u=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-checkbox.js"),r=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-input.js"),c=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-input-number.js"),s=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-select.js"),n=i.mt,o=n.En,f.prototype.create=function(){var t;return this.ZX()},f.prototype.ZX=function(){},f.prototype.ato=function(t,e,n,i,o){var u,n,u=this,n=new s.Kze({inputId:"barcode-"+e,defaultValue:u.LEt.option[u.selectBarcode][e],items:n,enable:i,label:{text:(0,a.getSR)().columnTypeBarcodeLabel[e]},onChange:function(t){u.LEt.option[u.selectBarcode][e]=t.value,o&&o(t),u.onChange()}});return n.create().appendTo(t),u.xT.push(n),n},f.prototype.rto=function(t,e,n,i){var o,n,o=this,n=new r.mZe({inputId:"barcode-"+e,enable:n,defaultValue:o.LEt.option[o.selectBarcode][e],className:"gc-defined-column-barcode-options-input",label:{text:(0,a.getSR)().columnTypeBarcodeLabel[e]},onBlur:function(t){o.LEt.option[o.selectBarcode][e]=t,i&&i(t),o.onChange()}});return n.create().appendTo(t),o.xT.push(n),n},f.prototype.cto=function(t,e,n,i){var o,n,o=this,n=new u.Rze({defaultValue:o.LEt.option[o.selectBarcode][e],label:{text:(0,a.getSR)().columnTypeBarcodeLabel[e]},enable:n,textDisplay:1,inputId:e,onChange:function(t){o.LEt.option[o.selectBarcode][e]=t,i&&i(t),o.onChange()}});return n.create().appendTo(t),o.xT.push(n),n},f.prototype.teo=function(t,e,n,i,o,u){var r,n,r=this,n=new c.AZe({defaultValue:r.LEt.option[r.selectBarcode][e],minValue:n,maxValue:i,enable:o,label:{text:(0,a.getSR)().columnTypeBarcodeLabel[e]},inputId:e,onChange:function(t){r.LEt.option[r.selectBarcode][e]=t,u&&u(t),r.onChange()}});return n.create().appendTo(t),r.xT.push(n),n},f.prototype.neo=function(t){var e,n,e=this,n=(0,i.GC$)(o("div"));return n.addClass("gc-defined-column-empty-div"),n.appendTo(t),e.xT.push(n),n},f.prototype.ieo=function(t){var e,n,i,e=this,n=e.fontOptions,i=e.labelPosition;e.cto(t,"showLabel",void 0,function(t){t?(n.forEach(function(t){t.isDisable()&&t.resetDisable()}),i.isDisable()&&i.resetDisable()):(n.forEach(function(t){t.isDisable()||t.resetDisable()}),i.isDisable()||i.resetDisable())}),i=e.ato(t,"labelPosition",[{text:"top",value:"top"},{text:"bottom",value:"bottom"}],!1)},f.prototype.oeo=function(t){var e,n,e=this,n=e.fontOptions;n.push(e.ato(t,"fontFamily",[{text:"sans-serif",value:"sans-serif"},{text:"serif",value:"serif"},{text:"monospace",value:"monospace"},{text:"Arial",value:"Arial"},{text:"Verdana",value:"Verdana"},{text:"Times",value:"Times"}],!1)),n.push(e.ato(t,"fontStyle",[{text:"normal",value:"normal"},{text:"italic",value:"italic"}],!1)),n.push(e.ato(t,"fontWeight",[{text:"normal",value:"normal"},{text:"bold",value:"bold"}],!1)),n.push(e.ato(t,"fontTextDecoration",[{text:"none",value:"none"},{text:"underline",value:"underline"},{text:"overline",value:"overline"},{text:"line-through",value:"line-through"}],!1)),n.push(e.ato(t,"fontTextAlign",[{text:"center",value:"center"},{text:"left",value:"left"},{text:"right",value:"right"},{text:"group",value:"group"}],!1)),n.push(e.ato(t,"fontSize",[{text:"12",value:12},{text:"13",value:13},{text:"14",value:14},{text:"15",value:15},{text:"16",value:16},{text:"17",value:17},{text:"18",value:18},{text:"19",value:19},{text:"20",value:20},{text:"21",value:21},{text:"22",value:22}],!1)),n.forEach(function(t){t.container.addClass("gc-defined-column-field-item-small-container")})},f.prototype.onChange=function(){var t=this,e=t.LEt.onChange;e&&e(t.LEt.option)},f.prototype.YZe=function(t){for(var e,n,i,e=0,n=t;e<n.length;e++)(i=n[e]).dispose?i.dispose():i.unbind&&i.unbind("click");return[]},f.prototype.dispose=function(){var t=this;t.xT=t.YZe(t.xT)},e.Meo=f},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-base.js":function(t,e,n){var i,o,u,r,a,n,c,s;function l(t){var e=this;e.LEt=t,e.xT=[]}Object.defineProperty(e,"__esModule",{value:!0}),e.Ieo=void 0,i=n("Core"),o=n("./dist/plugins/tableSheet/tableSheet.js"),u=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-input.js"),r=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-input-number.js"),a=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-select.js"),n=i.mt,c=n.En,l.prototype.create=function(){var t;return this.ZX()},l.prototype.ZX=function(){var e,t,n,n,e=this,t=(0,i.GC$)(c("div"));return t.addClass("gc-defined-column-barcode-base-container"),(n=new a.Kze({inputId:"barcode-type",defaultValue:e.LEt.option.barcodeType,enableSearch:!0,items:[{text:"QRCODE",value:"QRCODE"},{text:"EAN13",value:"EAN13"},{text:"EAN8",value:"EAN8"},{text:"CODABAR",value:"CODABAR"},{text:"CODE39",value:"CODE39"},{text:"CODE93",value:"CODE93"},{text:"CODE128",value:"CODE128"},{text:"GS1_128",value:"GS1_128"},{text:"CODE49",value:"CODE49"},{text:"PDF417",value:"PDF417"},{text:"DATAMATRIX",value:"DATAMATRIX"}],label:{text:(0,o.getSR)().columnTypeBarcodeLabel.barcodeType},onChange:function(t){e.LEt.option.barcodeType=t.value,e.onChange(!0)}})).create().appendTo(t),e.xT.push(n),(n=(0,i.GC$)(c("div"))).addClass("gc-defined-column-barcode-base-style-container"),n.appendTo(t),e.tto(n,"color",(0,o.getSR)().columnTypeBarcodeLabel.color),e.tto(n,"backgroundColor",(0,o.getSR)().columnTypeBarcodeLabel.backgroundColor),e.eto(n,"quietZoneLeft",(0,o.getSR)().columnTypeBarcodeLabel.quietZoneLeft,!0),e.eto(n,"quietZoneRight",(0,o.getSR)().columnTypeBarcodeLabel.quietZoneRight,!0),e.eto(n,"quietZoneTop",(0,o.getSR)().columnTypeBarcodeLabel.quietZoneTop,!0),e.eto(n,"quietZoneBottom",(0,o.getSR)().columnTypeBarcodeLabel.quietZoneBottom,!0),t},l.prototype.tto=function(t,e,n){var i,n,i=this,n=new u.mZe({inputId:"barcode-"+e,type:"color",defaultValue:i.LEt.option[e],label:{text:n},onChange:function(t){i.LEt.option[e]=t,i.onChange()}});n.create().appendTo(t),i.xT.push(n)},l.prototype.eto=function(t,e,n,i){var o,n,o=this,n=new r.AZe({inputId:"barcode-"+e,defaultValue:o.LEt.option[e],label:{text:n,useRawContent:i},allowMinNull:!0,maxValue:1e3,onChange:function(t){o.LEt.option[e]=t,o.onChange()}});n.create().appendTo(t),o.xT.push(n)},l.prototype.onChange=function(t){var e=this,n=e.LEt.onChange;n&&n(e.LEt.option,t)},l.prototype.YZe=function(t){for(var e,n,i,e=0,n=t;e<n.length;e++)(i=n[e]).dispose?i.dispose():i.unbind&&i.unbind("click");return[]},l.prototype.dispose=function(){var t=this;t.xT=t.YZe(t.xT)},e.Ieo=l},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-codabar.js":function(t,e,n){var i,o,n,u,r,a,c,s;function l(){return null!==s&&s.apply(this,arguments)||this}i=this&&this.__extends||(c=function(t,e){return(c=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}c(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),Object.defineProperty(e,"__esModule",{value:!0}),e.ueo=void 0,o=n("Core"),n=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-base-options.js"),u=o.mt,r=u.En,s=n.Meo,i(l,s),l.prototype.ZX=function(){var t,e,t=this,e=(0,o.GC$)(r("div"));return e.addClass("gc-defined-column-barcode-options-container"),t.ieo(e),t.cto(e,"checkDigit"),t.ato(e,"nwRatio",[{text:"3",value:"3"},{text:"2",value:"2"}]),t.oeo(e),e},e.ueo=l},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-code128.js":function(t,e,n){var i,o,n,u,r,a,c,s;function l(){return null!==s&&s.apply(this,arguments)||this}i=this&&this.__extends||(c=function(t,e){return(c=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}c(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),Object.defineProperty(e,"__esModule",{value:!0}),e.aeo=void 0,o=n("Core"),n=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-base-options.js"),u=o.mt,r=u.En,s=n.Meo,i(l,s),l.prototype.ZX=function(){var t,e,t=this,e=(0,o.GC$)(r("div"));return e.addClass("gc-defined-column-barcode-options-container"),t.ieo(e),t.ato(e,"codeSet",[{text:"auto",value:"auto"},{text:"A",value:"A"},{text:"B",value:"B"},{text:"C",value:"C"}]),t.neo(e),t.oeo(e),e},e.aeo=l},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-code39.js":function(t,e,n){var i,o,n,u,r,a,c,s;function l(){return null!==s&&s.apply(this,arguments)||this}i=this&&this.__extends||(c=function(t,e){return(c=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}c(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),Object.defineProperty(e,"__esModule",{value:!0}),e.ceo=void 0,o=n("Core"),n=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-base-options.js"),u=o.mt,r=u.En,s=n.Meo,i(l,s),l.prototype.ZX=function(){var t,e,t=this,e=(0,o.GC$)(r("div"));return e.addClass("gc-defined-column-barcode-options-container"),t.ieo(e),t.cto(e,"labelWithStartAndStopCharacter"),t.cto(e,"checkDigit"),t.ato(e,"nwRatio",[{text:"3",value:"3"},{text:"2",value:"2"}]),t.cto(e,"fullASCII"),t.oeo(e),e},e.ceo=l},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-code49.js":function(t,e,n){var i,o,n,u,r,a,c,s;function l(){return null!==s&&s.apply(this,arguments)||this}i=this&&this.__extends||(c=function(t,e){return(c=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}c(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),Object.defineProperty(e,"__esModule",{value:!0}),e.reo=void 0,o=n("Core"),n=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-base-options.js"),u=o.mt,r=u.En,s=n.Meo,i(l,s),l.prototype.ZX=function(){var t,e,t=this,e=(0,o.GC$)(r("div"));return e.addClass("gc-defined-column-barcode-options-container"),t.ieo(e),t.cto(e,"grouping"),t.rto(e,"groupNo"),t.oeo(e),e},e.reo=l},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-code93.js":function(t,e,n){var i,o,n,u,r,a,c,s;function l(){return null!==s&&s.apply(this,arguments)||this}i=this&&this.__extends||(c=function(t,e){return(c=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}c(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),Object.defineProperty(e,"__esModule",{value:!0}),e.seo=void 0,o=n("Core"),n=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-base-options.js"),u=o.mt,r=u.En,s=n.Meo,i(l,s),l.prototype.ZX=function(){var t,e,t=this,e=(0,o.GC$)(r("div"));return e.addClass("gc-defined-column-barcode-options-container"),t.ieo(e),t.cto(e,"checkDigit"),t.cto(e,"fullASCII"),t.oeo(e),e},e.seo=l},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-datamatrix.js":function(t,e,n){var i,o,u,r,n,a,c,s,l;function f(){return null!==l&&l.apply(this,arguments)||this}i=this&&this.__extends||(s=function(t,e){return(s=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}s(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),Object.defineProperty(e,"__esModule",{value:!0}),e.leo=void 0,o=n("Core"),u=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-base-options.js"),r=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-utils.js"),n=o.mt,a=n.En,l=u.Meo,i(f,l),f.prototype.ZX=function(){var t,e,n,i,t=this,e=(0,o.GC$)(a("div"));return e.addClass("gc-defined-column-barcode-options-container"),n=[],t.ato(e,"eccMode",[{text:"ECC000",value:"ECC000"},{text:"ECC050",value:"ECC050"},{text:"ECC080",value:"ECC080"},{text:"ECC100",value:"ECC100"},{text:"ECC140",value:"ECC140"},{text:"ECC200",value:"ECC200"}],void 0,function(t){var t=t.value;"ECC140"===t?(i.isDisable()&&i.resetDisable(),n.forEach(function(t){t.isDisable()||t.resetDisable()})):(n.forEach("ECC200"===t?function(t){t.isDisable()&&t.resetDisable()}:function(t){t.isDisable()||t.resetDisable()}),i.isDisable()||i.resetDisable())}),n.push(t.cto(e,"structureAppend",!1)),n.push(t.ato(e,"ecc200SymbolSize",[{text:"squareAuto",value:"squareAuto"},{text:"rectangularAuto",value:"rectangularAuto"},{text:"square10",value:"square10"},{text:"square12",value:"square12"},{text:"square14",value:"square14"},{text:"square16",value:"square16"},{text:"square18square20",value:"square18square20"},{text:"square22",value:"square22"},{text:"square24",value:"square24"},{text:"square26",value:"square26"},{text:"square32",value:"square32"},{text:"square36",value:"square36"},{text:"square40",value:"square40"},{text:"square44",value:"square44"},{text:"square48",value:"square48"},{text:"square52",value:"square52"},{text:"square64",value:"square64"},{text:"square72",value:"square72"},{text:"square80",value:"square80"},{text:"square88",value:"square88"},{text:"square96",value:"square96"},{text:"square104",value:"square104"},{text:"square120",value:"square120"},{text:"square132",value:"square132"},{text:"square144",value:"square144"},{text:"rectangular8x18",value:"rectangular8x18"},{text:"rectangular8x32",value:"rectangular8x32"},{text:"rectangular12x26",value:"rectangular12x26"},{text:"rectangular12x36",value:"rectangular12x36"},{text:"rectangular16x36",value:"rectangular16x36"},{text:"rectangular16x48",value:"rectangular16x48"}],!1)),n.push(t.ato(e,"structureNumber",(0,r.getItems)(15,!1,!0),!1)),n.push(t.ato(e,"ecc200EncodingMode",[{text:"auto",value:"auto"},{text:"ASCII",value:"ASCII"},{text:"C40",value:"C40"},{text:"Text",value:"Text"},{text:"X12",value:"X12"},{text:"EDIFACT",value:"EDIFACT"},{text:"Base256",value:"Base256"}],!1)),n.push(t.teo(e,"fileIdentifier",0,254,!1)),i=t.ato(e,"ecc00_140Symbol",[{text:"auto",value:"auto"},{text:"square9",value:"square9"},{text:"square11",value:"square11"},{text:"square13",value:"square13"},{text:"square15",value:"square15"},{text:"square17",value:"square17"},{text:"square19",value:"square19"},{text:"square21",value:"square21"},{text:"square23",value:"square23"},{text:"square25",value:"square25"},{text:"square27",value:"square27"},{text:"square29",value:"square29"},{text:"square31",value:"square31"},{text:"square33",value:"square33"},{text:"square35",value:"square35"},{text:"square37",value:"square37"},{text:"square39",value:"square39"},{text:"square41",value:"square41"},{text:"square43",value:"square43"},{text:"square45",value:"square45"},{text:"square47",value:"square47"},{text:"square49",value:"square49"}],!1),e},e.leo=f},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-ean13.js":function(t,e,n){var i,o,n,u,r,a,c,s;function l(){return null!==s&&s.apply(this,arguments)||this}i=this&&this.__extends||(c=function(t,e){return(c=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}c(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),Object.defineProperty(e,"__esModule",{value:!0}),e.feo=void 0,o=n("Core"),n=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-base-options.js"),u=o.mt,r=u.En,s=n.Meo,i(l,s),l.prototype.ZX=function(){var t,e,t=this,e=(0,o.GC$)(r("div"));return e.addClass("gc-defined-column-barcode-options-container"),t.ieo(e),t.rto(e,"addOn"),t.ato(e,"addOnLabelPosition",[{text:"top",value:"top"},{text:"bottom",value:"bottom"}]),t.oeo(e),e},e.feo=l},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-ean8.js":function(t,e,n){var i,o,n,u,r,a,c,s;function l(){return null!==s&&s.apply(this,arguments)||this}i=this&&this.__extends||(c=function(t,e){return(c=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}c(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),Object.defineProperty(e,"__esModule",{value:!0}),e.deo=void 0,o=n("Core"),n=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-base-options.js"),u=o.mt,r=u.En,s=n.Meo,i(l,s),l.prototype.ZX=function(){var t,e,t=this,e=(0,o.GC$)(r("div"));return e.addClass("gc-defined-column-barcode-options-container"),t.ieo(e),t.oeo(e),e},e.deo=l},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-gs1_128.js":function(t,e,n){var i,o,n,u,r,a,c,s;function l(){return null!==s&&s.apply(this,arguments)||this}i=this&&this.__extends||(c=function(t,e){return(c=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}c(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),Object.defineProperty(e,"__esModule",{value:!0}),e.heo=void 0,o=n("Core"),n=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-base-options.js"),u=o.mt,r=u.En,s=n.Meo,i(l,s),l.prototype.ZX=function(){var t,e,t=this,e=(0,o.GC$)(r("div"));return e.addClass("gc-defined-column-barcode-options-container"),t.ieo(e),t.oeo(e),e},e.heo=l},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-options-container.js":function(t,e,n){var i,o,u,r,a,c,s,l,f,d,h,g;function b(t){var e=this;e.LEt=t,e.xT=[]}Object.defineProperty(e,"__esModule",{value:!0}),e.nto=void 0,i=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-codabar.js"),o=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-ean13.js"),u=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-ean8.js"),r=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-code128.js"),a=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-code39.js"),c=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-code49.js"),s=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-code93.js"),l=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-datamatrix.js"),f=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-gs1_128.js"),d=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-pdf417.js"),h=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-qrcode.js"),b.prototype.create=function(){var t;this.ZX()},b.prototype.ZX=function(){var t;this.ito()},b.prototype.oto=function(t){var e,n,i,t,e=this,n=e.LEt.option,i=e.LEt.panel,t=new t({option:n,onChange:function(t){e.Z_i(t)}});t.create().appendTo(i),e.xT.push(t)},b.prototype.ito=function(){var t=this,e,n;switch(t.LEt.option.barcodeType){case"QRCODE":t.oto(h.uto);break;case"EAN13":t.oto(o.feo);break;case"EAN8":t.oto(u.deo);break;case"CODABAR":t.oto(i.ueo);break;case"CODE39":t.oto(a.ceo);break;case"CODE93":t.oto(s.seo);break;case"CODE128":t.oto(r.aeo);break;case"GS1_128":t.oto(f.heo);break;case"CODE49":t.oto(c.reo);break;case"PDF417":t.oto(d.geo);break;case"DATAMATRIX":t.oto(l.leo)}},b.prototype.Z_i=function(t){var e,n=this.LEt.onChange;n&&n(t)},b.prototype.reset=function(){var t=this;t.d5(),t.ZX()},b.prototype.d5=function(){var t=this;t.LEt.panel.empty(),t.xT=t.YZe(t.xT)},b.prototype.YZe=function(t){for(var e,n,i,e=0,n=t;e<n.length;e++)(i=n[e]).dispose?i.dispose():i.unbind&&i.unbind("click");return[]},b.prototype.dispose=function(){var t=this;t.xT=t.YZe(t.xT)},e.nto=b},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-pdf417.js":function(t,e,n){var i,o,u,r,n,a,c,s,l;function f(){return null!==l&&l.apply(this,arguments)||this}i=this&&this.__extends||(s=function(t,e){return(s=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}s(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),Object.defineProperty(e,"__esModule",{value:!0}),e.geo=void 0,o=n("Core"),u=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-base-options.js"),r=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-utils.js"),n=o.mt,a=n.En,l=u.Meo,i(f,l),f.prototype.ZX=function(){var t,e,t=this,e=(0,o.GC$)(a("div"));return e.addClass("gc-defined-column-barcode-options-container"),t.ato(e,"errorCorrectionLevel",(0,r.getItems)(8,!0,!0)),t.ato(e,"columns",(0,r.getItems)(30,!0)),t.ato(e,"rows",(0,r.getItems)(90,!0)),t.cto(e,"compact"),e},e.geo=f},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-qrcode.js":function(t,e,n){var i,o,n,u,r,a,c,s;function l(){return null!==s&&s.apply(this,arguments)||this}i=this&&this.__extends||(c=function(t,e){return(c=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}c(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),Object.defineProperty(e,"__esModule",{value:!0}),e.uto=void 0,o=n("Core"),n=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-base-options.js"),u=o.mt,r=u.En,s=n.Meo,i(l,s),l.prototype.ZX=function(){var t,e,t=this,e=(0,o.GC$)(r("div"));return e.addClass("gc-defined-column-barcode-options-container"),t.ato(e,"errorCorrectionLevel",[{text:"L",value:"L"},{text:"M",value:"M"},{text:"Q",value:"Q"},{text:"H",value:"H"}]),t.cto(e,"connection"),t.ato(e,"model",[{text:"2",value:2},{text:"1",value:1}]),t.ato(e,"connectionNo",[{text:"0",value:0},{text:"1",value:1},{text:"2",value:2},{text:"3",value:3},{text:"4",value:4},{text:"5",value:5},{text:"6",value:6},{text:"7",value:7},{text:"8",value:8},{text:"9",value:9},{text:"10",value:10},{text:"11",value:11},{text:"12",value:12},{text:"13",value:13},{text:"14",value:14},{text:"15",value:15}]),t.ato(e,"version",[{text:"auto",value:"auto"},{text:"1",value:"1"},{text:"2",value:"2"},{text:"3",value:"3"},{text:"4",value:"4"},{text:"5",value:"5"},{text:"6",value:"6"},{text:"7",value:"7"},{text:"8",value:"8"},{text:"9",value:"9"},{text:"10",value:"10"},{text:"11",value:"11"},{text:"12",value:"12"},{text:"13",value:"13"},{text:"14",value:"14"}]),t.rto(e,"charCode"),t.ato(e,"mask",[{text:"auto",value:"auto"},{text:"1",value:"1"},{text:"2",value:"2"},{text:"3",value:"3"},{text:"4",value:"4"},{text:"5",value:"5"},{text:"6",value:"6"},{text:"7",value:"7"}]),t.ato(e,"charset",[{text:"UTF-8",value:"UTF-8"},{text:"Shift_JIS",value:"Shift_JIS"}]),e},e.uto=l},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-utils.js":function(t,v,e){var u,M,n,I,e;function i(t){return{barcodeType:I.QRCODE,value:o(t),backgroundColor:"#FFFFFF",color:"#000000",data_CODABAR:v.BarCode_DefaultValue_CODABAR,data_CODE39:v.BarCode_DefaultValue_CODE39,data_CODE49:v.BarCode_DefaultValue_CODE49,data_CODE93:v.BarCode_DefaultValue_CODE93,data_CODE128:v.BarCode_DefaultValue_CODE128,data_DATAMATRIX:v.BarCode_DefaultValue_DATAMATRIX,data_EAN8:v.BarCode_DefaultValue_EAN8,data_EAN13:v.BarCode_DefaultValue_EAN13,data_GS1_128:v.BarCode_DefaultValue_GS1_128,data_QRCODE:v.BarCode_DefaultValue_QRCODE,data_PDF417:v.BarCode_DefaultValue_PDF417}}function o(t){return t?-1<t.indexOf(" ")?"[@["+t+"]]":"[@"+t+"]":n.keyword_null}function r(t,e){var n,n,i,n=null==(n=t.NMi)?void 0:n.getDataView();if(n)try{return n.Ywt(e)}catch(t){}try{return(i=t.getCalcService()).parse(null,e,0,0)}catch(t){}}function m(t,e){var n,i,o,n=t.parent;try{if((i=r(t,e))&&i.type===M.ExpressionType.function&&(o=i.functionName)&&n.getSparklineEx(o))return i}catch(t){}return null}function a(t,e){var t,n,i,o,u,r,e,a,c,s,l,f,d,h,g,b,b,t=m(t,e);if(t&&t.arguments&&(n={barcodeType:I.QRCODE,value:"",backgroundColor:"#FFFFFF",color:"#000000",data_CODABAR:v.BarCode_DefaultValue_CODABAR,data_CODE39:v.BarCode_DefaultValue_CODE39,data_CODE49:v.BarCode_DefaultValue_CODE49,data_CODE93:v.BarCode_DefaultValue_CODE93,data_CODE128:v.BarCode_DefaultValue_CODE128,data_DATAMATRIX:v.BarCode_DefaultValue_DATAMATRIX,data_EAN8:v.BarCode_DefaultValue_EAN8,data_EAN13:v.BarCode_DefaultValue_EAN13,data_GS1_128:v.BarCode_DefaultValue_GS1_128,data_QRCODE:v.BarCode_DefaultValue_QRCODE,data_PDF417:v.BarCode_DefaultValue_PDF417},!((o=(i=t.arguments).length)<=0))){for(u=0;u<o;u++)i[u].type===M.ExpressionType.reference&&(r="",i[u]={value:"=",type:"referenceValue"});for(e=t.functionName.substr(3),n.barcodeType=I[e],n.value=i[0]&&i[0].value?i[0].value:"",n.color=i[1]&&i[1].value?i[1].value:n.color,n.backgroundColor=i[2]&&i[2].value?i[2].value:n.backgroundColor,n.quietZoneLeft=i[o-4]&&i[o-4].value?i[o-4].value:"",n.quietZoneRight=i[o-4]&&i[o-3].value?i[o-3].value:"",n.quietZoneTop=i[o-4]&&i[o-2].value?i[o-2].value:"",n.quietZoneBottom=i[o-4]&&i[o-1].value?i[o-1].value:"",a={},c=3,s=v.DefaultBarCodeTypeData[n.barcodeType],l=0,f=Object.keys(s);l<f.length;l++)d=f[l],g=void 0,g=(h=i[c])&&h.type!==M.ExpressionType.missingArgument?h.value:s[d],a[d]=g,c++;switch(n.barcodeType){case I.QRCODE:(b=a).version=""+b.version,b.mask=""+b.mask,n.data_QRCODE=b;break;case I.EAN13:n.data_EAN13=a;break;case I.EAN8:n.data_EAN8=a;break;case I.CODE39:n.data_CODE39=a;break;case I.CODE49:n.data_CODE49=a;break;case I.CODE93:n.data_CODE93=a;break;case I.CODE128:n.data_CODE128=a;break;case I.PDF417:(b=a).errorCorrectionLevel=""+b.errorCorrectionLevel,b.rows=""+b.rows,b.columns=""+b.columns,n.data_PDF417=b;break;case I.DATAMATRIX:n.data_DATAMATRIX=a;break;case I.GS1_128:n.data_GS1_128=a}return n}}function c(t,e){var n,i,o,u,r,a,c,s,l,f,o,n="",i=[],o=e.value||"";try{d(o)?0<(u=[]).length&&i.push(o.substr(1)):i.push(o)}catch(t){i.push('"'+o+'"')}switch(i.push('"'+e.color+'"'),i.push('"'+e.backgroundColor+'"'),r={},e.barcodeType){case I.QRCODE:r=e.data_QRCODE;break;case I.EAN13:r=e.data_EAN13;break;case I.EAN8:r=e.data_EAN8;break;case I.CODE39:r=e.data_CODE39;break;case I.CODE49:r=e.data_CODE49;break;case I.CODE93:r=e.data_CODE93;break;case I.CODE128:r=e.data_CODE128;break;case I.PDF417:r=e.data_PDF417;break;case I.DATAMATRIX:r=e.data_DATAMATRIX;break;case I.GS1_128:r=e.data_GS1_128;break;case I.CODABAR:r=e.data_CODABAR}for(a=!1===r.showLabel,c=0,s=Object.keys(r);c<s.length;c++)l=s[c],!a||"labelPosition"!==l&&"font"!==l.substr(0,4)?(f=String(r[l]),void 0===r[l]&&(f=""),/^[0-9]+.?[0-9]*$/.test(f)||null===f||""===f||"true"===f||"false"===f||-1!==f.indexOf("{")||(f='"'+f+'"'),i.push(f)):i.push("");return i.push(e.quietZoneLeft),i.push(e.quietZoneRight),i.push(e.quietZoneTop),i.push(e.quietZoneBottom),o=i.join(","),n=(n=n+("=BC_"+e.barcodeType.toUpperCase())+"(")+o+")"}function s(t,e,n){var i,o,t,t,i={text:"auto",value:"auto"},o={text:"0",value:"0"},t=Array.from({length:t},function(t,e){return{text:(e+1).toString(),value:(e+1).toString()}}),t=u([],t,!0);return n&&t.unshift(o),e&&t.unshift(i),t}function d(t){return t&&"string"==typeof t&&"="===t.toString().charAt(0)}u=this&&this.__spreadArray||function(t,e,n){if(n||2===arguments.length)for(var i=0,o=e.length,u;i<o;i++)!u&&i in e||((u=u||Array.prototype.slice.call(e,0,i))[i]=e[i]);return t.concat(u||Array.prototype.slice.call(e))},Object.defineProperty(v,"__esModule",{value:!0}),v.getItems=v.createBarcodeFormula=v.parseBarCodeOptions=v.getFieldRef=v.getDefaultBarcodeOptions=v.DefaultBarCodeTypeData=v.BarCode_DefaultValue_CODE128=v.BarCode_DefaultValue_CODE93=v.BarCode_DefaultValue_CODE39=v.BarCode_DefaultValue_CODABAR=v.BarCode_DefaultValue_CODE49=v.BarCode_DefaultValue_QRCODE=v.BarCode_DefaultValue_PDF417=v.BarCode_DefaultValue_GS1_128=v.BarCode_DefaultValue_EAN13=v.BarCode_DefaultValue_EAN8=v.BarCode_DefaultValue_DATAMATRIX=v.SANS_SERIF_FONT=v.BarCodeType=void 0,M=e("CalcEngine"),n=e("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-utils.js"),(e=I=v.BarCodeType||(v.BarCodeType={})).QRCODE="QRCODE",e.EAN13="EAN13",e.EAN8="EAN8",e.CODABAR="CODABAR",e.CODE39="CODE39",e.CODE93="CODE93",e.CODE128="CODE128",e.GS1_128="GS1_128",e.CODE49="CODE49",e.PDF417="PDF417",e.DATAMATRIX="DATAMATRIX",v.SANS_SERIF_FONT="sans-serif",v.BarCode_DefaultValue_DATAMATRIX={eccMode:"ECC000",ecc200SymbolSize:"",ecc200EncodingMode:"",ecc00_140Symbol:"",structureAppend:!1,structureNumber:0,fileIdentifier:0},v.BarCode_DefaultValue_EAN8={showLabel:!1,labelPosition:"top",fontFamily:v.SANS_SERIF_FONT,fontStyle:"normal",fontWeight:"normal",fontTextDecoration:"none",fontTextAlign:"center",fontSize:12},v.BarCode_DefaultValue_EAN13={showLabel:!1,labelPosition:"top",addOn:"",addOnLabelPosition:"top",fontFamily:v.SANS_SERIF_FONT,fontStyle:"normal",fontWeight:"normal",fontTextDecoration:"none",fontTextAlign:"center",fontSize:12},v.BarCode_DefaultValue_GS1_128={showLabel:!1,labelPosition:"top",fontFamily:v.SANS_SERIF_FONT,fontStyle:"normal",fontWeight:"normal",fontTextDecoration:"none",fontTextAlign:"center",fontSize:12},v.BarCode_DefaultValue_PDF417={errorCorrectionLevel:"auto",rows:"auto",columns:"auto",compact:!1},v.BarCode_DefaultValue_QRCODE={errorCorrectionLevel:"L",model:2,version:"auto",mask:"auto",connection:!1,connectionNo:0,charCode:"",charset:"UTF-8"},v.BarCode_DefaultValue_CODE49={showLabel:!1,labelPosition:"top",grouping:!1,groupNo:"",fontFamily:v.SANS_SERIF_FONT,fontStyle:"normal",fontWeight:"normal",fontTextDecoration:"none",fontTextAlign:"center",fontSize:12},v.BarCode_DefaultValue_CODABAR={showLabel:!1,labelPosition:"bottom",checkDigit:!1,nwRatio:3,fontFamily:v.SANS_SERIF_FONT,fontStyle:"normal",fontWeight:"normal",fontTextDecoration:"none",fontTextAlign:"center",fontSize:12},v.BarCode_DefaultValue_CODE39={showLabel:!1,labelPosition:"top",labelWithStartAndStopCharacter:!1,checkDigit:!1,nwRatio:3,fullASCII:!1,fontFamily:v.SANS_SERIF_FONT,fontStyle:"normal",fontWeight:"normal",fontTextDecoration:"none",fontTextAlign:"center",fontSize:12},v.BarCode_DefaultValue_CODE93={showLabel:!1,labelPosition:"top",checkDigit:!1,fullASCII:!1,fontFamily:v.SANS_SERIF_FONT,fontStyle:"normal",fontWeight:"normal",fontTextDecoration:"none",fontTextAlign:"center",fontSize:12},v.BarCode_DefaultValue_CODE128={showLabel:!1,labelPosition:"top",codeSet:"auto",fontFamily:v.SANS_SERIF_FONT,fontStyle:"normal",fontWeight:"normal",fontTextDecoration:"none",fontTextAlign:"center",fontSize:12},v.DefaultBarCodeTypeData={QRCODE:v.BarCode_DefaultValue_QRCODE,EAN13:v.BarCode_DefaultValue_EAN13,EAN8:v.BarCode_DefaultValue_EAN8,CODABAR:v.BarCode_DefaultValue_CODABAR,CODE39:v.BarCode_DefaultValue_CODE39,CODE93:v.BarCode_DefaultValue_CODE93,CODE128:v.BarCode_DefaultValue_CODE128,GS1_128:v.BarCode_DefaultValue_GS1_128,CODE49:v.BarCode_DefaultValue_CODE49,PDF417:v.BarCode_DefaultValue_PDF417,DATAMATRIX:v.BarCode_DefaultValue_DATAMATRIX},v.getDefaultBarcodeOptions=i,v.getFieldRef=o,v.parseBarCodeOptions=a,v.createBarcodeFormula=c,v.getItems=s},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-checkbox-cell-type-text.js":function(t,e,n){var i,o,u,r,a,c,s,n,l,f,d,n;function h(t){var e;this.LEt=t}i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var e,n,i,o,n=1,i=arguments.length;n<i;n++)for(o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},Object.defineProperty(e,"__esModule",{value:!0}),e.sto=e.CheckBoxTextAlign=void 0,o=n("Core"),u=n("./dist/plugins/tableSheet/tableSheet.js"),r=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-checkbox.js"),a=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-select.js"),c=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-input.js"),s=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-utils.js"),n=o.mt,l=n.En,(n=f=e.CheckBoxTextAlign||(e.CheckBoxTextAlign={}))[n.top=0]="top",n[n.bottom=1]="bottom",n[n.left=2]="left",n[n.right=3]="right",h.prototype.create=function(){var t,e,t=this,e;return(t.Qze=(0,o.GC$)(l("div"))).addClass("gc-defined-column-checkbox-text-container"),t.ZX()},h.prototype.ZX=function(){var e,t,n,e=this,t=e.Qze,n=e.LEt;return e._isThreeState=new r.Rze({label:{text:(0,u.getSR)().columnTypeCheckboxIsThreeState},onChange:function(t){var t,t,t,t;t?null!=(t=e.lto)&&t.isVisible()||null!=(t=e.lto)&&t.resetVisible():null!=(t=e.lto)&&t.isVisible()&&null!=(t=e.lto)&&t.resetVisible(),e.fto()}}),e._isThreeState.create().appendTo(t),e._isThreeState.setValue(n.isThreeState),e.dto=new c.mZe({inputId:"checkboxTrue",label:{text:(0,u.getSR)().columnTypeCheckboxTrue},placeholder:"",onChange:function(t){e.fto()}}),e.dto.create().appendTo(t),e.dto.setValue(n.checkboxTrue),e.lto=new c.mZe({inputId:"checkboxIndeterminate",label:{text:(0,u.getSR)().columnTypeCheckboxIndeterminate},placeholder:"",hide:!n.isThreeState,onChange:function(t){e.fto()}}),e.lto.create().appendTo(t),e.lto.setValue(n.checkboxIndeterminate),e.hto=new c.mZe({inputId:"checkboxFalse",label:{text:(0,u.getSR)().columnTypeCheckboxFalse},placeholder:"",onChange:function(t){e.fto()}}),e.hto.create().appendTo(t),e.hto.setValue(n.checkboxFalse),e.gto=new a.Kze({inputId:"checkboxAlign",items:[{text:(0,u.getSR)().columnTypeCheckboxTextAlign.top,value:f.top},{text:(0,u.getSR)().columnTypeCheckboxTextAlign.bottom,value:f.bottom},{text:(0,u.getSR)().columnTypeCheckboxTextAlign.left,value:f.left},{text:(0,u.getSR)().columnTypeCheckboxTextAlign.right,value:f.right}],label:{text:(0,u.getSR)().columnTypeCheckboxAlign},onChange:function(){e.fto()}}),e.gto.create().appendTo(t),e.gto.setValue(n.checkboxAlign||f.right),t},h.prototype.fto=function(){var t,t,t,t,t,e,e=this;this.LEt.onChange({isThreeState:null==(t=e._isThreeState)?void 0:t.value,checkboxTrue:null==(t=e.dto)?void 0:t.value,checkboxIndeterminate:null==(t=e.lto)?void 0:t.value,checkboxFalse:null==(t=e.hto)?void 0:t.value,checkboxAlign:null==(t=e.gto)?void 0:t.value})},h.prototype.setOptions=function(t){var e=this;e.LEt=i(i({},e.LEt),t),e.clear(),e.ZX()},h.prototype.clear=function(){var t=this;t.L0(),t.Qze.empty()},h.prototype.dispose=function(){var t=this;t.clear(),t.Qze=s.keyword_null},h.prototype.L0=function(){var t=this;t._isThreeState&&t._isThreeState.dispose(),t._isThreeState=s.keyword_null,t.dto&&t.dto.dispose(),t.dto=s.keyword_null,t.lto&&t.lto.dispose(),t.lto=s.keyword_null,t.hto&&t.hto.dispose(),t.hto=s.keyword_null,t.gto&&t.gto.dispose(),t.gto=s.keyword_null},e.sto=h},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-checkbox-cell-type.js":function(t,e,n){var i,o,u,r,a,c,s,n,l,f;function d(t){var e;this.LEt=t}i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var e,n,i,o,n=1,i=arguments.length;n<i;n++)for(o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},Object.defineProperty(e,"__esModule",{value:!0}),e.Mto=void 0,o=n("Core"),u=n("./dist/plugins/tableSheet/tableSheet.js"),r=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-checkbox.js"),a=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-input-number.js"),c=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-input.js"),s=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-utils.js"),n=o.mt,l=n.En,d.prototype.create=function(){var t,e,t=this,e;return(t.Qze=(0,o.GC$)(l("div"))).addClass("gc-defined-column-column-type-checkbox-container"),t.ZX()},d.prototype.ZX=function(){var e,t,n,e=this,t=e.Qze,n=e.LEt;return e._caption=new c.mZe({inputId:"placeholder",label:{text:(0,u.getSR)().columnTypeCheckboxCaption},placeholder:"",onChange:function(t){e.Ito()}}),e._caption.create().appendTo(t),e._caption.setValue(n.caption),e._boxSize=new a.AZe({inputId:"checkbox-size-limit",minValue:0,label:{text:(0,u.getSR)().columnTypeCheckboxBoxSize},onChange:function(){e.Ito()}}),e._boxSize.create().appendTo(t),e._boxSize.setValue(n.boxSize||12),e.bto=new r.Rze({label:{text:(0,u.getSR)().columnTypeCheckboxAuto},onChange:function(t){t?e._boxSize.isDisable()||e._boxSize.resetDisable():e._boxSize.isDisable()&&e._boxSize.resetDisable(),e.Ito()}}),e.bto.create().appendTo(t),e.bto.setValue(n.autoBoxSize),t},d.prototype.Ito=function(){var t,t,t,e,e=this;this.LEt.onChange({caption:null==(t=e._caption)?void 0:t.value,boxSize:null==(t=e._boxSize)?void 0:t.value,autoBoxSize:null==(t=e.bto)?void 0:t.value})},d.prototype.setOptions=function(t){var e=this;e.LEt=i(i({},e.LEt),t),e.clear(),e.ZX()},d.prototype.clear=function(){var t=this;t.L0(),t.Qze.empty()},d.prototype.dispose=function(){var t=this;t.clear(),t.Qze=s.keyword_null},d.prototype.L0=function(){var t=this;t._caption&&t._caption.dispose(),t._caption=s.keyword_null,t._boxSize&&t._boxSize.dispose(),t._boxSize=s.keyword_null,t.bto&&t.bto.dispose(),t.bto=s.keyword_null},e.Mto=d},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-checkbox.js":function(t,e,n){var u,r,a,i;function o(t){var e;this.LEt=t}Object.defineProperty(e,"__esModule",{value:!0}),e.Rze=void 0,u=n("Core"),r=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-utils.js"),a=u.mt.En,Object.defineProperty(o.prototype,"value",{get:function(){var t;return null==(t=this.weo)?void 0:t.val()},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"container",{get:function(){return this.yZe},enumerable:!1,configurable:!0}),o.prototype.create=function(){var t,e,n,i,o,i,t=this,e=t.LEt,n=t.yZe=(0,u.GC$)(a("div"));return n.addClass("gc-defined-column-field-item-container gc-defined-column-checkbox-field-item-container"),e.label&&e.label.text&&e.textDisplay&&((i=(0,u.GC$)(a("div"))).addClass("gc-defined-column-field-label-container"),i.appendTo(n),(o=(0,u.GC$)(a("label"))).addClass("gc-defined-column-field-label-container-label-text"),o.appendTo(i),e.inputId&&o.attr("for","gc-defined-column-checkbox-"+e.inputId),o.text(e.label.text)),(i=t.Qze=(0,u.GC$)(a("div"))).appendTo(n),i.addClass("gc-defined-column-checkbox-container"),t.ZX(),n},o.prototype.ZX=function(){var e,n,t,i,t,o,o,e=this,n=e.LEt,t=e.Qze,i=e.Pze=(0,u.GC$)(a("div"));i.addClass("gc-defined-column-checkbox-item-container"),e.isDisable()&&i.addClass("gc-defined-column-disabled"),e.isVisible()||t.addClass("gc-defined-column-hide"),i.appendTo(t),i.attr("role","checkbox"),i.attr("tabindex","0"),i.attr("aria-label",n.label.text),i.attr("aria-checked","false"),i.attr("aria-disabled",e.isDisable()?"true":"false"),i.attr("aria-hide",e.isVisible()?"false":"true"),(t=(0,u.GC$)(a("div"))).addClass("gc-defined-column-check-container"),t.appendTo(i),(o=e.weo=(0,r.createDCElement)("sjs-checkbox")).attr("name",""),n.inputId&&o.attr("id","gc-defined-column-checkbox-"+n.inputId),n.isThreeState&&o.attr("indeterminate",!0),(0,r.isNullOrUndefined)(n.defaultValue)||e.setValue(n.defaultValue,!1),o.bind("input",function(t){e.isDisable()||(e.Yze(t.target.value),n.onChange&&n.onChange(t.target.value,t))}),o.appendTo(t),n.label&&n.label.text&&1!==n.textDisplay&&((o=e.LZe=(0,u.GC$)(a("div"))).addClass("gc-defined-column-label-container"),o.appendTo(i),o.text(n.label.text),n.inputId&&o.attr("for","gc-defined-column-checkbox-"+n.inputId),o.bind("click",function(t){e.isDisable()||e.setValue(e.kV(e.weo.val()))}))},o.prototype.Yze=function(t){var e=this,n=t;(0,r.isNullOrUndefined)(t)&&(n=!!e.LEt.isThreeState&&"mixed"),e.Pze.attr("aria-checked",n)},o.prototype.kV=function(t){var e;if(this.LEt.isThreeState){if(t)return r.keyword_null;if((0,r.isNullOrUndefined)(t))return!1}return!t},o.prototype.setValue=function(t,e){var n=this;n.weo.val(t),n.Yze(t),!1!==e&&(0,r.triggerEvent)(n.weo)},o.prototype.isDisable=function(){return!1===this.LEt.enable},o.prototype.resetDisable=function(){var t=this,e=t.isDisable(),n=t.Pze;(t.LEt.enable=e)?(n.removeClass("gc-defined-column-disabled"),n.attr("aria-disabled","false")):(n.addClass("gc-defined-column-disabled"),n.attr("aria-disabled","true"))},o.prototype.isVisible=function(){return!0!==this.LEt.hide},o.prototype.resetVisible=function(){var t=this,e=t.isVisible(),n=t.Qze;(t.LEt.hide=e)?(n.addClass("gc-defined-column-hide"),n.attr("aria-hide","true")):(n.removeClass("gc-defined-column-hide"),n.attr("aria-hide","false"))},o.prototype.dispose=function(){var t=this;t.weo.unbind("input"),t.weo=r.keyword_null,t.LZe&&t.LZe.unbind("click"),t.LZe=r.keyword_null,t.Pze=r.keyword_null,t.Qze=r.keyword_null},e.Rze=o},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-combobox-style.js":function(t,e,n){var i,o,u,r,a,c,s,l,n,f,d;function h(t){var e;this.LEt=t}i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var e,n,i,o,n=1,i=arguments.length;n<i;n++)for(o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},Object.defineProperty(e,"__esModule",{value:!0}),e.vto=void 0,o=n("Core"),u=n("./dist/plugins/tableSheet/tableSheet.js"),r=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-checkbox.js"),a=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-select.js"),c=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-input-number.js"),s=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-utils.js"),l=n("CellTypes"),n=o.mt,f=n.En,h.prototype.create=function(){var t,e,t=this,e;return(t.mto=(0,o.GC$)(f("div"))).addClass("gc-defined-column-combo-box-style-container"),t.ZX()},h.prototype.ZX=function(){var t,e,n,t=this,e=t.mto,n=t.LEt;return t._editorValueType=new a.Kze({inputId:"combo-box-editor-value-type",items:[{text:(0,u.getSR)().columnTypeComboBoxEditorValueType.text,value:l.EditorValueType.text},{text:(0,u.getSR)().columnTypeComboBoxEditorValueType.index,value:l.EditorValueType.index},{text:(0,u.getSR)().columnTypeComboBoxEditorValueType.value,value:l.EditorValueType.value}],label:{text:(0,u.getSR)().columnTypeComboBoxEditorValueTypes},onChange:function(){t.Nto()}}),t._editorValueType.create().appendTo(e),t._editorValueType.setValue((0,s.isNullOrUndefined)(n.editorValueType)?l.EditorValueType.value:n.editorValueType),t.Dto=new c.AZe({inputId:"combo-box-item-height",minValue:0,label:{text:(0,u.getSR)().columnTypeComboBoxItemHeight},onChange:function(){t.Nto()}}),t.Dto.create().appendTo(e),t.Dto.setValue(n.itemHeight||22),t.jto=new r.Rze({label:{text:(0,u.getSR)().columnTypeComboBoxEditable},onChange:function(){t.Nto()}}),t.jto.create().appendTo(e),t.jto.setValue(n.editable),e},h.prototype.Nto=function(){var t,t,t,e,e=this;this.LEt.onChange({editorValueType:null==(t=e._editorValueType)?void 0:t.value,itemHeight:null==(t=e.Dto)?void 0:t.value,editable:null==(t=e.jto)?void 0:t.value})},h.prototype.setOptions=function(t){var e=this;e.LEt=i(i({},e.LEt),t),e.clear(),e.ZX()},h.prototype.clear=function(){var t=this;t.L0(),t.mto.empty()},h.prototype.dispose=function(){var t=this;t.clear(),t.mto=s.keyword_null},h.prototype.L0=function(){var t=this;t._editorValueType&&t._editorValueType.dispose(),t._editorValueType=s.keyword_null,t.Dto&&t.Dto.dispose(),t.Dto=s.keyword_null,t.jto&&t.jto.dispose(),t.jto=s.keyword_null},e.vto=h},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-combobox.js":function(t,e,n){var i,o,u,r,a,n,c,s;function l(t){var e;this.Cto=[],this.LEt=t}i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var e,n,i,o,n=1,i=arguments.length;n<i;n++)for(o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},Object.defineProperty(e,"__esModule",{value:!0}),e.wto=void 0,o=n("Core"),u=n("./dist/plugins/tableSheet/tableSheet.js"),r=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-list.js"),a=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-utils.js"),n=o.mt,c=n.En,l.prototype.create=function(){var t,e,t=this,e;return(t.Tto=(0,o.GC$)(c("div"))).addClass("gc-defined-column-combo-box-container"),t.ZX()},l.prototype.ZX=function(){var t,e,n,t=this,e=t.Tto,n=t.LEt;return this.Cto=n.items||[],t.Ato=new r.Jze({label:{text:(0,u.getSR)().columnTypeComboBoxItems},showValue:!0,editable:!0,addAndRemoveButton:!0,items:this.Cto,onChange:function(){t.yto()}}),t.Ato.create().appendTo(e),t.yto(),e},l.prototype.yto=function(){var t;this.LEt.onChange({items:this.Cto})},l.prototype.setOptions=function(t){var e=this;e.LEt=i(i({},e.LEt),t),e.clear(),e.ZX()},l.prototype.clear=function(){var t=this;t.L0(),t.Tto.empty()},l.prototype.dispose=function(){var t=this;t.clear(),t.Tto=a.keyword_null},l.prototype.L0=function(){var t=this;t.Ato&&t.Ato.dispose(),t.Ato=a.keyword_null},e.wto=l},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-date-formatting.js":function(t,e,n){var i,o,u,f,r,a,d,c,s,l,n,h,g,b,v;function M(t){var e;this.Hze=b.getCultureInfo(1041),this.LEt=t}i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var e,n,i,o,n=1,i=arguments.length;n<i;n++)for(o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},Object.defineProperty(e,"__esModule",{value:!0}),e.Wze=void 0,o=n("Common"),u=n("Core"),f=n("./dist/plugins/tableSheet/tableSheet.js"),r=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-checkbox.js"),a=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-list.js"),d=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-formatting.js"),c=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-preview.js"),s=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-select.js"),l=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-utils.js"),n=u.mt,h=n.En,g="3/14/2001 13:30:00",b=o.Common.CultureManager,M.prototype.create=function(){var t,e,t=this,e;return(t.Vze=(0,u.GC$)(h("div"))).addClass("gc-defined-column-date-formatting-container"),t.ZX()},M.prototype.ZX=function(){var t,e,n,e,t=this,e=t.LEt,n=t.Vze,e=(0,d.parseFormat)(e.formatter);return t.Fze=new a.Jze({label:{text:(0,f.getSR)().columnTypeDateType},items:[],onChange:function(){t.Xze()}}),t.Fze.create().appendTo(n),t.Fze.setIndex(e.index||0),t._ze=new s.Kze({inputId:"date-locale",items:(0,d.getListItems)(4),label:{text:(0,f.getSR)().columnTypeDateLocale},onChange:function(){t.$ze(),t.qze(),t.tZe()}}),t._ze.create().appendTo(n),e.locale?t._ze.setValue(e.locale):t._ze.setIndex(0),t.nZe=new s.Kze({inputId:"date-locale",hide:"ja-jp"!==e.locale,items:[{text:(0,f.getSR)().columnTypeCalendarType.western,value:"western"},{text:(0,f.getSR)().columnTypeCalendarType.JER,value:"JER"}],label:{text:(0,f.getSR)().columnTypeCalendar},onChange:function(){t.qze(),t.tZe()}}),t.nZe.create().appendTo(n),t.nZe.setValue(e.calendarType||"western"),t.iZe=new r.Rze({hide:"JER"!==e.calendarType,label:{text:(0,f.getSR)().columnTypeShowEraFirstYear},onChange:function(){t.tZe()}}),t.iZe.create().appendTo(n),t.iZe.setValue(1===e.numberSystem),t.eZe().appendTo(n),t.tZe(),n},M.prototype.tZe=function(){var t,e,t,e,n,i,o,u,r,a,c,s,l,t,t=null==(t=this._ze)?void 0:t.value,e=null==(e=this.nZe)?void 0:e.value,n=new Date(g),i=[],o=[],u=[];if(t&&e&&(u=(0,d.getPredefinedFormats)(t,d.formatCategories[4])),t===this.Hze.name().toLowerCase()&&"JER"===e)for(r=(0,f.getSR)().columnTypeNumberFormatting.japanEmperorReignDateFormat,a=0;a<r.length;a++)c=this.iZe&&this.iZe.value,1===a&&c?(i.push(this.Re(n,(0,f.getSR)().columnTypeNumberFormatting.japanEmperorReignFirstYearDateFormat[a])),o.push((0,f.getSR)().columnTypeNumberFormatting.japanEmperorReignFirstYearDateFormat[a])):(i.push(this.Re(n,r[a])),o.push(r[a]));else for(a=0;a<u.length;a++)s=this.Re(n,u[a]),i.push(s),o.push(u[a]);for(l=[],a=0;a<i.length;a++)l.push({text:i[a],value:o[a]});t=this.Fze.index,this.Fze.setOptions({items:l}),this.Fze.setIndex(Math.max(t,0))},M.prototype.Xze=function(){var t=this,e=t.oZe();t.LEt.onChange({formatter:e})},M.prototype.$ze=function(){var t,t,t,t,t,e,n,e=this,n;(null==(t=this._ze)?void 0:t.value)===this.Hze.name().toLowerCase()?null!=(t=e.nZe)&&t.isVisible()||null!=(t=e.nZe)&&t.resetVisible():null!=(t=e.nZe)&&t.isVisible()&&null!=(t=e.nZe)&&t.resetVisible()},M.prototype.qze=function(){var t,e,t,e,t,e,t,e,n,t,e,n=this,t=(null==(t=this._ze)?void 0:t.value)===this.Hze.name().toLowerCase(),e="JER"===(null==(e=this.nZe)?void 0:e.value);t?e?null!=(t=n.iZe)&&t.isVisible()||null!=(e=n.iZe)&&e.resetVisible():null!=(t=n.iZe)&&t.isVisible()&&null!=(e=n.iZe)&&e.resetVisible():null!=(t=n.iZe)&&t.isVisible()&&null!=(e=n.iZe)&&e.resetVisible()},M.prototype.oZe=function(){var t,e,n,i,e=this,n=this.Fze.value,i=new Date(g);return null!=(t=e.uZe)&&t.setText(e.Re(i,n)),n},M.prototype.eZe=function(){var t,e,t,e;return(this.uZe=new c.rZe).create()},M.prototype.Re=function(t,e){var n;return new o.Formatter.GeneralFormatter(e).format(t)},M.prototype.setOptions=function(t){var e=this;e.LEt=i(i({},e.LEt),t),e.clear(),e.ZX()},M.prototype.clear=function(){var t=this;t.L0(),t.Vze.empty()},M.prototype.dispose=function(){var t=this;t.clear(),t.Vze=l.keyword_null},M.prototype.L0=function(){var t=this;t.Fze&&t.Fze.dispose(),t.Fze=l.keyword_null,t._ze&&t._ze.dispose(),t._ze=l.keyword_null,t.nZe&&t.nZe.dispose(),t.nZe=l.keyword_null,t.iZe&&t.iZe.dispose(),t.iZe=l.keyword_null,t.uZe.dispose(),t.uZe=l.keyword_null},e.Wze=M},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-date-time-picker.js":function(t,e,n){var i,o,u,r,a,c,n,s,l;function f(t){var e;this.LEt=t}i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var e,n,i,o,n=1,i=arguments.length;n<i;n++)for(o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},Object.defineProperty(e,"__esModule",{value:!0}),e.cZe=void 0,o=n("Core"),u=n("./dist/plugins/tableSheet/tableSheet.js"),r=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-checkbox.js"),a=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-select.js"),c=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-utils.js"),n=o.mt,s=n.En,f.prototype.create=function(){var t,e,t=this,e;return(t.aZe=(0,o.GC$)(s("div"))).addClass("gc-defined-column-date-time-picker-container"),t.ZX()},f.prototype.ZX=function(){var t,e,n,t=this,e=t.LEt,n=t.aZe;return t.dH=new a.Kze({inputId:"start-day",items:[{value:o.CalendarStartDay[o.CalendarStartDay.monday],text:(0,u.getSR)().columnTypeDateMonday},{value:o.CalendarStartDay[o.CalendarStartDay.tuesday],text:(0,u.getSR)().columnTypeDateTuesday},{value:o.CalendarStartDay[o.CalendarStartDay.wednesday],text:(0,u.getSR)().columnTypeDateWednesday},{value:o.CalendarStartDay[o.CalendarStartDay.thursday],text:(0,u.getSR)().columnTypeDateThursday},{value:o.CalendarStartDay[o.CalendarStartDay.friday],text:(0,u.getSR)().columnTypeDateFriday},{value:o.CalendarStartDay[o.CalendarStartDay.saturday],text:(0,u.getSR)().columnTypeDateSaturday},{value:o.CalendarStartDay[o.CalendarStartDay.sunday],text:(0,u.getSR)().columnTypeDateSunday}],label:{text:(0,u.getSR)().columnTypeDateStartDay},onChange:function(){t.sZe()}}),t.dH.create().appendTo(n),t.dH.setValue(e.startDay||o.CalendarStartDay[o.CalendarStartDay.sunday]),t.lZe=new a.Kze({inputId:"calendar-page",items:[{value:o.CalendarPage[o.CalendarPage.day],text:(0,u.getSR)().columnTypeDateDay},{value:o.CalendarPage[o.CalendarPage.year],text:(0,u.getSR)().columnTypeDateYear},{value:o.CalendarPage[o.CalendarPage.month],text:(0,u.getSR)().columnTypeDateMonth}],label:{text:(0,u.getSR)().columnTypeDateCalendarPage},onChange:function(){t.sZe()}}),t.lZe.create().appendTo(n),t.lZe.setValue(e.calendarPage||o.CalendarPage[o.CalendarPage.day]),t.HG=new r.Rze({label:{text:(0,u.getSR)().columnTypeDateShowTime},onChange:function(){t.sZe()}}),t.HG.create().appendTo(n),t.HG.setValue(!!e.showTime||(0,c.isNullOrUndefined)(e.showTime)),n},f.prototype.veo=function(t){var t,t,t,t,t,t,t,t,e,e=this;t?(null!=(t=e.lZe)&&t.isDisable()||null!=(t=e.lZe)&&t.resetDisable(),null!=(t=e.HG)&&t.isDisable()||null!=(t=e.HG)&&t.resetDisable()):(null!=(t=e.lZe)&&t.isDisable()&&null!=(t=e.lZe)&&t.resetDisable(),null!=(t=e.HG)&&t.isDisable()&&null!=(t=e.HG)&&t.resetDisable())},f.prototype.sZe=function(){var t,t,t;this.LEt.onChange({startDay:null==(t=this.dH)?void 0:t.value,calendarPage:null==(t=this.lZe)?void 0:t.value,showTime:!(null==(t=this.HG)||!t.value)})},f.prototype.setOptions=function(t){var e=this;e.LEt=i(i({},e.LEt),t),e.clear(),e.ZX()},f.prototype.clear=function(){var t=this;t.L0(),t.aZe.empty()},f.prototype.dispose=function(){var t=this;t.clear(),t.aZe=c.keyword_null},f.prototype.L0=function(){var t=this;t.dH&&t.dH.dispose(),t.dH=c.keyword_null,t.lZe&&t.lZe.dispose(),t.lZe=c.keyword_null,t.HG&&t.HG.dispose(),t.HG=c.keyword_null},e.cZe=f},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-divider.js":function(t,e,n){var i,o,n,u,r;function a(t){var e;this.LEt=t}Object.defineProperty(e,"__esModule",{value:!0}),e.dZe=void 0,i=n("Core"),o=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-utils.js"),n=i.mt,u=n.En,a.prototype.create=function(){var t;return this.gZe()},a.prototype.gZe=function(){var t,e,n,t=this,e=(0,i.GC$)(u("div"));return e.addClass("gc-defined-column-divider-container"),e.attr("role","separator"),(n=t._text=(0,i.GC$)(u("div"))).appendTo(e),n.addClass("gc-defined-column-divider-inner-text"),t.LEt.text?n.text(t.LEt.text):n.hide(),e},a.prototype.setText=function(t){var e;this._text.text(t)},a.prototype.dispose=function(){var t;this._text=o.keyword_null},e.dZe=a},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-formatting-select.js":function(t,e,n){var r,a,i,c,s,l,f,n,d,o;function u(t){var e;this.xT=[],this.LEt=t}Object.defineProperty(e,"__esModule",{value:!0}),e.MZe=void 0,r=n("Core"),a=n("./dist/plugins/tableSheet/tableSheet.js"),i=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-date-formatting.js"),c=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-formatting.js"),s=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-number-formatting.js"),l=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-select.js"),f=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-utils.js"),n=r.mt,d=n.En,u.prototype.create=function(){var t,e,t,e=this.hZe=(0,r.GC$)(d("div"));return e.addClass("gc-defined-column-formatting-select-container"),e},u.prototype.IZe=function(){var e,t,n,i,o,u,n,e=this,t=e.LEt.formatter,n=(0,c.parseFormat)(t),i=(0,r.GC$)(d("div"));return i.addClass("gc-defined-column-formatting-select-number-container"),o=new s.bZe({onChange:function(t){e.LEt.onChange(t)}}),(u=new l.Kze({inputId:"lookup-number-formatting",items:[{text:(0,a.getSR)().columnTypeNumber,value:1},{text:(0,a.getSR)().columnTypeCurrency,value:2},{text:(0,a.getSR)().columnTypePercent,value:6}],label:{text:(0,a.getSR)().columnTypeFormattingCategory},onChange:function(t){o.setOptions({type:t.value,formatter:f.keyword_null})}})).create().appendTo(i),e.xT.push(u),o.create().appendTo(i),e.xT.push(o),n=n.category,-1===[1,2,6].indexOf(n)&&(n=1),u.setValue(n),o.setOptions({type:n,formatter:t}),i},u.prototype.NZe=function(){var e,t,n,t,e=this,t=e.LEt.formatter,n=(0,r.GC$)(d("div"));return n.addClass("gc-defined-column-formatting-select-date-container"),(t=new i.Wze({formatter:t,onChange:function(t){e.LEt.onChange(t.formatter)}})).create().appendTo(n),e.xT.push(t),n},u.prototype.createFormatting=function(t){var e=this,n=e.hZe;if(e.L0(),(0,f.isNumber)(t))e.IZe().appendTo(n);else{if(!(0,f.isDate)(t))return!1;e.NZe().appendTo(n)}return!0},u.prototype.dispose=function(){var t=this;t.L0(),t.hZe=f.keyword_null},u.prototype.L0=function(){for(var t,e,n,i,t=this,e=0,n=t.xT;e<n.length;e++)(i=n[e]).dispose?i.dispose():i.unbind&&i.unbind("click");t.xT=[],t.hZe.empty()},e.MZe=u},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-formatting.js":function(t,e,n){var c,s,l,a,o,f,i,d,u,r;function h(t){var t,t,t=t.match(o);if(t&&t[0])return t=t[0],-1!==u.indexOf(t)?-1:parseInt(t.split("-")[1],16)}function g(t){return t.replace(o,function(t,e){var n="[$";return 0<e.length&&"-"!==e[0]&&(n+="$"),n+="-409]"})}function b(t,n){var i,i;return t&&(i=(i=t.split(";")).map(function(t,e){return 4===i.length&&2===e?t.replace(new RegExp("\\?{"+n+"}"),""):t.replace(a,"")})).join(";")}function v(u){var r,t,e,a,r=c.Common.CultureManager.getCultureInfoDict(),t=(0,s.getSR)().columnTypeNumberFormatting.japanEmperorReignDateFormat,e=(0,s.getSR)().columnTypeNumberFormatting.japanEmperorReignFirstYearDateFormat,a={category:4,index:-1};if(u=u.replace(d,""),-1<e.indexOf(u)?(a.index=e.indexOf(u),a.locale="ja-jp",a.calendarType="JER",1===a.index&&(a.numberSystem=1)):-1<t.indexOf(u)?(a.index=t.indexOf(u),a.locale="ja-jp",a.calendarType="JER"):Object.keys(r).forEach(function(t){var e,n,i,o,i;if(!(-1<a.index)&&"invariant"!==t){if(n=u,!r[e=t].predefinedFormats||!r[t].predefinedFormats.Date){if(i=h(u),!(0,l.isNullOrUndefined)(i)&&i!==r[t].id)return;e=f,n=g(u)}-1<(i=(o=r[e].predefinedFormats.Date).indexOf(n))&&(a.index=i,a.locale=t,a.calendarType="western")}}),-1<a.index)return a}function M(u){var r,a,r=c.Common.CultureManager.getCultureInfoDict(),a={category:5,index:-1};if(Object.keys(r).forEach(function(t){var e,n,i,o,i;if(!(-1<a.index)&&"invariant"!==t){if(n=u,!r[e=t].predefinedFormats||!r[t].predefinedFormats.Time){if(i=h(u),!(0,l.isNullOrUndefined)(i)&&i!==r[t].id)return;e=f,n=g(u)}-1<(i=(o=r[e].predefinedFormats.Time).indexOf(n))&&(a.index=i,a.locale=t)}}),0<=a.index)return a}function I(t,o){var u,r,a,u=c.Common.CultureManager.getCultureInfoDict(),r={category:3,index:-1};if(t&&(a=b(t,o).replace(d,""),Object.keys(u).forEach(function(t,e){var n,i;if(!(-1<r.index)&&"invariant"!==t){if(!u[n=t].predefinedFormats||!u[t].predefinedFormats.Accounting&&!u[n].predefinedFormats.Comma){if(i=h(a),!(0,l.isNullOrUndefined)(i)&&i!==u[t].id)return;n=f,a=g(a)}b(u[n].predefinedFormats.Accounting,2)===a?(r.decimalPlaces=o,r.locale=t,r.index=e):b(u[n].predefinedFormats.Comma,2)===a&&(r.decimalPlaces=o,r.locale=(0,s.getSR)().columnTypeNumberFormatting.accountingSymbol[0][0],r.index=e)}})),-1<r.index)return r}function m(t,e){var n,i,o;if(t&&(n=(0,s.getSR)().columnTypeNumberFormatting.percentageFormats,i=null,i=t,0!==e&&(o="\\"+t.substring(t.indexOf("."),t.indexOf(".")+e+1),i=t.replace(new RegExp(o,"g"),"")),i===n[0]))return{decimalPlaces:e,category:6,index:0}}function N(t,e){var n,i,o,u,r,n={category:1,index:-1};if(t)for(i=(0,s.getSR)().columnTypeNumberFormatting.numberCategoryFormats,o=t,0!==e&&(u="\\"+t.substring(t.indexOf("."),t.indexOf(".")+e+1),o=t.replace(new RegExp(u,"g"),"")),o=o.replace(d,""),r=0;r<i.length;r++)if(o===i[r])return n.decimalPlaces=e,-1!==t.indexOf("#,")&&(n.useThousand=!0),n.index=r%(i.length/2),n}function D(t,i){var o,u,r,o=c.Common.CultureManager.getCultureInfoDict(),u={category:2,index:-1},r=t;if(t&&(r=t.replace(a,"").replace(d,""),-1<(0,s.getSR)().columnTypeNumberFormatting.currencyFormatWithoutSymbol.indexOf(r)?(u.decimalPlaces=i,u.locale=(0,s.getSR)().columnTypeNumberFormatting.accountingSymbol[0][0],u.index=(0,s.getSR)().columnTypeNumberFormatting.currencyFormatWithoutSymbol.indexOf(r)):Object.keys(o).forEach(function(t){var e,n,n;if(!(-1<u.index)&&"invariant"!==t){if(e=o[t].predefinedFormats.Currency,!o[t].predefinedFormats||!o[t].predefinedFormats.Currency){if(n=h(r),!(0,l.isNullOrUndefined)(n)&&n!==o[t].id)return;r=g(r),e=o[f].predefinedFormats.Currency}e&&0<e.length&&(-1<u.index||0<=(n=(e=e.map(function(t){return t.replace(a,"")})).indexOf(r))&&(u.decimalPlaces=i,u.locale=t,u.index=n))}}),-1<u.index))return u}function j(t,a){var e,n,c,e="\u200e",n=a.id,c=r.hasOwnProperty(n);return t.replace(o,function(t,e,n,i,o,u){var r="[$";return 0<i.length&&"$"===i[0]&&(r+=a.NumberFormat.currencySymbol||"$"),r+=(c?"\u200e":"")+"-"+a.id.toString(16)+"]",c?e+n+o+r+u:e+n+r+o+u})}function C(t,e){var n,t,i,i,o,u,n=c.Common.CultureManager.getCultureInfo(t);if(n)return!(i=(t=n.predefinedFormats&&n.predefinedFormats[e])instanceof Array)&&t||i&&0<t.length?t:(i=c.Common.CultureManager.culture(),(t=(u=(o=c.Common.CultureManager.getCultureInfo(i)).predefinedFormats)[e]||(0,s.getSR)().columnTypeNumberFormatting.commonFormats[e]&&(0,s.getSR)().columnTypeNumberFormatting.commonFormats[e].format)instanceof Array?t.map(function(t){var e,e;return(e=(e=t.split(";")).map(function(t){return j(t,n)})).join(";")}):"object"==typeof t?{}:t.split(";").map(function(t){return j(t,n)}).join(";"))}function w(t){var e,n,i,e="";if(t)for(n=0;n<t.length;n++)t[n]&&(i=t[n].match(o))&&i[0]&&(e=i[0],t[n]=t[n].replace(o,"[$]"));return e}function p(t,e){var n,i;if(e)for(n=0;n<t.length;n++)t[n]&&-1<(i=t[n].indexOf("[$]"))&&(t[n]=t[n].slice(0,i)+e+t[n].slice(i+3))}function x(n,t){var i,o,u,i=c.Common.CultureManager.getCultureInfoDict(),o=[];return t&&(o=o.concat(t)),u=c.Common.CultureManager.culture(),Object.keys(i).sort(function(t,e){return t===u?-1:1}).forEach(function(t){var e;"invariant"!==t&&(e=i[t],2===n||3===n?o.push({text:e.NumberFormat.currencySymbol+" "+e.displayName,value:t}):o.push({text:e.displayName,value:t}))}),o}function y(t){var e,n,i,o;if(!t||"General"===t)return{category:0,index:0};if("@"===t)return{category:9,index:0};if(e=v(t)||M(t))return e;if(n=0,t&&-1!==t.indexOf("."))for(o=(i=t.indexOf("."))+1;o<t.length&&"0"===t.substring(o,o+1);o++)n++;return(e=I(t,n)||m(t,n)||N(t,n)||D(t,n))||{category:11,index:0}}Object.defineProperty(e,"__esModule",{value:!0}),e.parseFormat=e.getListItems=e.afterAdjustFormatString=e.beforeAdjustFormatString=e.getPredefinedFormats=e.formatCategories=e.decimalPlacesWithZeroDotReg=void 0,c=n("Common"),s=n("./dist/plugins/tableSheet/tableSheet.js"),l=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-utils.js"),a=/\.0*/g,e.decimalPlacesWithZeroDotReg=/(0)(\.?(0*)?)/g,o=/\[\$(.?-.*?)\]/g,f="en-us",d=new RegExp(i="\\\\","g"),u=["[$\u20ac-1]","[$\u20ac-2]"],e.formatCategories=["General","Number","Currency","Accounting","Date","Time","Percentage","Fraction","Scientific","Text","Special","Custom"],r={4097:!0,14337:!0,15361:!0,5121:!0,3073:!0,2049:!0,11265:!0,13313:!0,12289:!0,6145:!0,8193:!0,17409:!0,16385:!0,1025:!0,10241:!0,7169:!0,9217:!0,1170:!0},e.getPredefinedFormats=C,e.beforeAdjustFormatString=w,e.afterAdjustFormatString=p,e.getListItems=x,e.parseFormat=y},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-input-formula.js":function(t,e,n){var o,u,r,i,a,n,c,s;function l(t){var e;this.LEt=t}Object.defineProperty(e,"__esModule",{value:!0}),e.vZe=void 0,o=n("Core"),u=n("FormulaTextBox"),r=n("./dist/plugins/tableSheet/tableSheet.js"),i=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-input.js"),a=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-utils.js"),n=o.mt,c=n.En,l.prototype.create=function(){var t;return this.ZX()},l.prototype.ZX=function(){var t,e,n,t,t=this,e=(0,o.GC$)(c("div"));return e.addClass("gc-defined-column-input-formula-container"),(n=t.DZe=new i.mZe({inputId:"formula",type:"text",validate:t.LEt.validate,placeholder:(0,r.getSR)().defineColumnFormula})).create().appendTo(e),t.jZe(),(t=t.CZe=(0,o.GC$)(c("div"))).addClass("gc-defined-column-fake-ftb-workbook"),t.hide(),t.appendTo(e),e},l.prototype.jZe=function(){var e=this,n={formulaInputTextBox:null,fakeFormulaTextBoxWorkbook:null};e.DZe.bind("focus",function(){e.ggi(n.formulaInputTextBox,n.fakeFormulaTextBoxWorkbook);var t=e.wZe();n.formulaInputTextBox=t.formulaInputTextBox,n.fakeFormulaTextBoxWorkbook=t.fakeFormulaTextBoxWorkbook}).bind("blur",function(){var t=e.DZe.value;"="===t&&e.DZe.setValue(t="",!1),e.LEt.onChange(t),e.ggi(n.formulaInputTextBox,n.fakeFormulaTextBoxWorkbook)}).bind("keydown",function(t){13===t.keyCode&&(e.ggi(n.formulaInputTextBox,n.fakeFormulaTextBoxWorkbook),t.target.blur())})},l.prototype.TZe=function(){var t;this.DZe.unbind("focus").unbind("blur").unbind("keydown")},l.prototype.wZe=function(){var t,e,n,e,i,t=this,e=t.DZe,n=e.value||"=",e=new u.FormulaTextBox(e.getInputElement(),a.keyword_null,a.keyword_null);return e.Ngi=!0,i=new o.Workbook(t.CZe.get(0),{allowDynamicArray:!0,allowUndo:!1}),t.XMi(i),t.lgi(i),e.workbook(i),e.text(n),{formulaInputTextBox:e,fakeFormulaTextBoxWorkbook:i}},l.prototype.XMi=function(t){for(var e,n,i,o,u,e,n,i=0,o=this.LEt.getColumnFields();i<o.length;i++)u=o[i],t.zwt(t.hyt,u,"=A1",0,0,!0,(0,r.getSR)().GroupPanelDropDownCalcField+" "+u)},l.prototype.lgi=function(t){var t=t.getActiveSheet();delete t.Qy().PUt.enter,t.setRowCount(1),t.setColumnCount(1)},l.prototype.ggi=function(t,e){t&&t.destroy&&t.destroy(),e&&e.destroy&&e.destroy()},l.prototype.validate=function(){return this.DZe.validate()},l.prototype.setValue=function(t){var e;this.DZe.setValue(t)},l.prototype.dispose=function(){var t=this;t.TZe(),t.DZe.dispose(),t.DZe=a.keyword_null},e.vZe=l},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-input-number.js":function(t,e,n){var i,u,r,a,o;function c(t){var e;this.LEt=t}i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var e,n,i,o,n=1,i=arguments.length;n<i;n++)for(o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},Object.defineProperty(e,"__esModule",{value:!0}),e.AZe=void 0,u=n("Core"),r=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-utils.js"),a=u.mt.En,Object.defineProperty(c.prototype,"value",{get:function(){var t;return null==(t=this.DZe)?void 0:t.val()},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"container",{get:function(){return this.yZe},enumerable:!1,configurable:!0}),c.prototype.create=function(){var t,e,n,i,o,i,e,t=this,e=t.LEt,n=t.yZe=(0,u.GC$)(a("div"));return n.addClass("gc-defined-column-field-item-container gc-defined-column-input-number"),e.label&&e.label.text&&((i=(0,u.GC$)(a("div"))).addClass("gc-defined-column-field-label-container"),i.appendTo(n),(o=(0,u.GC$)(a("label"))).addClass("gc-defined-column-field-label-container-label-text"),o.attr("for","gc-defined-column-input-"+e.inputId),o.appendTo(i),i=e.label.useRawContent?e.label.text:(0,r.j0)(e.label.text),o.html(i)),(e=t.zZe=(0,u.GC$)(a("div"))).addClass("gc-defined-column-field-container"),e.appendTo(n),t.ZX(),n},c.prototype.ZX=function(){var n,i,t,e,t,n=this,i=n.LEt;!1===i.visible?n.yZe.hide():n.yZe.show(),t=n.zZe,n.isDisable()&&t.addClass("gc-defined-column-disabled"),(e=(0,u.GC$)(a("div"))).addClass("gc-defined-column-field-input-container"),e.appendTo(t),(t=n.DZe=(0,r.createDCElement)("sjs-number-editor")).attr("id","gc-defined-column-input-"+i.inputId),t.attr("type","text"),t.attr("aria-disabled",n.isDisable()?"true":"false"),n.isDisable()?t.attr("disabled","true"):t.removeAttr("disabled"),i.placeholder&&t.attr("placeholder",i.placeholder),(i.label&&i.label.text||i.placeholder)&&t.attr("aria-label",i.label&&i.label.text||i.placeholder),(0,r.isNullOrUndefined)(i.defaultValue)||t.val(i.defaultValue),t.attr("min",n.yeo()),(0,r.isNumber)(i.maxValue)&&t.attr("max",i.maxValue),i.allowMinNull&&(t.attr("allow-empty",!0),t.attr("empty-value",n.yeo())),t.bind("input",function(t){var e=t.target.value;n.isDisable()||i.onChange&&i.onChange(e,t)}),t.appendTo(e)},c.prototype.setFocus=function(){var t;this.DZe.focus()},c.prototype.setOptions=function(t){var e=this;e.L0(),e.LEt=i(i({},e.LEt),t),e.ZX()},c.prototype.isDisable=function(){return!1===this.LEt.enable},c.prototype.resetDisable=function(){var t=this,e=t.isDisable(),n=t.zZe;(t.LEt.enable=e)?n.removeClass("gc-defined-column-disabled"):n.addClass("gc-defined-column-disabled"),t.DZe.attr("aria-disabled",e?"false":"true"),e?t.DZe.removeAttr("disabled"):t.DZe.attr("disabled","true")},c.prototype.yeo=function(){var t=this;return(0,r.isNumber)(t.LEt.minValue)?t.LEt.minValue:0},c.prototype.setValue=function(t){var e=this;e.DZe.val((0,r.isNumber)(t)?t:e.yeo()),(0,r.triggerEvent)(e.DZe,"input")},c.prototype.dispose=function(){var t=this;t.L0(),t.zZe=r.keyword_null,t.yZe=r.keyword_null},c.prototype.L0=function(){var t=this;t.DZe.unbind("input"),t.DZe=r.keyword_null,t.zZe.empty()},e.AZe=c},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-input.js":function(t,e,n){var a,c,u,s,l,i;function o(t){var e;this.LEt=t}Object.defineProperty(e,"__esModule",{value:!0}),e.mZe=void 0,a=n("Core"),c=n("DataManager"),u=n("./dist/plugins/tableSheet/tableSheet.js"),s=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-utils.js"),l=a.mt.En,Object.defineProperty(o.prototype,"value",{get:function(){var t;return null==(t=this.DZe)?void 0:t.val()},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"container",{get:function(){return this.yZe},enumerable:!1,configurable:!0}),o.prototype.create=function(){var t,e,n,i,o,i,o,t=this,e=t.LEt,n=t.yZe=(0,a.GC$)(l("div"));return n.addClass("gc-defined-column-field-item-container gc-defined-column-input"+(e.className?" "+e.className:"")),e.label&&e.label.text&&((i=(0,a.GC$)(l("div"))).addClass("gc-defined-column-field-label-container"),i.appendTo(n),(o=(0,a.GC$)(l("label"))).addClass("gc-defined-column-field-label-container-label-text"),o.attr("for","gc-defined-column-input-"+e.inputId),o.appendTo(i),o.text(e.label.text)),(i=t.zZe=(0,a.GC$)(l("div"))).addClass("gc-defined-column-field-container"),i.appendTo(n),t.isVisible()||n.addClass("gc-defined-column-hide"),n.attr("aria-hide",t.isVisible()?"false":"true"),t.ZX(),(o=(0,a.GC$)(l("div"))).addClass("gc-defined-column-field-label-validation-container"),o.appendTo(n),n},o.prototype.ZX=function(){var e,n,t,i,o,u,r,e=this,n=e.LEt,t=e.zZe;e.isDisable()&&t.addClass("gc-defined-column-disabled"),e.SZe()&&(t.addClass("gc-defined-column-click"),t.bind("click",function(t){if(!e.isDisable())return n.onClick(t),!1})),n.label&&((i=e.LZe=(0,a.GC$)(l("div"))).addClass("gc-defined-column-field-label-container"),(0,s.isFunction)(n.label.onClick)&&i.bind("click",function(t){if(!e.isDisable())return n.label.onClick(t),!1}),i.appendTo(t),(o=(0,a.GC$)(l("label"))).addClass("gc-defined-column-field-label-container-label "+n.label.iconClass),o.attr("for","gc-defined-column-input-"+n.inputId),o.appendTo(i),(0,c.isNullOrEmpty)(n.label.iconClass))&&o.hide(),(u=(0,a.GC$)(l("div"))).addClass("gc-defined-column-field-input-container"),u.appendTo(t),"color"===n.type?n.tag="sjs-color-picker":(0,s.isNullOrUndefined)(n.type)&&(n.tag="sjs-text-editor"),r=e.DZe=n.tag?(0,s.createDCElement)(n.tag):(0,a.GC$)(l("input")),n.tag||r.addClass("gc-defined-column-field-input-container-input"),r.attr("id","gc-defined-column-input-"+n.inputId),n.tag||r.attr("type",n.type||"text"),r.attr("aria-disabled",e.isDisable()?"true":"false"),e.isDisable()?r.attr("disabled","true"):r.removeAttr("disabled"),n.placeholder&&r.attr("placeholder",n.placeholder),(n.label&&n.label.text||n.placeholder)&&r.attr("aria-label",n.label&&n.label.text||n.placeholder),(0,s.isNullOrUndefined)(n.defaultValue)||(r.attr("value",n.defaultValue),r.val(n.defaultValue)),n.isReadOnly&&r.attr("readonly","true"),n.maxLength&&r.attr("maxlength",n.maxLength),e.v7(),r.appendTo(u),n.label&&n.label.suffixIconClass&&((i=e.kZe=(0,a.GC$)(l("div"))).addClass("gc-defined-column-field-label-container"),(0,s.isFunction)(n.label.onSuffixClick)&&(i.addClass("gc-defined-column-click-quieter"),i.bind("click",function(t){if(!e.isDisable())return n.label.onSuffixClick(t),!1})),i.appendTo(t),(o=(0,a.GC$)(l("label"))).addClass("gc-defined-column-field-label-container-label "+n.label.suffixIconClass),o.appendTo(i))},o.prototype.v7=function(){var n,i,t,e,n=this,i=n.LEt,t=n.DZe,e=function(e){return function(t){n.isValid()||n.resetValid(n.validate()),n.isDisable()||(i.maxLength&&t.target.value&&i.maxLength<t.target.value.length?n.setValue(t.target.value.slice(0,i.maxLength)):e&&e(t))}};n.OZe()&&t.bind("blur",e(function(t){i.onBlur&&i.onBlur(t.target.value,t)})),n.ZZe()&&t.bind("input",e(function(t){i.onChange(t.target.value,t)}))},o.prototype.setFocus=function(){var t;this.DZe.focus()},o.prototype.setOptions=function(t){var e=this;e.L0(),e.LEt=t,e.ZX()},o.prototype.isDisable=function(){return!1===this.LEt.enable},o.prototype.resetDisable=function(){var t=this,e=t.isDisable(),n=t.zZe;(t.LEt.enable=e)?n.removeClass("gc-defined-column-disabled"):n.addClass("gc-defined-column-disabled"),t.DZe.attr("aria-disabled",e?"false":"true"),e?t.DZe.removeAttr("disabled"):t.DZe.attr("disabled","true")},o.prototype.validate=function(){var t,e,n,n,t=this,e=t.LEt.validate,n=!t.isDisable();if(e&&n){if(e.required&&(0,c.isNullOrEmpty)(t.value))return t.resetValid(!1,{required:!1}),!1;if(e.validating)if("boolean"==typeof(n=e.validating(t.value))){if(!n)return t.resetValid(!1),!1}else if(!n.success)return t.resetValid(!1,n),!1}return t.resetValid(!0),!0},o.prototype.resetValid=function(t,e){var n,i,o,n,n=this,i=n.DZe,o=n.zZe,n=(0,a.GC$)(n.yZe.find(".gc-defined-column-field-label-validation-container")[0]);t?(o.removeClass("gc-define-column-invalid"),i.removeClass("gc-define-column-invalid"),i.attr("aria-invalid","false"),n.hide(),n.empty()):o.hasClass("gc-define-column-invalid")||(o.addClass("gc-define-column-invalid"),i.addClass("gc-define-column-invalid"),i.attr("aria-invalid","true"),!1===e.required?n.html((0,u.getSR)().defineColumnRequired):e.errorMessage&&n.html(e.errorMessage),e&&n.show())},o.prototype.isValid=function(){var t,e;return!this.zZe.hasClass("gc-define-column-invalid")},o.prototype.OZe=function(){var t=this,e=t.LEt;return!e.isReadOnly&&!t.isDisable()&&e.validate&&(e.validate.required||(0,s.isFunction)(e.validate.validating))},o.prototype.isVisible=function(){return!0!==this.LEt.hide},o.prototype.resetVisible=function(){var t=this,e=t.isVisible(),n=t.yZe;(t.LEt.hide=e)?n.addClass("gc-defined-column-hide"):n.removeClass("gc-defined-column-hide"),t.DZe.attr("aria-hide",e?"true":"false")},o.prototype.ZZe=function(){var t,e=this.LEt;return!e.isReadOnly&&(0,s.isFunction)(e.onChange)},o.prototype.SZe=function(){var t,e=this.LEt;return e.isReadOnly&&(0,s.isFunction)(e.onClick)},o.prototype.setValue=function(t,e){var n=this;n.DZe.val(t),!1!==e&&(0,s.triggerEvent)(n.DZe,"input")},o.prototype.getInputElement=function(){return(0,s.getSJSInput)(this.DZe)},o.prototype.bind=function(t,e){return this.DZe.bind(t,e),this},o.prototype.unbind=function(t){return this.DZe.unbind(t),this},o.prototype.dispose=function(){var t=this;t.L0(),t.zZe=s.keyword_null},o.prototype.L0=function(){var t=this;t.ZZe()&&t.DZe.unbind("input"),t.OZe()&&t.DZe.unbind("blur"),"sjs-text-editor"===t.LEt.tag&&t.DZe.unbind("sjs-mounted"),t.SZe()&&(t.zZe.removeClass("gc-defined-column-click"),t.zZe.unbind("click")),t.LZe&&(0,s.isFunction)(t.LEt.label.onClick)&&t.LZe.unbind("click"),t.kZe&&(0,s.isFunction)(t.LEt.label.onSuffixClick)&&t.kZe.unbind("click"),t.LZe=s.keyword_null,t.kZe=s.keyword_null,t.DZe=s.keyword_null,t.zZe.empty()},e.mZe=o},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-list.js":function(t,e,n){var i,s,l,a,c,n,f,o;function u(t){this.itemContainers=[];var e=this;e.LEt=t,e.$G=c.keyword_null,e.EZe=[]}i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var e,n,i,o,n=1,i=arguments.length;n<i;n++)for(o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},Object.defineProperty(e,"__esModule",{value:!0}),e.Jze=void 0,s=n("Core"),l=n("DataManager"),a=n("./dist/plugins/tableSheet/tableSheet.js"),c=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-utils.js"),n=s.mt,f=n.En,Object.defineProperty(u.prototype,"value",{get:function(){var t,e,e=this;return e.LEt.enableMultiSelected?e.EZe.map(function(t){var t;return null==(t=e.LEt.items[t])?void 0:t.value}):null==(t=e.LEt.items[e.$G])?void 0:t.value},enumerable:!1,configurable:!0}),Object.defineProperty(u.prototype,"index",{get:function(){var t=this;return t.LEt.enableMultiSelected?t.EZe.slice():t.$G},enumerable:!1,configurable:!0}),u.prototype.create=function(){var t,e,n,i,o,u,t=this,e=t.LEt.label,n=t.LEt.addAndRemoveButton,i=(0,s.GC$)(f("div"));return i.addClass("gc-defined-column-list-container"),(o=(0,s.GC$)(f("div"))).addClass("gc-defined-column-list-label-container"),o.appendTo(i),e&&e.text&&((u=(0,s.GC$)(f("div"))).addClass("gc-defined-column-label-container"),u.appendTo(o),u.text(e.text)),n&&t.zto(o),t.zqt().appendTo(i),i},u.prototype.zqt=function(){var t,e,t=this,e=t.UZe=(0,s.GC$)(f("div"));return e.addClass("gc-defined-column-list-items-container"),t.isDisable()&&e.addClass("gc-defined-column-disabled"),e.attr("role","listbox"),e.attr("tabindex","-1"),e.attr("aria-disabled",t.isDisable()?"true":"false"),t.tyn(),e},u.prototype.tyn=function(){var i,o,e,n,u,r,a,t,c,i=this,o=i.LEt.items,e=i.LEt.enableMultiSelected,n=i.EZe,u=i.$G,r=i.LEt.showValue,a=function(t){return e?-1<n.indexOf(t):u===t};for(r&&i.meo(),t=function(e){var t,n,t=o[e],n=(0,s.GC$)(f("span"));i.itemContainers.push(n),n.addClass("gc-defined-column-list-item-container"),a(e)&&n.addClass("gc-defined-column-list-item-selected"),n.attr("role","option"),n.attr("tabindex","0"),n.attr("aria-selected",a(e)?"true":"false"),n.bind("click",function(t){i.isDisable()||i.GZe(e,t)}),i.ryn(t,n),r&&i.ryn(t,n,!0),n.appendTo(i.UZe)},c=0;c<o.length;c++)t(c)},u.prototype.meo=function(){var t,e,n,n,t=this,e=(0,s.GC$)(f("div"));e.addClass("gc-defined-column-list-header"),(n=(0,s.GC$)(f("div"))).addClass("gc-defined-column-list-header-text"),n.text((0,a.getSR)().columnTypeText),n.appendTo(e),(n=(0,s.GC$)(f("div"))).addClass("gc-defined-column-list-header-value"),n.text((0,a.getSR)().defineColumnValue),n.appendTo(e),e.appendTo(t.UZe)},u.prototype.ryn=function(t,e,n){var i,o,u,r,a,i=this,o=i.LEt.editable,u=o?(0,s.GC$)(f("input")):(0,s.GC$)(f("div")),r;u.addClass("gc-defined-column-list-item-"+(n?"value":"text")),t.class&&u.addClass(t.class),a=n||(0,c.isNullOrUndefined)(t.text)?t.value:t.text,o?u.val(a):u.text(a),u.appendTo(e),i.LEt.editable&&u.bind("blur",function(){n?t.value=this.value:t.text=this.value,i.LEt.onChange&&i.LEt.onChange()})},u.prototype.GZe=function(t,e){var n,i,o,u,r,a,c,n=this,i=n.LEt.enableMultiSelected,o=n.EZe,u=function(t){return i?-1<o.indexOf(t):n.$G===t};for(i?(r=function(t){var e=o.indexOf(t);-1<e?o.splice(e,1):o.push(t)},(0,l.isArray)(t)?t.forEach(function(t){r(t)}):r(t)):n.$G=t,a=0;a<n.itemContainers.length;a++)c=n.itemContainers[a],u(a)?(c.attr("aria-selected","true"),c.addClass("gc-defined-column-list-item-selected")):(c.attr("aria-selected","false"),c.removeClass("gc-defined-column-list-item-selected"));n.LEt.onChange&&n.LEt.onChange(n.value,n.index,e)},u.prototype.zto=function(t){var e,n,i,o,u,r,r,r,u,o,e=this,n=e.LEt.items,i=(0,s.GC$)(f("div"));i.addClass("gc-defined-column-add-remove-container"),(o=(0,s.GC$)(f("div"))).addClass("gc-defined-column-add-remove-container-buttons"),o.appendTo(i),(u=(0,s.GC$)(f("button"))).addClass("gc-defined-column-add-remove-container-button"),u.appendTo(o),(r=(0,s.GC$)(f("span"))).addClass("gc-defined-column-add-remove-container-button-icon gc-defined-column-add-remove-container-button-icon-add ui-icon ui-icon-check"),r.appendTo(u),(r=(0,s.GC$)(f("span"))).appendTo(u),r.text((0,a.getSR)().columnTypeComboBoxAdd),u.bind("click",function(){(n=n||[]).push({text:"",value:""}),e.setOptions({items:n}),e.LEt.onChange&&e.LEt.onChange()}),(r=(0,s.GC$)(f("button"))).addClass("gc-defined-column-add-remove-container-button"),r.appendTo(o),(u=(0,s.GC$)(f("span"))).addClass("gc-defined-column-add-remove-container-button-icon gc-defined-column-add-remove-container-button-icon-remove ui-icon ui-icon-check"),u.appendTo(r),(o=(0,s.GC$)(f("span"))).appendTo(r),o.text((0,a.getSR)().columnTypeComboBoxRemove),r.bind("click",function(){var t=e.$G;n.splice(t,1),e.setOptions({items:n}),e.LEt.onChange&&e.LEt.onChange()}),i.appendTo(t)},u.prototype.setValue=function(e){var i,t,t,i=this;(0,c.isNullOrUndefined)(e)||(i.LEt.enableMultiSelected&&(0,l.isArray)(e)?0<(t=e.reduce(function(t,e){var n=i.LEt.items.findIndex(function(t){return t.value===e});return-1<n&&t.push(n),t},[])).length&&i.GZe(t):-1<(t=i.LEt.items.findIndex(function(t){return t.value===e}))&&i.GZe(t))},u.prototype.setIndex=function(t){var e=this;(0,c.isNullOrUndefined)(t)||(e.LEt.enableMultiSelected&&(0,l.isArray)(t)?0<t.length&&e.GZe(t):-1<t&&e.GZe(t))},u.prototype.setOptions=function(t){var e=this;e.LEt=i(i({},e.LEt),t),e.clear(),e.tyn()},u.prototype.isDisable=function(){return!1===this.LEt.enable},u.prototype.resetDisable=function(){var t=this,e=t.isDisable(),n=t.UZe;(t.LEt.enable=e)?n.removeClass("gc-defined-column-disabled"):n.addClass("gc-defined-column-disabled"),n.attr("aria-disabled",e?"false":"true")},u.prototype.clear=function(){for(var t,e,n,i,t=this,e=0,n=t.itemContainers;e<n.length;e++)(i=n[e]).unbind("click");t.itemContainers=[],t.UZe.empty()},u.prototype.dispose=function(){var t=this;t.clear(),t.UZe=c.keyword_null},e.Jze=u},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-lookup.js":function(t,e,n){var c,a,s,l,f,d,n,h,i;function o(t){var e;this.Pq=[],this.BZe=[],this.LEt=t}c=this&&this.__assign||function(){return(c=Object.assign||function(t){for(var e,n,i,o,n=1,i=arguments.length;n<i;n++)for(o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},Object.defineProperty(e,"__esModule",{value:!0}),e.RZe=void 0,a=n("Core"),s=n("./dist/plugins/tableSheet/tableSheet.js"),l=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-input-number.js"),f=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-select.js"),d=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-utils.js"),n=a.mt,h=n.En,o.prototype.create=function(t){var e,n,e=this,n=e.QZe=(0,a.GC$)(h("div"));return n.addClass("gc-defined-column-look-up-container"),e.ZX(e.PZe(t)),n},o.prototype.ZX=function(t){var e,i,e,o,n,u,r,a,i=this,e=i.QZe,o=i.LEt.getLookup()||[],n=new f.Kze({inputId:"lookup-tables",validate:{required:!0},enableSearch:!0,stickItem:{value:d.keyword_undefined,text:(0,s.getSR)().defineColumnNone,iconClass:""},items:o.map(function(t){return{lookup:t,value:t.table,text:t.table,iconClass:""}}),label:{text:(0,s.getSR)().columnTypeLookupTables},onChange:function(t){var e,n,e,n=function(){i.YZe(i.Pq.slice(2)),i.Pq=i.Pq.slice(0,2),i.ltt().setValue()};(null==t?void 0:t.value)?(0===i.BZe.length?i.BZe.push(t.lookup):(i.BZe=[t.lookup],n()),o=i.LEt.getLookup(i.HZe()),i.ltt().setOptions({items:o.map(function(t){return{lookup:t,value:t.table,text:t.table,iconClass:""}})}),i.ltt().isDisable()&&i.ltt().resetDisable()):(i.BZe=[],n(),i.ltt().isDisable()||i.ltt().resetDisable()),i.WZe()}});if(n.create().appendTo(e),i.Pq.push({fieldSelect:n}),i.VZe(),t&&(n.setValue(null==(e=t[0])?void 0:e.table),1<t.length)&&i.ltt().setValue(t[1],t[0]),t&&2<t.length)for(u=2;u<t.length;u++)r=t[u],(a=i.Pq[u]).fieldSelect.setValue(r.table),a.fieldNumber&&i.FZe(a.fieldNumber,t[u-1].isMany,r.index)},o.prototype.VZe=function(t,e){var n,u,i,n,o,r,n,u=this,i=!!t,n=u.QZe,o=(0,a.GC$)(h("div"));o.appendTo(n),o.addClass("gc-defined-column-look-up-field-container"),(n=new l.AZe({inputId:"lookup-fields-number"+(t?"-"+t:"")+"-"+(u.Pq.length-1),minValue:0,maxValue:Number.MAX_SAFE_INTEGER,allowMinNull:!0,enable:i,visible:!(null==(n=u.BZe[u.Pq.length-1])||!n.isMany),label:{text:(0,s.getSR)().columnTypeLookupIndexes},onChange:function(t){var e,n,i,e=u.Pq.findIndex(function(t){return t.fieldSelect===r}),n=r.item.lookup,i=t||d.keyword_undefined;n?(n.index=t||d.keyword_undefined,u.BZe[e]?u.BZe[e]=n:u.BZe.push(n)):u.BZe[e]?u.BZe[e].index=i:u.BZe.push({table:d.keyword_null,index:i}),u.WZe()}})).create().appendTo(o),(r=new f.Kze({inputId:"lookup-fields"+(t?"-"+t:"")+"-"+u.Pq.length,enable:i,enableSearch:!0,stickItem:{value:d.keyword_undefined,text:(0,s.getSR)().defineColumnNone,iconClass:""},items:i?e.map(function(t){return{lookup:t,value:t.table,text:t.table,iconClass:""}}):[],label:{text:(0,s.getSR)().columnTypeLookupFields},onChange:function(t){var e,n,i,o,e,t,n=null==t?void 0:t.value,i=u.Pq.findIndex(function(t){return t.fieldSelect===r}),o=function(){var t=i+1;u.BZe=u.BZe.slice(0,t),u.YZe(u.Pq.slice(t)),u.Pq=u.Pq.slice(0,t)},e=null==(e=u.Pq[i].fieldNumber)?void 0:e.value;n?(u.BZe[i]?(u.BZe[i]=t.lookup,u.BZe[i].index=e,o()):u.BZe.push(t.lookup),t.lookup.isTable&&(t=u.LEt.getLookup(u.HZe()))&&0<t.length&&u.VZe(n,t)):(o(),u.BZe[i]&&(u.BZe[i]=c(c({},u.BZe[i]),{table:d.keyword_null}),e||(u.BZe[i].index=d.keyword_null))),u.WZe()}})).create().appendTo(o),u.Pq.push({fieldSelect:r,fieldNumber:n,lookupFieldContainer:o}),r.setValue()},o.prototype.ltt=function(){var n,t,i,o,n=this,t=n.Pq[1],i=t.fieldSelect,o=t.fieldNumber;return{setValue:function(t,e){i.setValue(null==t?void 0:t.table),n.FZe(o,null==e?void 0:e.isMany,null==t?void 0:t.index)},resetDisable:function(){i.resetDisable(),o.resetDisable()},isDisable:function(){return i.isDisable()},setOptions:function(t){var t;i.setOptions(t),n.FZe(o,n.BZe[0].isMany,null==(t=n.BZe[1])?void 0:t.index)}}},o.prototype.FZe=function(t,e,n){e?(t.setOptions({visible:!0}),t.setValue(n)):(t.setOptions({visible:!1}),t.setValue())},o.prototype.PZe=function(t){for(var i,o,u,e,r,n,a,i=this,o=[],u=t?t.split("."):[],e=function(t,e){var n,t,n=u[t];Number.isInteger(Number(n))?o[e]=c(c({},o[e]),{index:Number(n)}):(t=i.LEt.getLookup(u.slice(0,t).join(".")),o[e]=c(c(c({},o[e]),{table:n}),t.find(function(t){return t.table===n})),e++),r=e},n=0,a=0;n<u.length;n++)e(n,a),a=r;return o},o.prototype.HZe=function(t){var e,n,i,o,e=this,n="";if(t){for(i=0;i<e.BZe.length;i++)(o=e.BZe[i]).index&&(n+=".".concat(o.index)),o.table&&(n+=".".concat(o.table));n=n.slice(1)}else n=e.BZe.map(function(t){return t.table}).join(".");return n},o.prototype.WZe=function(){var t,e,t=this,e=t.HZe(!0);t.LEt.onChange(e)},o.prototype.validate=function(){var t,t;return null==(t=null==(t=this.Pq[0])?void 0:t.fieldSelect)?void 0:t.validate()},o.prototype.YZe=function(t){for(var e,n,i,o,u,r,o,e=function(t){t&&(t.dispose?t.dispose(!0):t.unbind&&t.unbind("click"))},n=0,i=t;n<i.length;n++)u=(o=i[n]).fieldSelect,r=o.fieldNumber,o=o.lookupFieldContainer,u&&e(u),r&&e(r),o&&o.remove();return[]},o.prototype.dispose=function(){var t=this;t.YZe(t.Pq),t.Pq=[],t.QZe=d.keyword_null},e.RZe=o},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-mask.js":function(t,e,n){var r,a,c,s,l,f,d,n,h,i;function o(t){var e=this;e.LEt=t,e.y4={pattern:t.pattern,placeholder:t.placeholder,excludeLiteral:t.excludeLiteral,excludePlaceholder:t.excludePlaceholder}}r=this&&this.__assign||function(){return(r=Object.assign||function(t){for(var e,n,i,o,n=1,i=arguments.length;n<i;n++)for(o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},Object.defineProperty(e,"__esModule",{value:!0}),e.JZe=void 0,a=n("Core"),c=n("DataManager"),s=n("./dist/plugins/tableSheet/tableSheet.js"),l=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-checkbox.js"),f=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-input.js"),d=null,n=a.mt,h=n.En,o.prototype.create=function(){var t;return this.ZX()},o.prototype.ZX=function(){var e,n,i,t,o,o,o,u,u,e=this,n=e.LEt,i=n.onChange,t=(0,a.GC$)(h("div"));return t.addClass("gc-defined-column-mask-container"),(o=e.XZe=new f.mZe({inputId:"pattern",label:{text:(0,s.getSR)().columnTypeMask.pattern},onChange:function(t){e.y4.pattern=(0,c.isNullOrEmpty)(t)?n.pattern:t,i&&i(r({},e.y4))}})).create().appendTo(t),n.pattern&&o.setValue(n.pattern),(o=e._Ze=new f.mZe({inputId:"placeholder",maxLength:1,label:{text:(0,s.getSR)().columnTypeMask.placeholder},onChange:function(t){e.y4.placeholder=(0,c.isNullOrEmpty)(t)?d:t,i&&i(r({},e.y4))}})).create().appendTo(t),n.placeholder&&o.setValue(n.placeholder),(o=(0,a.GC$)(h("div"))).addClass("gc-defined-column-mask-exclude-container"),o.appendTo(t),(u=e.Lse=new l.Rze({label:{text:(0,s.getSR)().columnTypeMask.excludeLiteral},onChange:function(t){e.y4.excludeLiteral=t,i&&i(r({},e.y4))}})).create().appendTo(o),n.excludeLiteral&&u.setValue(n.excludeLiteral),(u=e.Fse=new l.Rze({label:{text:(0,s.getSR)().columnTypeMask.excludePlaceholder},onChange:function(t){e.y4.excludePlaceholder=t,i&&i(r({},e.y4))}})).create().appendTo(o),n.excludePlaceholder&&u.setValue(n.excludePlaceholder),t},o.prototype.dispose=function(){var t=this;t.XZe.dispose(),t.XZe=d,t._Ze.dispose(),t._Ze=d,t.Lse.dispose(),t.Lse=d,t.Fse.dispose(),t.Fse=d},e.JZe=o},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-modified-fields.js":function(t,e,n){var i,o,u,r,a,c,n,s,l;function f(t){var e;this.LEt=t}i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var e,n,i,o,n=1,i=arguments.length;n<i;n++)for(o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},Object.defineProperty(e,"__esModule",{value:!0}),e.KZe=void 0,o=n("Core"),u=n("./dist/plugins/tableSheet/tableSheet.js"),r=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-checkbox.js"),a=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-list.js"),c=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-utils.js"),n=o.mt,s=n.En,f.prototype.create=function(){var t,e,t=this,e;return(t.$Ze=(0,o.GC$)(s("div"))).addClass("gc-defined-column-modified-fields-container"),t.ZX()},f.prototype.ZX=function(){var t,e,n,i,t,n,e=this,n=e.LEt,i=e.$Ze,t=(null==(t=n.fields)?void 0:t.split(","))||[];return e.qZe=new r.Rze({label:{text:(0,u.getSR)().columnTypeAllEditableFields},onChange:function(t){var t,t,t,t;t?(e.fields="*",e.Xze(),null!=(t=e.cUe)&&t.isDisable()||null!=(t=e.cUe)&&t.resetDisable()):null!=(t=e.cUe)&&t.isDisable()&&null!=(t=e.cUe)&&t.resetDisable()}}),e.qZe.create().appendTo(i),n=(0,c.isNullOrUndefined)(n.fields)||t.length===e.lUe().length||"*"===n.fields,e.qZe.setValue(n),e.cUe=new a.Jze({label:{text:(0,u.getSR)().GroupPanelFieldsHeader},enableMultiSelected:!0,items:e.lUe(),enable:!e.qZe.value,onChange:function(t){e.fields=t.join(","),t.length===e.lUe().length&&e.qZe.setValue(!0),e.Xze()}}),e.cUe.create().appendTo(i),e.cUe.setValue(t),i},f.prototype.lUe=function(){for(var t,e,n,i,o,u,t,e,n=[],i=0,o=this.LEt.getTriggerFields();i<o.length;i++)u=o[i],n.push({value:u});return n},f.prototype.Xze=function(){var t=this;t.LEt.onChange({fields:t.fields})},f.prototype.setOptions=function(t){var e=this;e.LEt=i(i({},e.LEt),t),e.clear(),e.ZX()},f.prototype.clear=function(){var t=this;t.L0(),t.$Ze.empty()},f.prototype.dispose=function(){var t=this;t.clear(),t.$Ze=c.keyword_null},f.prototype.L0=function(){var t=this;t.qZe&&t.qZe.dispose(),t.qZe=c.keyword_null,t.cUe&&t.cUe.dispose(),t.cUe=c.keyword_null},e.KZe=f},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-number-formatting.js":function(t,e,n){var i,m,o,N,u,r,a,D,c,s,j,l,n,f,d,h;function g(t){var e;this.LEt=t}i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var e,n,i,o,n=1,i=arguments.length;n<i;n++)for(o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},Object.defineProperty(e,"__esModule",{value:!0}),e.bZe=void 0,m=n("Common"),o=n("Core"),N=n("./dist/plugins/tableSheet/tableSheet.js"),u=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-input-number.js"),r=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-checkbox.js"),a=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-list.js"),D=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-formatting.js"),c=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-preview.js"),s=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-select.js"),j=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-utils.js"),l=m.Formatter&&m.Formatter.GeneralFormatter,n=o.mt,f=n.En,d=n.St,g.prototype.create=function(){var t,e,t=this,e;return(t.fUe=(0,o.GC$)(f("div"))).addClass("gc-defined-column-number-formatting-container"),t.ZX()},g.prototype.ZX=function(){var t,e,n,i,o,t=this,e=t.LEt,n=t.fUe,i=(0,D.parseFormat)(e.formatter),o=e.type||i.category;return t.dUe=new u.AZe({inputId:"number-formatting-decimal-places",maxValue:16,label:{text:(0,N.getSR)().columnTypeNumberFormattingDecimalPlaces},onChange:function(){6!==o&&t.gUe.setOptions({items:t.MUe()}),t.Xze()}}),t.dUe.create().appendTo(n),6===o?(t.eZe().appendTo(n),t.dUe.setValue((0,j.isNullOrUndefined)(i.decimalPlaces)?2:i.decimalPlaces)):((2===o?((0,j.isNullOrUndefined)(i.locale)&&e.formatter&&1===i.category&&(i.locale=(0,N.getSR)().columnTypeNumberFormatting.accountingSymbol[0][0]),t.oUt=new s.Kze({inputId:"number-formatting-symbol",items:(0,D.getListItems)(2,[{text:(0,N.getSR)().columnTypeNumberFormatting.accountingSymbol[0][0],value:(0,N.getSR)().columnTypeNumberFormatting.accountingSymbol[0][0]}]),label:{text:(0,N.getSR)().columnTypeNumberFormattingSymbol},onChange:function(){t.gUe.setOptions({items:t.MUe()}),t.Xze()}}),t.oUt):(t.hUe=new r.Rze({label:{text:(0,N.getSR)().columnTypeNumberFormattingThousandSeparator},onChange:function(){t.gUe.setOptions({items:t.MUe()}),t.Xze()}}),t.hUe)).create().appendTo(n),t.gUe=new a.Jze({label:{text:(0,N.getSR)().columnTypeNumberFormattingNegativeNumbers},items:t.MUe(),onChange:function(){t.Xze()}}),t.gUe.create().appendTo(n),t.eZe().appendTo(n),t.gUe.setIndex(0),t.dUe.setValue((0,j.isNullOrUndefined)(i.decimalPlaces)?2:i.decimalPlaces),2===o?i.locale?t.oUt.setValue(i.locale):t.oUt.setIndex(1):t.hUe.setValue(i.useThousand||!1),t.gUe.setIndex((0,j.isNullOrUndefined)(i.index)?0:i.index)),n},g.prototype.Xze=function(){var t=this,e=t.oZe();t.LEt.onChange(e)},g.prototype.oZe=function(){var t,e,n,t=this,e=6===t.LEt.type?t.IUe(t.dUe.value):2===t.LEt.type?t.bUe(t.gUe.value):t.NUe(t.gUe.index,t.dUe.value,t.hUe.value),n=t.Re("12345",e);return t.uZe.setText(n),e},g.prototype.IUe=function(t){var e,n,i,e="";if(0<t){for(i=0;i<t;i++)e+="0";n="0."+e+"%"}else n="0%";return n},g.prototype.bUe=function(t){return t},g.prototype.NUe=function(t,e,n){var i,o,u,r,a,i=(0,N.getSR)().columnTypeNumberFormatting.numberCategoryFormats,o=i.length/2,u=i[t];if(n&&(u=i[t+o]),0<e){for(r="",a=0;a<e;a++)r+="0";u=u.replace(/0/g,"0."+r)}return u},g.prototype.MUe=function(){var t=this,e;return 2===t.LEt.type?t.vUe():t.DUe()},g.prototype.DUe=function(){var t,e,n,i,o,n,u,r,a,c,s,l,f,f,t="1234.10",e=this.dUe.value,n=this.hUe&&this.hUe.value;if(0<e){for(i="",o=0;o<e;o++)i=(o%10).toString()+i.substring(0);t=t.substring(0,t.indexOf(".")+1)+i}else t=t.substring(0,t.indexOf("."));for(n&&(n=t.indexOf("1"),t=t.substring(0,n+1)+","+t.substring(n+1)),u=[],r=(0,N.getSR)().columnTypeNumberFormatting.negativeNumbers,a=0,c=Object.keys(r);a<c.length;a++)l=t,-1!==(f=r[s=c[a]]).indexOf("-")&&(l="-"+l),-1!==f.indexOf(")")&&(l="("+l+")"),-1!==f.indexOf("\u25b3")&&(l="\u25b3"+l),f={text:l=-1!==f.indexOf("\u25b2")?"\u25b2"+l:l,value:s,class:j.keyword_null},-1!==s.indexOf("red")&&(f.class="gc-defined-column-number-formatting-negative-item"),u.push(f);return u},g.prototype.vUe=function(){var t,e,n,i,o,u,r,a,c,s,l,f,d,h,g,b,v,v,M,I,b,t=this.dUe.value,e=this.oUt.value,n=[],i="-1234",o="",u="";if(0<t){for(u="."+Array(t).fill(0).join(""),r=Array(t),a=0;a<t;)r[a]=a%10,a++;r.reverse(),o=r.join("")}for(s=parseFloat(c=i+"."+o),l=(0,j.isNullOrUndefined)(e)||e===(0,N.getSR)().columnTypeNumberFormatting.accountingSymbol[0][0]?(0,N.getSR)().columnTypeNumberFormatting.currencyFormatWithoutSymbol:(0,D.getPredefinedFormats)(e,D.formatCategories[2]),f=/(\[[^\$^\]]+\])?(\[\$[^\]]+\])?(.*)(0)([^\[]*)(\[\$[^\]]+\])?/,d=0,h=l.length;d<h;d++)b=(g=l[d]).split(";"),v=(0,D.beforeAdjustFormatString)(b),b=b.map(function(t){return t.replace(f,function(){for(var t,e,n,t=[],e=1;e<7;e++)(0,j.isNullOrUndefined)(n=arguments[e])||(2!==e&&6!==e&&(n=n.replace(D.decimalPlacesWithZeroDotReg,"0"+u)),t.push(n));return t.join("")})}),(0,D.afterAdjustFormatString)(b,v),g=b.join(";"),v={},b={text:I=(M=new m.Formatter.GeneralFormatter(g)).format(s,v),value:g},(v.conditionalForeColor&&"red"===v.conditionalForeColor.toLowerCase()||"#FF0000"===v.conditionalForeColor)&&(b.class="gc-defined-column-number-formatting-negative-item"),n.push(b);return n},g.prototype.eZe=function(){var t,e,t,e;return(this.uZe=new c.rZe).create()},g.prototype.Re=function(t,e){var n,i;return d(e)?(n=(0,j.isNullOrUndefined)(t)?0:t,e.replace(/@/g,n)):(i=new l(e)).format(t)},g.prototype.setOptions=function(t){var e=this;e.LEt=i(i({},e.LEt),t),e.clear(),e.ZX()},g.prototype.clear=function(){var t=this;t.L0(),t.fUe.empty()},g.prototype.dispose=function(){var t=this;t.clear(),t.fUe=j.keyword_null},g.prototype.L0=function(){var t=this;t.dUe.dispose(),t.dUe=j.keyword_null,t.hUe&&t.hUe.dispose(),t.hUe=j.keyword_null,t.gUe&&t.gUe.dispose(),t.gUe=j.keyword_null,t.oUt&&t.oUt.dispose(),t.oUt=j.keyword_null,t.uZe.dispose(),t.uZe=j.keyword_null},e.bZe=g},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-popup.js":function(Z,t,e){var n,a,r,c,s,i,o,u,l,f,d,h,g,b,v,M,I,m,N,D,j,C,w,p,x,y,A,T,S,z,e,L,U,O,k;function E(t,e,n){var t,e,t=k.call(this,t,e)||this;return t.xT=[],(e=t).LEt=n,e.mUe=n.column,e.jUe={},e.Wc=e.Xc(),e._hidden(),t}n=this&&this.__extends||(O=function(t,e){return(O=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}O(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),a=this&&this.__assign||function(){return(a=Object.assign||function(t){for(var e,n,i,o,n=1,i=arguments.length;n<i;n++)for(o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},Object.defineProperty(t,"__esModule",{value:!0}),t.Uze=void 0,r=e("Core"),c=e("./dist/plugins/tableSheet/tableSheet.js"),s=e("./dist/plugins/tableSheet/tableSheet.interface.js"),i=e("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-select-types.js"),o=e("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-input.js"),u=e("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-tabs.js"),l=e("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-number-formatting.js"),f=e("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-date-formatting.js"),d=e("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-date-time-picker.js"),h=e("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-modified-fields.js"),g=e("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-attachment.js"),b=e("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-attachment-margin.js"),v=e("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-checkbox-cell-type.js"),M=e("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-checkbox-cell-type-text.js"),I=e("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-lookup.js"),m=e("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-formatting-select.js"),N=e("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-input-formula.js"),D=e("DataManager"),j=e("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-utils.js"),C=e("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-mask.js"),w=e("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-base.js"),p=e("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-utils.js"),x=e("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-barcode/tablesheet-define-column-barcode-options-container.js"),y=e("CellTypes"),A=e("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-combobox.js"),T=e("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-combobox-style.js"),S=e("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-url.js"),z="keydown.definedColumn.gcSheet",e=r.mt,L=e.En,k=r.zc,n(E,k),E.prototype.LF=function(){var e=this,t=e.Wc;t.css(["box-shadow","width","box-sizing","overflow-y"],["rgba(0, 0, 0, 0.15) 2px 4px 5px","400px","content-box","auto"]),t.addClass("gc-defined-column-container"),t.appendTo(e.qc()),e.CUe(),(0,r.GC$)(j.DOCUMENT).bind(z,function(t){27===t.keyCode&&e.close()})},E.prototype.lf=function(t){var e,n,n;return this.jUe.type===s.ColumnTypes.Formula&&-1<(null==(e=(0,r.GC$)(t).attr("class"))?void 0:e.indexOf("gcsj-func-ac"))||k.prototype.lf.call(this,t)},E.prototype.wUe=function(t,e){var t=new o.mZe(t),n=t.create();return(0,j.isNullOrUndefined)(e)||t.setValue(e),{input:t,elem:n}},E.prototype.TUe=function(t,e){var t=this.wUe(t,e),e=t.input,t=t.elem;return this.xT.push(e),t},E.prototype.CUe=function(){var u,t,e,n,u=this,t=u.Wc,e=(0,r.GC$)(L("div"));e.addClass("gc-defined-column-content-container"),e.appendTo(t),u.TUe({inputId:"value",placeholder:(0,c.getSR)().defineColumnValue,validate:{validating:function(t){var e,n,i,o,e=u.jUe.type,n=e===s.ColumnTypes.Formula;return n||((0,D.isNullOrEmpty)(t)?{success:!1,errorMessage:(0,c.getSR)().defineColumnRequired}:(i=u.LEt.getColumnBy(t),(o=0!==Object.keys(u.mUe).length&&t===u.mUe.value||!i||!1===i.visible)?!(!n&&(0,j.isFormula)(t)&&e!==s.ColumnTypes.Lookup&&((0,j.isRelation)(t)||u.LEt.isRelationship(t))):{success:!1,errorMessage:(0,c.getSR)().defineColumnExisted}))}},onBlur:function(){u.jUe.type===s.ColumnTypes.Barcode&&u.Sto(u.t())},onChange:function(t){u.jUe.value=t}},u.mUe.value).appendTo(e),u.TUe({inputId:"caption",placeholder:(0,c.getSR)().defineColumnCaption,onChange:function(t){u.jUe.caption=t}},u.mUe.caption).appendTo(e),n=u.AUe(),u.yUe().appendTo(e),n.appendTo(e),u.zUe().appendTo(t)},E.prototype.yUe=function(){var o,t,e,n,o=this,t=new i.pUe({items:o.LEt.columnTypeItems,onChange:function(t){var e,n,i,e=o.xT[0],n;e.isDisable()?(e.resetDisable(),(i=e.value)&&o.LEt.isRelationship(i)&&(e.setValue(j.keyword_null),o.jUe.value=j.keyword_null)):t.name===s.ColumnTypes.Lookup&&o.mUe.value&&o.LEt.isRelationship(o.mUe.value)&&(e.setValue(o.mUe.value),o.jUe.value=o.mUe.value),o.jUe={value:o.jUe.value,caption:o.jUe.caption,type:t.name},o.xUe(t)}}),e=t.create();return(0,j.isNullOrUndefined)(o.mUe.type)?0<Object.keys(o.mUe).length&&("number"===o.mUe.dataType?t.setValue(s.ColumnTypes.Number):"date"===o.mUe.dataType?t.setValue(s.ColumnTypes.Date):"boolean"===o.mUe.dataType?t.setValue(s.ColumnTypes.Checkbox):(0,j.isFormula)(o.mUe.formula)?t.setValue(s.ColumnTypes.Formula):o.LEt.isRelationship(o.mUe.value)?t.setValue(s.ColumnTypes.Lookup):(n=o.LEt.getSampleValue(o.mUe),(0,j.isNumber)(n)?t.setValue(s.ColumnTypes.Number):(0,j.isDate)(n)?t.setValue(s.ColumnTypes.Date):(0,j.isString)(n)?t.setValue(s.ColumnTypes.Text):(0,j.isBoolean)(n)&&t.setValue(s.ColumnTypes.Checkbox))):t.setValue(o.mUe.type),o.xT.push(t),e},E.prototype.Ue=function(){var t=this;return t.jUe&&t.jUe.style&&t.jUe.style.formatter?t.jUe.style.formatter:t.mUe&&t.mUe.style&&t.mUe.style.formatter?t.mUe.style.formatter:j.keyword_null},E.prototype.Lto=function(){var t=this;return t.jUe&&t.jUe.style&&t.jUe.style.cellType?t.jUe.style.cellType:t.mUe&&t.mUe.style&&t.mUe.style.cellType?t.mUe.style.cellType:j.keyword_null},E.prototype.SUe=function(){var t=this;return t.jUe&&t.jUe.style&&t.jUe.style.dropDowns?t.jUe.style.dropDowns[0].option:t.mUe&&t.mUe.style&&t.mUe.style.dropDowns?t.mUe.style.dropDowns[0].option:j.keyword_null},E.prototype.Neo=function(){var t=this;return t.jUe&&t.jUe.trigger&&t.jUe.trigger.fields?t.jUe.trigger.fields:t.mUe&&t.mUe.trigger&&t.mUe.trigger.fields?t.mUe.trigger.fields:j.keyword_null},E.prototype.ono=function(){var t=this,e=t.jUe,t=t.mUe;return 0<Object.keys(t).length&&!(0,D.isNullOrEmpty)(t.type)&&t.type!==e.type},E.prototype.uno=function(t){var e,n,i,e=this;if(e.ono())e.jUe.style=t;else{for(i in(0,j.isNullOrUndefined)(e.jUe.style)&&(e.jUe.style={}),n=e.mUe.style)n.hasOwnProperty(i)&&(e.jUe.style[i]=(0,j.cloneObject)(n[i],!1));for(i in t)t.hasOwnProperty(i)&&(e.jUe.style[i]=t[i])}},E.prototype.xUe=function(t){var e=this;switch(e.LUe.xT=e.YZe(e.LUe.xT),e.LUe.empty(),t.name){case s.ColumnTypes.Text:e.jUe.dataType="string",e.OUe().appendTo(e.LUe);break;case s.ColumnTypes.Number:e.IZe(1);break;case s.ColumnTypes.Percent:e.IZe(6,s.ColumnTypes.Percent.toLowerCase());break;case s.ColumnTypes.Currency:e.IZe(2,s.ColumnTypes.Currency.toLowerCase());break;case s.ColumnTypes.Lookup:e.ZUe();break;case s.ColumnTypes.Formula:e.kUe();break;case s.ColumnTypes.Phone:e.EUe();break;case s.ColumnTypes.Email:e.UUe();break;case s.ColumnTypes.URL:e.Oto();break;case s.ColumnTypes.Date:e.NZe(4,s.ColumnTypes.Date.toLowerCase());break;case s.ColumnTypes.CreatedTime:e.NZe(4,s.ColumnTypes.CreatedTime.toLowerCase());break;case s.ColumnTypes.ModifiedTime:e.NZe(4,s.ColumnTypes.ModifiedTime.toLowerCase());break;case s.ColumnTypes.Barcode:e.kto();break;case s.ColumnTypes.Attachment:e.Zto();break;case s.ColumnTypes.Checkbox:e.Eto();break;case s.ColumnTypes.Select:e.Uto()}},E.prototype.IZe=function(n,t){var i=this;t=t?"-"+t:"",i.jUe.dataType="number",i.GUe([{typeId:"number-formatting"+t,title:(0,c.getSR)().defineColumnFormatting,onInitPanel:function(t){var e=new l.bZe({type:n,formatter:i.Ue(),onChange:function(t){i.uno({mask:{pattern:"[-]{0,1}0{0,}[.]{0,1}0{0,}"},formatter:t})}});e.create().appendTo(t),i.LUe.xT.push(e)}},{typeId:"number-default"+t,title:(0,c.getSR)().defineColumnDefaultValue,onInitPanel:function(t){i.OUe().appendTo(t)}}]).appendTo(i.LUe)},E.prototype.ZUe=function(){var n,i,o,u,n=this,i=n.xT[0];i.resetDisable(),o={formattingSelect:j.keyword_null},u=n.BUe(o,"value"),n.GUe([{typeId:"lookup",title:(0,c.getSR)().defineColumnConfiguration,onInitPanel:function(t){var e=new I.RZe({getLookup:function(t){return n.LEt.getLookup(t)},onChange:function(t){o.formattingSelect&&(i.setValue(t),n.jUe.value=t,u())}});e.create(n.jUe.value||n.mUe.value).appendTo(t),n.LUe.xT.push(e)}},{typeId:"lookup-formatting",title:(0,c.getSR)().defineColumnFormatting,onInitPanel:function(t){o.formattingSelect=new m.MZe({formatter:n.Ue(),onChange:function(t){n.uno({formatter:t})}}),o.formattingSelect.create().appendTo(t),u(),n.LUe.xT.push(o.formattingSelect)}}]).appendTo(n.LUe)},E.prototype.BUe=function(e,n){var i,o,u,r,t,i=this,o=i.LUe.xT,u=o.length,r=function(t){t===o[u].isDisable(1)&&o[u].resetDisable(1)},t;return function(){var t,t,t,t=a(a({},i.mUe),i.jUe),t=t[n]&&i.LEt.getSampleValue(t),t=e.formattingSelect&&e.formattingSelect.createFormatting(t);r(t)}},E.prototype.kUe=function(){var n,i,o,n=this;n.jUe.dataType="formula",i={formattingSelect:j.keyword_null},o=n.BUe(i,"formula"),n.GUe([{typeId:"formula",title:(0,c.getSR)().defineColumnFormula,onInitPanel:function(t){var e=new N.vZe({validate:{validating:function(t){return!(0,D.isNullOrEmpty)(t)&&"="!==t||{success:!1,errorMessage:(0,c.getSR)().defineColumnRequired}}},getColumnFields:function(){return n.LEt.getColumnFields()},onChange:function(t){i.formattingSelect&&(n.jUe.formula=t,o())}});e.create().appendTo(t),e.setValue(n.jUe.formula||n.mUe.formula),n.LUe.xT.push(e)}},{typeId:"formula-formatting",title:(0,c.getSR)().defineColumnFormatting,onInitPanel:function(t){i.formattingSelect=new m.MZe({formatter:n.Ue(),onChange:function(t){n.uno({formatter:t})}}),i.formattingSelect.create().appendTo(t),o(),n.LUe.xT.push(i.formattingSelect)}}]).appendTo(n.LUe)},E.prototype.Oto=function(){var n,i,o,u,n=this;n.jUe.dataType="string",(i=n.Lto())&&"hyperlink"===i.type&&(o=i.linkColor,u=i.visitedLinkColor),n.GUe([{typeId:"url",title:(0,c.getSR)().defineColumnConfiguration,onInitPanel:function(t){var e=new S.Gto({linkColor:o,visitedLinkColor:u,onChange:function(t){n.uno({cellType:{type:"hyperlink",linkColor:t.linkColor,visitedLinkColor:t.visitedLinkColor,target:null==i?void 0:i.target}})}});e.create().appendTo(t),n.LUe.xT.push(e)}},{typeId:"url-default",title:(0,c.getSR)().defineColumnDefaultValue,onInitPanel:function(t){n.OUe().appendTo(t)}}]).appendTo(n.LUe)},E.prototype.EUe=function(){this.RUe("phone","1 \\(0{3}\\) 0{3}-0{4}")},E.prototype.UUe=function(){this.RUe("email","[a0._\\-]{1,}@[a0._\\-]{1,}.(com|cn|gov|edu)")},E.prototype.RUe=function(t,n){var i,o,i=this;i.jUe.dataType="string",o=i.QUe(),i.GUe([{typeId:t,title:(0,c.getSR)().defineColumnConfiguration,onInitPanel:function(t){var e,e,e=new C.JZe({pattern:null!=(e=o.pattern)?e:n,placeholder:o.placeholder,excludeLiteral:o.excludeLiteral,excludePlaceholder:o.excludePlaceholder,onChange:function(t){i.uno({mask:t})}});e.create().appendTo(t),i.LUe.xT.push(e)}},{typeId:t+"-default",title:(0,c.getSR)().defineColumnDefaultValue,onInitPanel:function(t){i.OUe().appendTo(t)}}]).appendTo(i.LUe)},E.prototype.QUe=function(){var t=this;return t.jUe&&t.jUe.style&&t.jUe.style.mask?t.jUe.style.mask:t.mUe&&t.mUe.style&&t.mUe.style.mask?t.mUe.style.mask:{}},E.prototype.NZe=function(t,e){var n=this;n.GUe(n.PUe(t,e)).appendTo(n.LUe)},E.prototype.PUe=function(n,t){var i,e,o,u,i=this,e=t?"-"+t:"";return i.jUe.dataType="date",t!==s.ColumnTypes.CreatedTime.toLowerCase()&&t!==s.ColumnTypes.ModifiedTime.toLowerCase()||(i.jUe.readonly=!0,i.jUe.trigger={formula:"=NOW()",when:t===s.ColumnTypes.CreatedTime.toLowerCase()?"onNew":"onNewAndUpdate"}),t===s.ColumnTypes.CreatedTime.toLowerCase()&&(i.jUe.defaultValue="=NOW()"),o=[],t===s.ColumnTypes.Date.toLowerCase()?(u=i.SUe(),o.push({typeId:"date-time-piker"+e,title:(0,c.getSR)().defineColumnConfiguration,onInitPanel:function(t){var e=new d.cZe({type:n,calendarPage:null==u?void 0:u.calendarPage,showTime:null==u?void 0:u.showTime,showDateRange:null==u?void 0:u.showDateRange,startDay:null==u?void 0:u.startDay,showBuiltInDateRange:null==u?void 0:u.showBuiltInDateRange,onChange:function(t){i.uno({cellButtons:[{imageType:r.ButtonImageType[r.ButtonImageType.dropdown],command:"openDateTimePicker",useButtonStyle:!1,visibility:r.ButtonVisibility[r.ButtonVisibility.always],buttonBackColor:"transparent"}],dropDowns:[{type:r.DropDownType[r.DropDownType.dateTimePicker],option:{startDay:t.startDay,calendarPage:t.calendarPage,showTime:t.showTime,showDateRange:null==u?void 0:u.showDateRange,showBuiltInDateRange:null==u?void 0:u.showBuiltInDateRange}}]})}});e.create().appendTo(t),i.LUe.xT.push(e)}})):t===s.ColumnTypes.ModifiedTime.toLowerCase()&&o.push({typeId:"modified-fields"+e,title:(0,c.getSR)().defineColumnConfiguration,onInitPanel:function(t){var e=new h.KZe({fields:i.Neo(),getTriggerFields:function(){return i.LEt.getTriggerFields()},onChange:function(t){i.jUe.trigger.fields=t.fields}});e.create().appendTo(t),i.LUe.xT.push(e)}}),o.push({typeId:"date-formatting"+e,title:(0,c.getSR)().defineColumnFormatting,onInitPanel:function(t){var e=new f.Wze({formatter:i.Ue(),onChange:function(t){i.uno({formatter:t.formatter})}});e.create().appendTo(t),i.LUe.xT.push(e)}}),o},E.prototype.t=function(){var t=this,e=t.Ue(),t,n=t.jUe.value||t.mUe.value,t=e?(0,p.parseBarCodeOptions)(t.LEt.getSheet(),e):(0,p.getDefaultBarcodeOptions)(n);return t},E.prototype.Sto=function(t){var e=this;t.value=(0,p.getFieldRef)(e.jUe.value||e.mUe.value),e.uno({formatter:(0,p.createBarcodeFormula)(e.LEt.getSheet(),t)})},E.prototype.kto=function(){var n=this,i=n.t(),o={container:j.keyword_null};n.Ue()||n.Sto(i),n.GUe([{typeId:"barcode-base",title:(0,c.getSR)().defineColumnConfiguration,onInitPanel:function(t){var e=new w.Ieo({option:i,onChange:function(t,e){n.Sto(t),e&&o.container.reset()}});e.create().appendTo(t),n.LUe.xT.push(e)}},{typeId:"barcode-options",title:(0,c.getSR)().defineColumnOthers,onInitPanel:function(t){var t=o.container=new x.nto({panel:t,option:i,onChange:function(t){n.Sto(t)}});t.create(),n.LUe.xT.push(t)}},{typeId:"barcode-default",title:(0,c.getSR)().defineColumnDefaultValue,onInitPanel:function(t){n.OUe().appendTo(t)}}]).appendTo(n.LUe)},E.prototype.Zto=function(){var n,i,o,n=this,i={type:"fileUpload"},o=n.Lto();n.GUe([{typeId:"attachment",title:(0,c.getSR)().defineColumnConfiguration,onInitPanel:function(t){var e=new g.YQe({fileType:null==o?void 0:o.accept,maxSize:null==o?void 0:o.maxSize,preview:null==o?void 0:o.isPreviewEnabled,download:null==o?void 0:o.isDownloadEnabled,clear:null==o?void 0:o.isClearEnabled,onChange:function(t){i.accept=t.fileType,i.maxSize=t.maxSize,i.isPreviewEnabled=!!t.preview,i.isDownloadEnabled=!!t.download,i.isClearEnabled=!!t.clear,n.uno({hAlign:r.HorizontalAlign[r.HorizontalAlign.center],vAlign:r.VerticalAlign[r.VerticalAlign.center],cellType:i})}});e.create().appendTo(t),n.LUe.xT.push(e)}},{typeId:"attachment-margin",title:(0,c.getSR)().columnTypeAttachmentMarginGroup,onInitPanel:function(t){var e=new b.zQe({marginLeft:null==o?void 0:o.marginLeft,marginRight:null==o?void 0:o.marginRight,marginTop:null==o?void 0:o.marginTop,marginBottom:null==o?void 0:o.marginBottom,onChange:function(t){i.marginLeft=t.marginLeft,i.marginRight=t.marginRight,i.marginTop=t.marginTop,i.marginBottom=t.marginBottom}});e.create().appendTo(t),n.LUe.xT.push(e)}}]).appendTo(n.LUe)},E.prototype.Eto=function(){var n,i,o,n=this,i={type:"checkbox"};n.jUe.dataType="boolean",o=n.Lto(),n.GUe([{typeId:"checkbox",title:(0,c.getSR)().defineColumnConfiguration,onInitPanel:function(t){var e=new v.Mto({caption:null==o?void 0:o.caption,boxSize:null==o?void 0:o.boxSize,autoBoxSize:"auto"===(null==o?void 0:o.boxSize),onChange:function(t){i.caption=t.caption,i.boxSize=t.boxSize,t.autoBoxSize&&(i.boxSize="auto"),n.uno({cellType:i})}});e.create().appendTo(t),n.LUe.xT.push(e)}},{typeId:"checkbox-text",title:(0,c.getSR)().columnTypeCheckboxTextGroup,onInitPanel:function(t){var e=new M.sto({isThreeState:null==o?void 0:o.isThreeState,checkboxTrue:null==o?void 0:o.textTrue,checkboxFalse:null==o?void 0:o.textFalse,checkboxIndeterminate:null==o?void 0:o.textIndeterminate,checkboxAlign:o?M.CheckBoxTextAlign[o.textAlign]:j.keyword_null,onChange:function(t){i.isThreeState=t.isThreeState,i.textTrue=t.checkboxTrue,i.textFalse=t.checkboxFalse,i.textIndeterminate=t.checkboxIndeterminate,i.textAlign=M.CheckBoxTextAlign[t.checkboxAlign]}});e.create().appendTo(t),n.LUe.xT.push(e)}}]).appendTo(n.LUe)},E.prototype.Uto=function(){var n,i,o,n=this,i={type:"combobox"},o=n.Lto();n.GUe([{typeId:"combobox",title:(0,c.getSR)().defineColumnConfiguration,onInitPanel:function(t){var e=new A.wto({items:null==o?void 0:o.items,onChange:function(t){i.items=t.items,n.uno({cellType:i})}});e.create().appendTo(t),n.LUe.xT.push(e)}},{typeId:"combobox-text",title:(0,c.getSR)().columnTypeCheckboxTextGroup,onInitPanel:function(t){var e=new T.vto({editorValueType:o?y.EditorValueType[o.editorValueType]:j.keyword_null,itemHeight:null==o?void 0:o.itemHeight,editable:null==o?void 0:o.editable,onChange:function(t){i.editorValueType=y.EditorValueType[t.editorValueType],i.itemHeight=t.itemHeight,i.editable=t.editable}});e.create().appendTo(t),n.LUe.xT.push(e)}}]).appendTo(n.LUe)},E.prototype.GUe=function(t){var t=new u.YUe({items:t});return this.LUe.xT.push(t),t.create()},E.prototype.OUe=function(){var n,t,e,t,n=this,t=n.wUe({inputId:"defaultValue",placeholder:(0,c.getSR)().defineColumnDefaultValue,onChange:function(t){var e;"string"===n.jUe.dataType||(0,j.isFormula)(t)||(0,D.isNullOrEmpty)(t)||(e=Number(t),Number.isNaN(e))||(t=e),n.jUe.defaultValue=t}},n.mUe.defaultValue),e=t.input,t=t.elem;return n.LUe.xT.push(e),t},E.prototype.AUe=function(){var t,e,t,e=this.LUe=(0,r.GC$)(L("div"));return e.xT=[],e.addClass("gc-defined-column-type-config-container"),e},E.prototype.Aoe=function(){var t,e,t=this,e=function(t){for(var e,n,i,e=0,n=t;e<n.length;e++)if((i=n[e]).validate&&!i.validate())return i.setFocus&&i.setFocus(),!1;return!0};return!!e(t.xT)&&!!e(t.LUe.xT)},E.prototype.zUe=function(){var u,t,e,n,i,o,i,u=this,t=(0,r.GC$)(L("div"));return t.addClass("gc-defined-column-footer-container"),(e=(0,r.GC$)(L("div"))).addClass("gc-defined-column-footer-container-buttons"),e.appendTo(t),(n=(0,r.GC$)(L("button"))).addClass("gc-defined-column-footer-container-button ui-button ui-state-default ui-corner-all btn btn-default"),(i=(0,r.GC$)(L("span"))).appendTo(n),i.text((0,c.getSR)().cancel),n.bind("click",function(){u.close()}),u.xT.push(n),(o=(0,r.GC$)(L("button"))).addClass("gc-defined-column-footer-container-button gc-defined-column-footer-container-button-primary ui-button ui-state-default ui-corner-all btn btn-default"),o.appendTo(e),n.appendTo(e),(i=(0,r.GC$)(L("span"))).appendTo(o),i.text((0,c.getSR)().submit),o.bind("click",function(){var t,e,n,i,o,o,t=a({},u.jUe);if(u.Aoe()){if(e=t.type===s.ColumnTypes.Formula){if(!(0,D.isNullOrEmpty)(t.value)&&(t.name=t.value,(0,j.isFormula)(t.value)))return;t.value=t.formula,(0,D.isNullOrEmpty)(t.name)&&delete t.dataType,delete t.formula}(i=0<(n=Object.keys(u.mUe)).length)&&(u.mUe.value===t.value||(0,D.isNullOrEmpty)(t.value))||(o=u.LEt.getColumnBy(t.value))&&!1===o.visible&&(u.mUe=a({},o),u.mUe.visible=!0,i=0<(n=Object.keys(u.mUe)).length),i?(e&&(0,D.isNullOrEmpty)(t.value)&&(t.value=u.mUe.formula),t.value=t.value||u.mUe.value,t.type=t.type||u.mUe.type,t.caption=t.caption||u.mUe.caption,o=a({},u.mUe),(0,D.isNullOrEmpty)(o.formula)||(o.value=o.formula,delete o.formula),e&&(0,D.isNullOrEmpty)(t.name)&&"formula"===t.dataType&&!(0,D.isNullOrEmpty)(o.name)&&"formula"===o.dataType||((0,c.copyOriginalColumnPropertiesToCurrentColumn)(t,o,n),u.LEt.onSaving({cmd:"ModifyColumn",originalColumn:o,column:t}))):t.value&&u.LEt.onSaving({cmd:"DefineColumn",column:t})}}),u.xT.push(o),n.bind("mouseover",function(){o.addClass("ui-state-hover")}).bind("mouseout",function(){o.removeClass("ui-state-hover")}),o.bind("mouseover",function(){o.addClass("ui-state-hover")}).bind("mouseout",function(){o.removeClass("ui-state-hover")}),t},E.prototype._hidden=function(){var t,e;this.Wc.css(["visibility","display"],["hidden","block"])},E.prototype.$c=function(t){var e,n;this.Wc.css(["visibility"],["visible"]),k.prototype.$c.call(this,t)},E.prototype.YZe=function(t){for(var e,n,i,e=0,n=t;e<n.length;e++)(i=n[e]).dispose?i.dispose():i.unbind&&(i.unbind("click"),i.unbind("mouseover"),i.unbind("mouseout"));return[]},E.prototype.L0=function(){var t=this;t.xT=t.YZe(t.xT),t.LUe.xT=t.YZe(t.LUe.xT),t.LUe.empty(),t.LUe=j.keyword_null,(0,r.GC$)(j.DOCUMENT).unbind(z)},E.prototype.close=function(){var t=this;t._hidden(),t.L0(),k.prototype.close.call(this),t.LEt.onClosed()},E.prototype.rQ=function(t,e){var n=this,i=n.Wc;n.LF(),i.css(["left","top"],[t,e]),i.css("max-height",Math.min(window.innerHeight,window.innerHeight-e)+"px"),n.LEt.onOpening(),n.$c(),n.if(),setTimeout(function(){n.Wc.focus(),n.xT[0].setFocus()}),n.LEt.onOpened()},t.Uze=E},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-preview.js":function(t,e,n){var i,o,u,r,n,a,c;function s(t){var e;this.LEt=t}Object.defineProperty(e,"__esModule",{value:!0}),e.rZe=void 0,i=n("Core"),o=n("./dist/plugins/tableSheet/tableSheet.js"),u=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-divider.js"),r=null,n=i.mt,a=n.En,s.prototype.create=function(){var t;return this.eZe()},s.prototype.eZe=function(){var t,e,n,t,t=this,e=(0,i.GC$)(a("div"));return e.addClass("gc-defined-column-number-formatting-preview-container"),(n=t.HUe=new u.dZe({text:(0,o.getSR)().columnTypeNumberFormattingSample})).create().appendTo(e),(t=t.uZe=(0,i.GC$)(a("div"))).appendTo(e),t.addClass("gc-defined-column-number-formatting-preview-text"),e},s.prototype.setText=function(t){var e;this.uZe.text(t)},s.prototype.dispose=function(){var t=this;t.uZe=r,t.HUe.dispose(),t.HUe=r},e.rZe=s},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-select-types.js":function(t,e,n){var a,i,u,o,c,n,s,r;function l(t){this.itemContainers=[];var e=this;e.LEt=t,e.jrt=e.ano=c.keyword_null}Object.defineProperty(e,"__esModule",{value:!0}),e.pUe=void 0,a=n("Core"),i=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-input.js"),u=n("./dist/plugins/tableSheet/tableSheet.js"),o=n("DataManager"),c=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-utils.js"),n=a.mt,s=n.En,l.prototype.WUe=function(t){var e=this;return{inputId:"type-select",type:"text",placeholder:(0,u.getSR)().defineColumnType,isReadOnly:!!t,label:t?{iconClass:t,suffixIconClass:"gc-defined-column-type-icon-chevrondown"}:{iconClass:"gc-defined-column-type-icon-magnifyingglass",suffixIconClass:"gc-defined-column-type-icon-x",onSuffixClick:function(t){(e.jrt||e.ano)&&(0,o.isNullOrEmpty)(e.XKt)?(e.jrt=e.jrt||e.ano,e.VUe(e.jrt,t)):e.FUe.setValue()}},onChange:t?c.keyword_null:function(t){e.JUe(t)},onClick:t?function(t){e.FUe.setOptions(e.WUe()),e.FUe.setValue(),e.ano=e.jrt,e.jrt=c.keyword_null,e.LEt.onChange({name:c.keyword_null},t)}:c.keyword_null}},l.prototype.create=function(){var t,e,n,e,t=this,e=t.LEt.items,n=(0,a.GC$)(s("div"));return n.addClass("gc-defined-column-type-select-container"),n.attr("role","combobox"),n.attr("aria-autocomplete","list"),n.attr("aria-controls","gc-defined-column-types-container"),t.FUe=new i.mZe(t.WUe()),t.FUe.create().appendTo(n),(0,c.isNullOrUndefined)(t.jrt)&&e&&0<e.length&&t.XUe().appendTo(n),(e=(0,a.GC$)(s("div"))).addClass("gc-defined-column-field-label-validation-container"),e.appendTo(n),n},l.prototype.XUe=function(){var t,e,n,t=this,e=t.LEt.items,n=t._Ue=(0,a.GC$)(s("div"));return n.addClass("gc-defined-column-types-container"),n.attr("id","gc-defined-column-types-container"),n.attr("role","listbox"),t.tyn(e),n},l.prototype.tyn=function(t){for(var i,o,e,n,u,r,i=this,o=i._Ue,e=function(e){var t,n,n,t=(0,a.GC$)(s("div"));i.itemContainers.push(t),t.addClass("gc-defined-column-field-type-container"),t.attr("role","option"),(0,c.isFunction)(i.LEt.onChange)&&t.bind("click",function(t){i.jrt=e,i.VUe(e,t)}),(n=(0,a.GC$)(s("div"))).addClass("gc-defined-column-field-type-icon "+e.iconClass),n.appendTo(t),(n=(0,a.GC$)(s("div"))).addClass("gc-defined-column-field-type-text"),n.text(e.text||e.name),n.appendTo(t),t.appendTo(o)},n=0,u=t;n<u.length;n++)e(r=u[n])},l.prototype.setValue=function(e){var t,n,t=this;!(0,c.isNullOrUndefined)(e)&&(n=t.LEt.items.find(function(t){return t.name===e}))?(t.jrt=n,t.VUe(n)):t.FUe.setValue()},l.prototype.VUe=function(t,e){var n=this;n.isValid()||n.resetValid(n.validate()),n.FUe.setOptions(n.WUe(t.iconClass)),n.FUe.setValue(t.text||t.name),n.d5(),n.LEt.onChange(t,e)},l.prototype.d5=function(){var t,e,n,i,t=this;if(t._Ue.empty(),(0,c.isFunction)(t.LEt.onChange))for(e=0,n=t.itemContainers;e<n.length;e++)(i=n[e]).unbind("click")},l.prototype.JUe=function(e){var t,n,n,t=this,n=t.LEt.items;t.XKt=e,t.d5(),(0,c.isNullOrUndefined)(e)||""===e?t.tyn(n):(e=e.toLowerCase(),n=n.filter(function(t){return-1<(t.text||t.name).toLowerCase().indexOf(e)}),t.tyn(n))},l.prototype.validate=function(){var t=this;return(0,c.isNullOrUndefined)(t.jrt)?(t.resetValid(!1,{required:!1}),!1):(t.resetValid(!0),!0)},l.prototype.resetValid=function(t,e){var n,i,n,o,o,i,n=null==(n=this._Ue)?void 0:n.parent();n&&(o=n.find(".gc-defined-column-field-label-validation-container"),o=(0,a.GC$)(o[o.length-1]),t?(n.removeClass("gc-define-column-invalid"),n.attr("aria-invalid","false"),o.hide(),o.empty()):n.hasClass("gc-define-column-invalid")||(n.addClass("gc-define-column-invalid"),n.attr("aria-invalid","true"),!1===e.required&&o.html((0,u.getSR)().defineColumnRequired),o.show()))},l.prototype.isValid=function(){var t,e,t,e,t=null==(t=this._Ue)?void 0:t.parent();return t&&!t.hasClass("gc-define-column-invalid")},l.prototype.setFocus=function(){this.FUe.setFocus()},l.prototype.dispose=function(){var t=this;t.FUe.dispose(),t.FUe=c.keyword_null,t.d5(),t._Ue=c.keyword_null},e.pUe=l},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-select.js":function(t,e,n){var i,u,o,r,a,n,c,s;function l(){var t,e,t=Date.now().toString(),e=Math.floor(1e3*Math.random()).toString().padStart(3,"0")}function f(t){var e=this;e.LEt=t,e.KUe=a.keyword_null,e.$Ue="select-input-"+t.inputId||0}i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var e,n,i,o,n=1,i=arguments.length;n<i;n++)for(o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},Object.defineProperty(e,"__esModule",{value:!0}),e.Kze=void 0,u=n("Core"),o=n("DataManager"),r=n("./dist/plugins/tableSheet/tableSheet.js"),a=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-utils.js"),n=u.mt,c=n.En,Object.defineProperty(f.prototype,"value",{get:function(){var t;return null==(t=this.KUe)?void 0:t.value},enumerable:!1,configurable:!0}),Object.defineProperty(f.prototype,"index",{get:function(){var e=this;return this.EE().findIndex(function(t){return t.value===e.value})},enumerable:!1,configurable:!0}),Object.defineProperty(f.prototype,"item",{get:function(){return this.KUe},enumerable:!1,configurable:!0}),Object.defineProperty(f.prototype,"container",{get:function(){return this.yZe},enumerable:!1,configurable:!0}),f.prototype.EE=function(){var t,e=this.LEt,n=e.stickItem,e=e.items;return n?[n].concat(e):e},f.prototype.create=function(){var n,t,e,i,o,i,o,t,t,n=this,t=n.LEt,e=n.yZe=(0,u.GC$)(c("div"));return e.addClass("gc-defined-column-field-item-container gc-defined-column-select-box-container"),t.label&&t.label.text&&((i=(0,u.GC$)(c("div"))).addClass("gc-defined-column-field-label-container"),i.appendTo(e),(o=(0,u.GC$)(c("label"))).addClass("gc-defined-column-field-label-container-label-text"),o.attr("for","gc-defined-column-input-"+n.$Ue),o.appendTo(i),o.text(t.label.text)),(i=(0,u.GC$)(c("div"))).addClass("gc-defined-column-select-container"),n.isDisable()&&i.addClass("gc-defined-column-disabled"),n.isVisible()||e.addClass("gc-defined-column-hide"),i.attr("role","combobox"),i.attr("aria-autocomplete","list"),i.attr("aria-controls","gc-defined-column-select-list-container"),i.attr("aria-disabled",n.isDisable()?"true":"false"),i.appendTo(e),(o=n.peo=(0,a.createDCElement)("sjs-select")).attr("id","gc-defined-column-input-"+t.inputId),(0,a.isNullOrUndefined)(t.defaultValue)||o.attr("value",t.defaultValue),n.LEt.enableSearch&&o.attr("editable",!0),o.attr("no-result-tip",(0,r.getSR)().defineColumnNoResults),(t=n.EE())&&0<t.length&&o.attr("items",JSON.stringify(n.EE().map(function(t){return{value:t.value,label:t.text||t.value}}))),o.bind("input",function(t){var e,t,e=t.target.value;(0,a.isNullOrUndefined)(t.target.value)&&(e=a.keyword_null),t=n.EE().find(function(t){return((0,a.isNullOrUndefined)(t.value)?a.keyword_null:t.value)===e}),n.KUe=t,n.LEt.onChange(n.KUe),n.isValid()||n.resetValid(n.validate())}),o.appendTo(i),(t=(0,u.GC$)(c("div"))).addClass("gc-defined-column-field-label-validation-container"),t.appendTo(e),t.hide(),e},f.prototype.setValue=function(e,t){var n=this;n.setIndex(n.EE().findIndex(function(t){return t.value===e}),t)},f.prototype.setIndex=function(t,e){var n,t,n=this;!(0,a.isNullOrUndefined)(t)&&-1<t&&(t=n.EE()[t])?(n.KUe=t,n.qUe(t,null==e||e)):n.qUe(a.keyword_null)},f.prototype.setOptions=function(t){var e,n,e=this,n=e.LEt.items!==t.items;e.LEt=i(i({},e.LEt),t),n&&e.peo.setItems(e.EE().map(function(t){return{value:t.value,label:t.text||t.value}})),e.setValue()},f.prototype.qUe=function(t,e){var n=this;t?n.peo.val(t.value):(n.KUe=a.keyword_null,n.peo.val(a.keyword_null)),e&&n.LEt.onChange(t)},f.prototype.isDisable=function(){return!1===this.LEt.enable},f.prototype.resetDisable=function(){var t=this,e=t.isDisable(),n=t.peo.parent();(t.LEt.enable=e)?n.removeClass("gc-defined-column-disabled"):n.addClass("gc-defined-column-disabled"),n.attr("aria-disabled",e?"false":"true")},f.prototype.isVisible=function(){return!0!==this.LEt.hide},f.prototype.resetVisible=function(){var t=this,e=t.isVisible(),n=t.yZe;(t.LEt.hide=e)?n.addClass("gc-defined-column-hide"):n.removeClass("gc-defined-column-hide"),n.attr("aria-hide",e?"true":"false")},f.prototype.validate=function(){var t,e,n,n,t=this,e=t.LEt.validate,n=!t.isDisable();if(e&&n){if(e.required&&(0,o.isNullOrEmpty)(t.value))return t.resetValid(!1,{required:!1}),!1;if(e.validating)if("boolean"==typeof(n=e.validating(t.value))){if(!n)return t.resetValid(!1),!1}else if(!n.success)return t.resetValid(!1,n),!1}return t.resetValid(!0),!0},f.prototype.resetValid=function(t,e){var n,i,o,n,n=this,i=n.peo,o=i.parent(),n=(0,u.GC$)(n.yZe.find(".gc-defined-column-field-label-validation-container")[0]);t?(o.removeClass("gc-define-column-invalid"),i.removeClass("gc-define-column-invalid"),i.attr("aria-invalid","false"),n.hide(),n.empty()):o.hasClass("gc-define-column-invalid")||(o.addClass("gc-define-column-invalid"),i.addClass("gc-define-column-invalid"),i.attr("aria-invalid","true"),!1===e.required?n.html((0,r.getSR)().defineColumnRequired):e.errorMessage&&n.html(e.errorMessage),e&&n.show())},f.prototype.isValid=function(){var t,e;return!this.peo.parent().hasClass("gc-define-column-invalid")},f.prototype.dispose=function(t){var e=this;e.peo.unbind("input"),e.peo=a.keyword_null,t&&e.yZe.remove(),e.yZe=a.keyword_null},e.Kze=f},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-tabs.js":function(t,e,n){var l,n,f,i;function o(t){var e;this.itemContainers=[],this.LEt=t}Object.defineProperty(e,"__esModule",{value:!0}),e.YUe=void 0,n=(l=n("Core")).mt,f=n.En,o.prototype.create=function(){var t,e,t=this,e=(0,l.GC$)(f("div"));return e.addClass("gc-defined-column-tab-list-container"),t.GUe().appendTo(e),t.eQe().appendTo(e),t.oQe().appendTo(e),e},o.prototype.GUe=function(){var s,t,e,n,i,s=this,t=s.LEt.items,e=(0,l.GC$)(f("div"));for(e.addClass("gc-defined-column-tab-list-items-container"),e.attr("role","tablist"),n=function(r){var a,c,a=t[r],c=(0,l.GC$)(f("div"));s.itemContainers.push([c]),c.addClass("gc-defined-column-tab-item-container"),c.attr("role","tab"),c.attr("tabindex",0),c.attr("id","gc-defined-column-tab-"+a.typeId),c.attr("aria-selected","false"),c.attr("aria-controls","gc-defined-column-tab-panel-"+a.typeId),c.text(a.title),0===r&&(c.attr("aria-selected","true"),c.addClass("gc-defined-column-tab-item-container-selected")),c.bind("click",function(t){var e,n,i,o,i,u;if(!1!==s.itemContainers[r][2]){for(e=0,n=s.itemContainers;e<n.length;e++)o=(i=n[e])[0],i=i[1],o.attr("aria-selected","false"),o.removeClass("gc-defined-column-tab-item-container-selected"),i.removeClass("gc-defined-column-tab-panel-container-selected");c.attr("aria-selected","true"),c.addClass("gc-defined-column-tab-item-container-selected"),(u=s.itemContainers[r][1]).addClass("gc-defined-column-tab-panel-container-selected"),a.onSelect&&a.onSelect(a,t)}}),c.appendTo(e)},i=0;i<t.length;i++)n(i);return e},o.prototype.oQe=function(){var t,e,n,i,o,u,t=this,e=t.LEt.items,n=(0,l.GC$)(f("div"));for(n.addClass("gc-defined-column-tab-panels-container"),i=0;i<e.length;i++)o=e[i],u=(0,l.GC$)(f("div")),(t.itemContainers[i][1]=u).addClass("gc-defined-column-tab-panel-container"),u.attr("role","tabpanel"),u.attr("id","gc-defined-column-tab-panel-"+o.typeId),u.attr("aria-labelledby","gc-defined-column-tab-"+o.typeId),0===i&&u.addClass("gc-defined-column-tab-panel-container-selected"),o.onInitPanel&&o.onInitPanel(u),u.appendTo(n);return n},o.prototype.eQe=function(){var t=(0,l.GC$)(f("div"));return t.addClass("gc-defined-column-tab-list-break-line"),t},o.prototype.isDisable=function(t){return!1===this.itemContainers[t][2]},o.prototype.resetDisable=function(t){var e=this,n=e.isDisable(t),i=e.itemContainers[t][0];(e.itemContainers[t][2]=n)?i.removeClass("gc-defined-column-disabled"):i.addClass("gc-defined-column-disabled"),i.attr("aria-disabled",n?"false":"true")},o.prototype.d5=function(){for(var t,e,t=this,e=0;e<t.LEt.items.length;e++)t.itemContainers[e]&&t.itemContainers[e][0].unbind("click");t.itemContainers=[]},o.prototype.dispose=function(){var t;this.d5()},e.YUe=o},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-url.js":function(t,e,n){var r,a,c,i,s,l,n,f,o;function u(t){var e;this.LEt=t}Object.defineProperty(e,"__esModule",{value:!0}),e.Gto=void 0,r=n("Core"),a=n("./dist/plugins/tableSheet/tableSheet.js"),c=n("./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-input.js"),i=null,s="#0066cc",l="#3399ff",n=r.mt,f=n.En,u.prototype.create=function(){var t;return this.ZX()},u.prototype.ZX=function(){var e,t,n,i,o,u,e=this,t=e.LEt,n=t.onChange,i=(0,r.GC$)(f("div"));return i.addClass("gc-defined-column-url-container"),(o=e.Rto=new c.mZe({inputId:"linkColor",type:"color",defaultValue:t.linkColor||s,label:{text:(0,a.getSR)().columnTypeUrlLabel.linkColor},onChange:function(t){n&&n({linkColor:t||s,visitedLinkColor:e.Pto.value||l})}})).create().appendTo(i),(u=e.Pto=new c.mZe({inputId:"visitedLinkColor",type:"color",defaultValue:t.visitedLinkColor||l,label:{text:(0,a.getSR)().columnTypeUrlLabel.visitedLinkColor},onChange:function(t){n&&n({linkColor:e.Rto.value||s,visitedLinkColor:t||l})}})).create().appendTo(i),n&&n({linkColor:e.Rto.value||s,visitedLinkColor:e.Pto.value||l}),i},u.prototype.dispose=function(){var t=this;t.Rto.dispose(),t.Rto=i,t.Pto.dispose(),t.Pto=i},e.Gto=u},"./dist/plugins/tableSheet/tablesheet-define-columns/tablesheet-define-column-utils.js":function(t,u,e){var n,r,e,i;function o(t){return"boolean"==typeof t}function a(t){return"string"==typeof t}function c(t){return t&&"="===t[0]}function s(t){return t&&-1<t.indexOf(".")}function l(t){return"number"==typeof t}function f(t){return n.Common.ct.W0(t)}function d(t){return"function"==typeof t}function h(t,e){var t=t.get(0);b(t)?t.aer():t.dispatchEvent(new Event(e))}function g(t){return t&&-1<t.indexOf("sjs-")}function b(t){return g(t.nodeName.toLowerCase())}function v(t){var t=t.get(0);return b(t)?t.shadowRoot&&t.shadowRoot.querySelector("input"):t}function M(t,e){var n,i,o,n=(0,r.GC$)(t,e);return b(t)&&(n.focus=function(){n.get(0).ZS()},n.setItems=function(t){t&&0<t.length&&n.attr("items",JSON.stringify(t));try{n.get(0).Cor(t)}catch(t){}},n.val=function(t){var e=n.get(0);if(!arguments.length)return e.Sl();try{!(0,u.isNullOrUndefined)(e.getAttribute("value"))||e.shadowRoot&&!(0,u.isNullOrUndefined)(e.shadowRoot.innerHTML)&&""!==e.shadowRoot.innerHTML?e.Kbt(t):e.y4=t}catch(t){}(0,u.isNullOrUndefined)(t)||e.setAttribute("value","string"==typeof t?t:""+t)},i=n.bind.bind(n),n.bind=function(t,e,o){void 0===o&&(o=e,e=u.keyword_undefined),"input"===t?i(t="sjs-value-change",e,function(t){var e=!1,n,i=!1;(0,u.isNullOrUndefined)(t.target)?(e=!0,t.target={value:t.detail}):t.target.value!==t.detail&&(i=!0,n=t.target.value,t.target.value=t.detail),o(t),i&&(t.target.value=n),e&&delete t.target}):i(t,e,o)},o=n.unbind.bind(n),n.unbind=function(t){o(t="input"===t?"sjs-value-change":t)}),n}function I(t){return M(i(t))}function m(t){return D(t,!0)}function N(t){return D(t,!1)}function D(t,e){var n,i,o,u,i,r,n={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"},i=new RegExp("(?:&|<|>|\"|'|`)","g"),o={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#x27;":"'","&#x60;":"`"},u=new RegExp("(?:&amp;|&lt;|&gt;|&quot;|&#x27;|&#x60;)","g"),i=e?i:u,r=e?n:o;return i.test(t)?t.replace(i,function(t){return r[t]}):t}Object.defineProperty(u,"__esModule",{value:!0}),u.dI=u.j0=u.createDCElement=u.DC$=u.getSJSInput=u.isSJSElem=u.isSJSComponent=u.triggerEvent=u.isFunction=u.isDate=u.isNumber=u.isRelation=u.isFormula=u.isString=u.isBoolean=u.keyword_undefined=u.keyword_null=u.cloneObject=u.isNullOrUndefined=u.DOCUMENT=void 0,n=e("Common"),r=e("Core"),u.DOCUMENT=document,e=n.Common.lt,i=r.mt.En,u.isNullOrUndefined=e.ht,u.cloneObject=e.tv,u.keyword_null=null,u.keyword_undefined=void 0,u.isBoolean=o,u.isString=a,u.isFormula=c,u.isRelation=s,u.isNumber=l,u.isDate=f,u.isFunction=d,u.triggerEvent=h,u.isSJSComponent=g,u.isSJSElem=b,u.getSJSInput=v,u.DC$=M,u.createDCElement=I,u.j0=m,u.dI=N},"./dist/plugins/tableSheet/tablesheet-filter-dialog.js":function(t,e,n){var i,o,u,Z,n,U,z,G,I,m,N,x,r,D,a,c,s;function l(t,e,n,i,o,u){var t=s.call(this,t,e,n,i,o)||this;return t.cze=!1,t.vMi=u,t}i=this&&this.__extends||(c=function(t,e){return(c=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}c(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),Object.defineProperty(e,"__esModule",{value:!0}),e.TableSheetFilterDialog=void 0,o=n("Filters"),u=n("Common"),Z=n("./dist/plugins/tableSheet/tablesheet-filter-utils.js"),n=u.Common.lt,U=n.ht,z=n.q8,G=Object.keys,I="null",m="undefined",N=null,x=void 0,r=1e5,D=/((\d{4}|\d{2}|\d{1})[\/\-])(\d{4}|\d{2}|\d{1})[\/\-](\d{4}|\d{2}|\d{1})|((\d{4}|\d{2}|\d{1})[\/\-\s](\d{4}|\d{2}|\d{1}) [0-9]{2}:[0-9]{2}:[0-9]{2})/,s=o.iut,i(l,s),l.prototype.Bst=function(t,e,n,i,o,u,r){var a,c,s,l,f,d,h,g,b,v,M,I,m,c,N,N,h,d,h,D,j,C,w,p,a=this,c=this.vMi,s=c.Mf,l=this.filterButtonInfo.col,f=c.getDataView(),d=f&&f.gLt,h=function(t){return f.uFt(t)},g=this,b;if(g.filterButtonInfo.rowFilter){if(M=0,m=[],c=c.qNi(),(N=(N=f.kve(t)).value||N.name)&&!c){if((I=h(N))?d&&d.rBt(N)&&I.K$!==d.rBt(N)&&f.eRt([N],!0):f.eRt([N]),I=h(N),U(I))return;v=I.K$,(d=null==(h=d.jRt(N))?void 0:h.dataMap)&&"object"==typeof d&&(h=typeof Object.values(d)[0],-1!==["string","number","boolean"].indexOf(h))&&(v=h),delete I.K$,m=G(I),M=m.length,I.K$=v,g.wTi=g.yTi(f.lFt&&0<f.lFt.length?f.iRt.slice(f.lFt.length):f.iRt)}if(g.Jut=!1,D=s.getCellType(-1,t),j=s.l0(-1,t===l?t:l,"formatter"),c)g.Jut=x,g.mTi(N,t,e,n,i,o,u,r,D,j);else if(1e5<M){for(C=Math.ceil(M/1e5),g.CTi=[],w=1;w<C;w++)p=void 0,p=w===C-1?{startIndex:1e5*w,endIndex:M-1}:{startIndex:1e5*w,endIndex:1e5*(w+1)-1},g.CTi.push(p);g.Yut(!0),g.zTi(I,m,0,99999,t,e,n,i,o,u,r,D,j,v),g.Vut(1e5/M),setTimeout(function(){a.asyncCreateFilterData(I,m,g.CTi,t,e,n,i,o,u,r,D,j,v,M)},0)}else g.zTi(I,m,0,M-1,t,e,n,i,o,u,r,D,j,v)}},l.prototype.mTi=function(a,c,t,e,E,s,l,f,d,h){var n,g,b,v,n,M,i,o,u,r,I,o,m,N,D,j,C,w,p,x,y,A,T,S,z,L,O,k,n=this.vMi,g=this,b={},v=n.getDataView(),n=v.gLt._dataSource.options.schema,i=g.filterButtonInfo.rowFilter,o=i.Mct,I=i.isFiltered(c)&&(0,Z.isSpecificCondition)(i.rrt[c][0],g),o=0===o.length||i.isFiltered(c)&&1===o.length;if(n&&n.columns&&n.columns[a]&&(M=n.columns[a].dataType),m=v.iRt,D=(N=v.bFt()).length,o)i.tAi=N;else{for(u=v.Jce||{},j=v.ife(c),r=G(u).filter(function(t){var e,e;return t!==j&&((e=(e=u[t].options[0])&&void 0!==e.Vce)&&(C=t),!e)}),x=C,y=p=w=0;y<r.length;y++)((A=u[r[y]].count)<w||0===y)&&(w=A,x=r[p=y]);for(L in r.splice(p,1),T=function(t){for(var e=0;e<r.length&&u[r[e]][t];e++);return e===r.length},S=[],z=u[x])T(L)&&(O=+L,Number.isNaN(O)||S.push(O));v.iRt=S,v.yFt(),S=v.iRt,v.iRt=m,D=(N=S).length,i.tAi=S}for(k=function(t){var e,n,e,e,i,o,u,r,e,n=N[t],e=v.getRowData(n,!0,((e={})[a]=!0,e))[a];U(M)&&!U(e)&&(M=typeof e),i=(e=g.LTi(e,b,c,function(){return{row:t,isCheckedInCheckList:!I&&-1<m.indexOf(n),index:n}},d,h,M)).row,o=e.filterData,u=e.filterText,r=e.type,e=e.isCheckedInCheckList,g.Pst({},s,l,i,c,f,!0,o,u,r,e)},y=0;y<D;y++)k(y)},l.prototype.zTi=function(a,c,t,e,s,n,i,l,f,d,h,g,b,v){var o,M,I,u,r,m,N,D,j,C,w,r,p,u,x,y,A,T,S,o=this.vMi,M=this,I={},u=M.filterButtonInfo.rowFilter,r=u.Mct,j=u.isFiltered(s)&&(0,Z.isSpecificCondition)(u.rrt[s][0],M),C=0===r.length||u.isFiltered(s)&&1===r.length;for(C?(u=(w=o.getDataView()).lFt)&&0<u.length&&(D=u.reduce(function(t,e){return t[e]=!0,t},{})):(w=o.getDataView(),m=w.Jce||{},r=w.kve(s),p=r.isGroup&&z(r._Ft)?w.ife(r._Ft):w.ife(r),N=G(m).filter(function(t){return t!==p})),x=function(t){var e,n;if(U(D))return!0;for(e=a[t],n=0;n<e.length;n++)if(!D[e[n]])return!0;return!1},y=function(t){for(var e,n,i,e=a[t],n=0;n<e.length;n++){for(i=0;i<N.length&&m[N[i]][e[n]];i++);if(i===N.length)return!0}return!1},A=function(t,e){return!!t||(C?x:y)(e)},T=function(t){var e,n,t,i,o,u,r,t,e=c[t],n,t=M.LTi(e,I,s,function(){return M.xTi(a[e],M.wTi)},g,b,v),i=t.row,o=t.filterData,u=t.filterText,r=t.type,t=t.isCheckedInCheckList;M.Pst(l,f,d,i,s,h,!0,o,u,r,!j&&t,A(t,e))},S=t;S<=e;S++)T(S)},l.prototype.LTi=function(t,e,n,i,o,u,r){var a,c,s,l,c,i,f,d,i,h,g,b,v,M,M,b,a=this.vMi,c=this,s=a.Mf,l="string";return t!==I&&t!==m||(t=N,l="blank"),t&&"string"!==r&&("number"!==r&&"boolean"!==r||(isNaN(parseFloat(t))||(t=parseFloat(t)),l="number"),"object"!==r&&"date"!==r||t.match(D)&&!isNaN(Date.parse(t))&&(t=new Date(t),c.Jut=!0,l="date"),"string"===l)&&(this.cze=!0),c=t,f=(i=i()).row,d=i.isCheckedInCheckList,i=i.index,h=!1,("string"!=typeof t||u)&&U(f)&&"string"==typeof u&&a.Mf.parent&&(v=!(b=(g=void 0,Z.isFormulaFormatter)(u))&&(0,Z.hasATWithoutBrackets)(u),(b||v)&&(M=a.Mf.parent.xO(u))&&(h=!0,g=M.getExpression()),h)&&(b?c=a.getDataView().sPt(g,i):v&&(c=a.getDataView().jfe(g,i,(0,Z.getColumnName)(a,n))),U(c)||"object"!=typeof c||(c=U(c._error)?c.toString():c._error)),h||void 0!==(M=o.format(t,u,e,{sheet:s,row:f,col:n,sheetArea:3,quotePrefix:null}))&&c!==M&&(c=M),"boolean"===r&&("TRUE"===(b=String(t).toUpperCase())&&(t=!0,c="TRUE"),"FALSE"===b)&&(t=!1,c="FALSE"),{row:f,filterData:t,filterText:c=c&&"string"==typeof c?c.replace(/\r\n?/g,"\n"):c,type:l,isCheckedInCheckList:d}},l.prototype.yTi=function(t){for(var e,n,e=[],n=0;n<t.length;n++)e[t[n]]=n;return e},l.prototype.xTi=function(t,e){for(var n,i,o,o=0;o<t.length;o++)if((i=e[n=t[o]])!==x)return{row:i,isCheckedInCheckList:!0};return{row:e[n=t[0]],isCheckedInCheckList:!1,index:n}},l.prototype.qut=function(){var t,e,n,i,o,u,r,a,c,s,l,f,d,h,g,b,v,t=this.vMi,e=this,n=e.ast.outlineColumn.getCheckStatus(),i=0,o=e._at||e.Dat,u=e.Eat,a=0;if(e.Bat=1===n[0],"add_current"===e.dataSource[0].key&&(i=1),e.Dat=[],c=t.qNi(),s="add_current"===e.dataSource[0].key,c&&s&&e.Bat)for(e.Bat=!1,l=0;l<u.length;l++)1===(f=u[l]).status&&e.Dat.push({text:f.key,value:f.value,type:f.type});for(l=0;l<u.length&&(r=u[l],s||(r.checkedStatus=n[l]),!(a>=o.length));l++)if(r.key===o[a].text){for(a++,d=r.children,h=x,g=0;g<d.length;g++)b=d[g],v=n[i],!1!==b.show&&(i+=1),h=v||h,3!==v&&(b.status=v);2!==h?(e.Dat.push({text:r.key,value:r.value,type:r.type}),c&&(s?1!==h&&e.Dat.pop():e.Dat[e.Dat.length-1].eAi=d[0].level)):e.lst(l,r)}},l.prototype.asyncCreateFilterData=function(t,e,n,i,o,u,r,a,c,s,l,f,d,h){var g,b,v,M,I,m,N,g=this,b=this,v,M;if(this.vMi.Mf.Cut){for(I=new Date,m=new Date;0<n.length&&m-I<=500;)N=n.shift(),b.zTi(t,e,N.startIndex,N.endIndex,i,o,u,r,a,c,s,l,f,d),b.Vut(N.endIndex/h),m=new Date;0<n.length?setTimeout(function(){g.asyncCreateFilterData(t,e,b.CTi,i,o,u,r,a,c,s,l,f,d,h)},0):(b.Lst(o,u,!1,c,s,a,i),b.sut(),b.Yut(!1))}},e.TableSheetFilterDialog=l},"./dist/plugins/tableSheet/tablesheet-filter-utils.js":function(t,e,n){var i,h,o,n,g,u;function r(t){return t instanceof h.Condition}function a(t,e){return r(t)&&!t.art&&!e.jut().isShowSelectCheck}function c(t){return"="===t.charAt(0)}function s(t){var e,n;return{left:t.indexOf("["),right:t.indexOf("]")}}function l(t){var t=s(t),e=t.left,t=t.right;return 0<=e&&e<t}function f(t){return c(t)&&l(t)}function d(t){var e,n;if(f(t=t.trim()))return!0;if(0<=t.indexOf("{{")&&0<=t.indexOf("}}"))for(e=t.split(/{{|}}/),n=0;n<e.length;n++)if(f(e[n].trim()))return!0;return!1}function b(t){var e,n,i,o;if(c(t))for(e=!0,n=0;n<t.length;n++)if(i=t.charAt(n),e&&"@"===i){if("."===(o=t.charAt(n+1))||!o.match(/[\[\s\]]/))return!0}else"["===i?e=!1:"]"===i&&(e=!0);return!1}function v(t,e,n,i,o){var t=t;3!==o||!t.zDi(e)&&!t.vDi(e)||i.locked||M(t,n,i,!0)}function M(t,e,n,i){for(var o,u,r,a,c,s,o,u=t.cNi,r=0;r<u.length;r++)if(0<(a=u[r]).length)for(c=0;c<a.length;c++)if((s=a[c])===e){n.locked=i;break}}function I(t,e){var t=t.getDataView();return t&&t.kve(e)}function m(t,e){var t,e,t=t.getDataView();if(t)return(e=t.kve(e)).isGroup&&u(e._Ft)?t.ife(e._Ft):t.ife(e)}function N(t,e){for(var n=0,i=t.length;n<i;n++)if(e(t[n],n,t))return n;return-1}function D(t,e,n){var i,o,u,r,a,c,n,s,l,f,d,n,o,t,o,i=t.expected(),o=t.conType(),u=n&&n.isDateFieldListFilter;if("string"==typeof i&&(i=i.split(String.fromCharCode(160)).join(" ")),t.art&&o===h.ConditionType.textCondition&&(i=t.urt,g(t.urt)||(o=h.ConditionType.dateCondition,u=!0)),o===h.ConditionType.numberCondition)return[{field:e,operator:(r={0:"eq",1:"neq",2:"gt",3:"gte",4:"lt",5:"lte"})[t.compareType()],value:i,ort:u}];if(o===h.ConditionType.cellValueCondition)return[{field:e,operator:(r={0:"eq",1:"neq",2:"gt",3:"gte",4:"lt",5:"lte"})[t.compareType()],value:i,ort:u}];if(o===h.ConditionType.relationCondition)return a=D(t.item1(),e),(c=D(t.item2(),e))[0].relation=(r={0:"or",1:"and"})[t.compareType()],a.concat(c);if(o===h.ConditionType.textCondition)return[{field:e,operator:(r={0:"eq",1:"neq",2:"startswith",3:"doesnotstartwith",4:"endswith",5:"doesnotendwith",6:"contains",7:"doesnotcontain"})[t.compareType()],value:i,ort:u}];if(o===h.ConditionType.dateCondition)switch(n=t.compareType(),c=a=l=s=void 0,f=i.setHours(0,0,0,0),d=i.setHours(23,59,59,999),n){case 0:return s=new h.Condition(h.ConditionType.numberCondition,{compareType:h.GeneralComparisonOperators.greaterThanOrEqualsTo,expected:f}),l=new h.Condition(h.ConditionType.numberCondition,{compareType:h.GeneralComparisonOperators.lessThanOrEqualsTo,expected:d}),a=D(s,e,{isDateFieldListFilter:u}),(c=D(l,e,{isDateFieldListFilter:u}))[0].relation="and",a.concat(c);case 1:return s=new h.Condition(h.ConditionType.numberCondition,{compareType:h.GeneralComparisonOperators.greaterThan,expected:d}),l=new h.Condition(h.ConditionType.numberCondition,{compareType:h.GeneralComparisonOperators.lessThan,expected:f}),a=D(s,e,{isDateFieldListFilter:u}),(c=D(l,e,{isDateFieldListFilter:u}))[0].relation="or",a.concat(c);case 2:return[{field:e,operator:"lt",value:f,ort:u}];case 3:return[{field:e,operator:"lte",value:d,ort:u}];case 4:return[{field:e,operator:"gt",value:d,ort:u}];case 5:return[{field:e,operator:"gte",value:f,ort:u}];default:return[]}else{if(o===h.ConditionType.top10Condition)return[{field:e,operator:(r={0:"top",1:"bottom"})[t.type()],value:i,ort:u}];if(o===h.ConditionType.averageCondition)return[{field:e,operator:(r={0:"above",1:"below"})[t.type()],value:i,ort:u}];if(o===h.ConditionType.dateExCondition)return(n=t.expectTypeId())===h.DateExConditionExpectType.dateOccurring||n===h.DateExConditionExpectType.yearToDate?(d=f=void 0,n===h.DateExConditionExpectType.dateOccurring?(f=(o=t.getExConditionDateScope(i)).from,d=o.to):(f=new Date,d=new Date,f.setMonth(0,1),f.setHours(0,0,0,0),d.setHours(23,59,59,999)),t=new h.Condition(h.ConditionType.dateCondition,{compareType:h.DateCompareType.afterEqualsTo,expected:f}),o=new h.Condition(h.ConditionType.dateCondition,{compareType:h.DateCompareType.beforeEqualsTo,expected:d}),a=D(t,e),(c=D(o,e))[0].relation="and",a.concat(c)):[{field:e,operator:(r={0:"date",1:"year",2:"quarter",3:"month",4:"week",5:"day",6:"yearToDate"})[n],value:i,ort:u}]}return[]}function j(t){var e=t.col,n=t.rowFilter;return!n.Dit(e)&&n.Dit(n.NTi(e))?i(i({},t),{col:n.NTi(e)}):t}i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var e,n,i,o,n=1,i=arguments.length;n<i;n++)for(o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},Object.defineProperty(e,"__esModule",{value:!0}),e.reuseFilterButtonInfo=e.convertCondition=e.findIndex=e.getColumnName=e.getColumnInfo=e.setInsertedRowCrossStyle=e.hasATWithoutBrackets=e.isFormulaFormatter=e.isSpecificCondition=e.isCondition=void 0,h=n("ConditionalFormatting"),n=(o=n("Common")).Common.lt,g=n.ht,u=n.q8,e.isCondition=r,e.isSpecificCondition=a,e.isFormulaFormatter=d,e.hasATWithoutBrackets=b,e.setInsertedRowCrossStyle=v,e.getColumnInfo=I,e.getColumnName=m,e.findIndex=N,e.convertCondition=D,e.reuseFilterButtonInfo=j},"./dist/plugins/tableSheet/tablesheet-filter.js":function(t,e,n){var i,b,o,u,r,d,v,h,s,n,M,g,f,I,a,c,l,m,N;function D(t,e){var t=N.call(this,t)||this;return t.sTi={},t.MTi={},t.ITi=!1,t.gTi=0,t.act=!1,t.sze=e,t}i=this&&this.__extends||(m=function(t,e){return(m=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}m(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),b=this&&this.__assign||function(){return(b=Object.assign||function(t){for(var e,n,i,o,n=1,i=arguments.length;n<i;n++)for(o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},Object.defineProperty(e,"__esModule",{value:!0}),e.GroupHideRowFilter=void 0,o=n("Common"),u=n("Filters"),r=n("Core"),d=n("ConditionalFormatting"),v=n("./dist/plugins/tableSheet/tableSheet-undo.js"),h=n("./dist/plugins/tableSheet/tablesheet-filter-utils.js"),s=n("./dist/plugins/tableSheet/tablesheet-filter-dialog.js"),n=o.Common.lt,M=n.ht,g=n.q8,f=null,I=void 0,a=-1,c=-2,N=u.HideRowFilter,i(D,N),D.prototype.toJSON=function(){var t=this,e=N.prototype.toJSON.call(this);return e.sortMap=t.sTi,e.relatedColFilters=t.MTi,e.lastGroup=t.ITi,e.lastGroupCount=t.gTi,M(t.lTi)||(e.lTi=t.lTi),e},D.prototype.fromJSON=function(t,e){var n;M(t)||((n=this).sTi=t.sortMap,n.MTi=t.relatedColFilters,n.ITi=t.lastGroup,n.gTi=t.lastGroupCount,M(t.lTi)||(n.lTi=t.lTi),N.prototype.fromJSON.call(this,t,e))},D.prototype.isFiltered=function(t){var e=this;return arguments.length?N.prototype.isFiltered.call(this,t)||N.prototype.isFiltered.call(this,e.NTi(t)):N.prototype.isFiltered.call(this)},D.prototype.Dit=function(t){return N.prototype.isFiltered.call(this,t)},D.prototype.aze=function(t){var e,n,u,n,t,r,a,c,s,i,o,l,f,f,e,n=this.sze,u=this.rrt[t],n=n.getDataView(),t=n.kve(t),r=t.isGroup&&g(t._Ft)?n.ife(t._Ft):n.ife(t);if(u&&0<u.length){if(a=[],(0,h.isCondition)(u[0]))if(u[0].art&&u[0].conType()===d.ConditionType.textCondition)for(o=0;o<u.length;o++)l=u[o].urt,a.push({field:r,value:M(l)?l:l.toISOString()});else for(o=0;o<u.length;o++)f=u[o],f=(0,h.convertCondition)(f,r),a=0===o?f:(f[0].relation="or",a.concat(f));else for(c=[],s=[],i=function(t){var e,n,i,n,o,e,e;if(g(u[t].eAi)){if(e=u[t].eAi,c.push(u[t].value),s.push(e),n=u[t+1],(i=M(n)?-1:n.eAi)<=e)for(n=!0,o=c.slice().reverse(),(n=(e=a[a.length-1])?!((e=e.Vce).length===o.length&&e.every(function(t,e){return t===o[e]})):n)&&a.push({field:r,value:u[t].value,Vce:o});0<s.length&&s[s.length-1]>=i;)c.pop(),s.pop()}else a.push({field:r,value:u[t].value})},o=0;o<u.length;o++)i(o);return a}},D.prototype.Mze=function(){var n,t,e,i,e,o,n=this,t,e=this.sze.Mf,i=this.rrt,e=e.getDataSource(),o=Object.keys(i).filter(function(t){return!M(i[t])}).reduce(function(t,e){return t.concat(n.aze(Number(e)))},[]);e.filter(o)},D.prototype.Ize=function(t,e){var n=this.sze,i=n.Mf;i.suspendPaint(),this.Nct(t,e),n.tDe(),i.resumePaint()},D.prototype.filter=function(t,e){var n,n,i,o,u,r,a,c,s,n=this.sze;if(n&&(i=n.getDataView())){if(o=this.rrt,u=!M(o[t]),n.qNi()&&(r=function(t){var t=o[t];return t&&t.some(function(t){return g(t.eAi)})})(t))for(a=this.Mct,c=0;c<a.length;c++)if(t!==(s=a[c])&&r(s)){this.yct(s),this.Nct(s,!1);break}n.qni.addUndoChanges({type:v.TableSheetUndoType.FilterColumn,changedData:{col:t,columnName:null==(n=n.fDi(t))?void 0:n.name}}),this.Mze(),this.Ize(t,u)}},D.prototype.unfilter=function(t){var e,e,e=this.sze;e&&(e=e.getDataView())&&(e.filter(),this.setFilterItemMap(t),this.Mze(),this.Ize(t,!1))},D.prototype.reFilter=function(t){},D.prototype.openFilterDialog=function(t){var e,n,i,o,o,u,u,u,r,o,a,c,e=this.sze,n=this,i=n.Mf,o=t.col,o=e.getDataView().kve(o);this.irt=!1,this.ort=!1,u=0<((u=e.getDataView())&&u.length()||0),n.Hct(i)&&u&&(u={sortByValue:!1!==o.allowSort,filterByValue:!1!==o.allowFilterByValue,listFilterArea:!1!==o.allowFilterByList,sortByColor:!1,filterByColor:!1},n.filterDialogVisibleInfo(u),r=new s.TableSheetFilterDialog(i.parent.j2(),i,(0,h.reuseFilterButtonInfo)(t),u,n.dialogOptions,e),i.Cut=r,(o=e.qNi())&&(a=i.outlineColumn.Un,i.outlineColumn.Un=function(){return!0},c=r.close,r.close=function(){i.outlineColumn.Un=a,r.close=c,r.close()},r.STi=function(){for(var t,e,n,i,t=this,e=t.dataSource,n=t.Eat,i=0;i<e.length;i++)n[i].checkedStatus=e[i].status}),r.rQ(),o&&r.STi(),r.cze)&&r.yut()},D.prototype.Nct=function(t,e){var n=this;e&&n.DFt(t)?n.MTi[t]=n.NTi(t):M(n.MTi[t])||delete n.MTi[t],N.prototype.Nct.call(this,t,e)},D.prototype.setFilterItemMap=function(t,e){var n=this;n.rrt[t]=e||I},D.prototype.removeFilterItems=function(t,e){var n,i,o,n=this.sze;n&&(o=(i=n.Mf).getDataSource(),n.nAi&&o?N.prototype.removeFilterItems.call(this,t,e):(this.setFilterItemMap(t),this.Cct(t),n.yno&&o&&o.filter()))},D.prototype.DTi=function(t){for(var e,n,i,o,u,r,a,c,c,s,l,e=this,o=e.Mct,u=e.rrt,r=e.hst,a=0;a<o.length;a++)n=o[a],0<=(i=(M(e.MTi[n])?n:e.MTi[n])+t)?(o[a]=i,c=u[n],M(c)||(u[n]=f,u[i]=c),r&&(c=r[n],M(c)||(delete r[n],r[i]=c))):(o[a]=-1,u[n]=f,r&&delete r[n]);e.Mct=o.filter(function(t){return-1!==t}),o.some(function(t){return-1===t})&&((l=(s=e.Mf).NMi).suspendPaint(),l.resumePaint())},D.prototype.getSortState=function(t){var e=this;return e.GFt(t)&&(t=e.NTi(t)),e.nrt=e.wot(t),N.prototype.getSortState.call(this,t)},D.prototype.Gze=function(){var t=this,e=t.jFt(),n=t.gTi;e?(t.gTi=t.dTi(),n=t.gTi-n,t.DTi(n),t.hTi(-1,n)):(t.gTi=0,t.DTi(-n),t.hTi(-2,-n),t.TTi())},D.prototype.hTi=function(t,e){var n,i,o,u,n=this,i=n.sTi[t];for(u in i)i[u]&&((o=i[u]).index+=e,n.setSortInfo(o))},D.prototype.TTi=function(){var t,e,e=this;e.sTi[-2]?e.sTi=((t={})[-1]=e.sTi[-1],t):e.sTi={}},D.prototype.eIi=function(){return this.Mf.getDataSource()},D.prototype.HMi=function(){return this.Mf.getDataSource().getColumnInfos()},D.prototype.fDi=function(t){return this.Mf.NMi.fDi(t)},D.prototype.Teo=function(){return this.Mf.NMi.Teo()},D.prototype.dTi=function(){return this.HMi().filter(function(t){return t.isGroup}).length},D.prototype.jFt=function(){return this.Mf.NMi.jFt},D.prototype.bTi=function(t){var t=this.fDi(t);return t&&t.OFt},D.prototype.GFt=function(t){var t=this.fDi(t);return t&&t.GFt},D.prototype.DFt=function(t){var t=this.fDi(t);return t&&t.DFt},D.prototype.tNi=function(){return this.Mf.NMi},D.prototype.NTi=function(t,e){var n=this.fDi(t);return n&&(this.Teo()&&!e?t:n._Ft)},D.prototype.Sze=function(t){var e,n,n,i,o,u,r,e=this,n=e.ATi(t),n=e.sTi[n],i=e.sTi[-2],o=n&&n[t],u=i&&i[t];(o||u)&&(r=e.tNi()).qni.addUndoChanges({type:v.TableSheetUndoType.SortMapChanged,oldData:e.Dze()}),n&&n[t]&&delete n[t],i&&i[t]&&delete i[t]},D.prototype.wot=function(t){var e=this,n=e.ATi(t),e=e.sTi[n];return e&&e[t]?e[t]:f},D.prototype.ATi=function(t){var e=this,e;return e.jFt()&&(e=e.bTi(t),!M(e))?e:-1},D.prototype.xno=function(){var t,e,n,i,o,u,r,t,e=this.sTi,n=[];for(i in e)if(-2!=+i)for(u in o=e[i])o.hasOwnProperty(u)&&(r=o[u],n.push(r));return n},D.prototype.Gct=function(t){return arguments&&0<arguments.length?this:N.prototype.Gct.call(this)},D.prototype.Dze=function(){var t;return null!=(t=(0,r.RF)(this.sTi))?t:{}},D.prototype.Nze=function(t){this.sTi=t},D.prototype.qO=function(r,a,t){var c,e,s,n;for(n in N.prototype.qO.call(this,r,a,t),c=this.sTi,e=function(t){var e,n,i,o,u;if(c.hasOwnProperty(t)){for(i in n=[],e=c[t])e.hasOwnProperty(i)&&(o=e[i]).index>=r&&(s.GFt(u=+i+a)?o.index=s.NTi(u,!0):o.index+=a,n.push({col:i,sortInfo:o}));n.forEach(function(t){var t=t.col;delete e[t]}),n.forEach(function(t){var t=t.sortInfo;e[t.index]=t}),0===Object.keys(e).length&&delete c[t]}},s=this,c)e(n)},D.prototype.NP=function(a,c){var s,t,l,e;for(e in N.prototype.NP.call(this,a,c),s=this.sTi,t=function(t){var e,n,i,o,u,r;if(s.hasOwnProperty(t)){for(i in n=[],e=s[t])e.hasOwnProperty(i)&&(u=(o=e[i]).index,r=a+c,a<=u&&u<r?l.DFt(i)?(o.index=l.NTi(i,!0),o.index-=c,n.push({col:i,sortInfo:o})):n.push({col:i}):r<=u&&(o.index-=c,n.push({col:i,sortInfo:o})));n.forEach(function(t){var t=t.col;delete e[t]}),n.forEach(function(t){var t=t.sortInfo;t&&(e[t.index]=t)}),0===Object.keys(e).length&&delete s[t]}},l=this,s)t(e)},D.prototype.vno=function(t){this.setFilterItemMap(t)},D.prototype.setSortInfo=function(t){var e,n,i,o,n=this,i=t.index;n.GFt(i)&&(i=n.NTi(i),t.index=i),o=n.ATi(i),n.sTi[o]=((e={})[i]=t,e),n.vTi(t,o)},D.prototype.vTi=function(t,e){var e,n,i,n=this,i=t.index;-1===e||n.DFt(i)||n.GFt(i)?(n.DFt(i)&&(i=n.NTi(i)),n.sTi[-2]=((e={})[i]=b(b({},t),{index:i}),e)):delete n.sTi[-2]},D.prototype.BDi=function(){var t,i,t,o,u,r,t,t=this.Mf,i=t.getDataSource(),t=t.rowFilter(),o=t&&t.sTi,u={};return o&&((t=function(t){var e,n,e=o[t];if(!M(e))for(n in u[t]={},e)e.hasOwnProperty(n)&&(r=i.uPt(n),u[t][r]=n)})(r=-1),t(-2)),u},D.prototype.RDi=function(a){var t,c,e,s,t,t=this.Mf,c=t.getDataSource(),e,s=t.rowFilter().sTi,t=function(t){var e,n,i,o,i,u,r,e=a[t];for(n in e)e.hasOwnProperty(n)&&(o=+e[i=+n],(i=c.BPt(i))!==o)&&(u=s[t],M(u)||(r=u[o],M(r))||(delete u[o],u[r.index=i]=r))};t(-1),t(-2)},D.prototype.nTi=function(t){var a,c,s,e,n,i,o,u,r,l,f,d,h,g,h;if(t&&0<t.length){for(a=this,c=[],s=[],e=t.map(function(t){return{oldIndex:t.oldIndex,newIndex:t.newIndex}}),n=function(t){var e,n,i,i,t,o,u,r,i,e=t.oldIndex,n=b({},t);for(o in s.push(n),-1<(i=a.Mct.indexOf(e))&&c.push([i,t.newIndex]),i=a.rrt[e],M(i)||(n.filterItemMapValue=i,a.rrt[e]=I),t=a.hst&&a.hst[e],!M(t)&&0<t.length&&(n.filteredColumnCacheValue=t,delete a.hst[e]),a.MTi)a.MTi.hasOwnProperty(o)&&(u=a.MTi[o])===e&&(n.relatedColFiltersValue=o,delete a.MTi[o]);M(a.sTi)||(r=a.sTi,(i=function(t){var t=r[t];M(t)||M(t[e])||(n.sortMapValue=t[e],delete t[e])})(-1),i(-2))},i=0,o=t;i<o.length;i++)n(u=o[i]);for(r=function(e){var n,t,i,o,u,r,t,n=e.newIndex,t=e.filterItemMapValue,i=e.filteredColumnCacheValue,o=e.relatedColFiltersValue,u=e.sortMapValue;M(t)||(a.rrt[n]=t),M(i)||(a.hst[n]=i),M(o)||(a.MTi[o]=n),M(a.sTi)||M(u)||(r=a.sTi,(t=function(t){var t=r[t];M(t)||(t[e.sortMapValue.index=n]=e.sortMapValue)})(-1),t(-2))},l=0,f=s;l<f.length;l++)r(u=f[l]);for(d=0;d<c.length;d++)g=(h=c[d])[0],h=h[1],a.Mct[g]=h;this.tNi().qni.addUndoChanges({type:v.TableSheetUndoType.UpdateFilterOnMoved,changedData:e})}},D.prototype.VIi=function(n){var e,i,o,t,u,r,a,e=this,i=e.eIi(),o=[],t=e.HMi(),u=[];t.forEach(function(t,e){t.RBt===n&&o.push(i.BPt(e))}),o.forEach(function(t){e.gze(t)}),a=o[(r=o.length)-1],0<r&&t.forEach(function(t,e){var e=i.BPt(e);a<e&&u.push({oldIndex:e,newIndex:e-r})}),e.nTi(u)},D.prototype.gze=function(n){var t,t,e,i,t,o,u,r,t,e=this,i={index:n,filterItemMap:{oldValue:null==(t=e.rrt)?void 0:t[n]},filteredColumns:{},filteredColumnCache:{oldValue:null==(t=e.hst)?void 0:t[n]},relatedColFilters:[],sortMap:[]};for(o in e.rrt[n]&&(e.rrt[n]=I,t=e.Mct.indexOf(n),i.filteredColumns={index:t,oldValue:e.Mct[t]},e.Mct.splice(t,1)),e.hst&&e.hst[n]&&delete e.hst[n],e.MTi)e.MTi.hasOwnProperty(o)&&(u=e.MTi[o])===n&&(i.relatedColFilters.push({col:o,value:e.MTi[o]}),delete e.MTi[o]);M(e.sTi)||(r=e.sTi,(t=function(t){var e=r[t];M(e)||M(e[n])||(i[t].push({level:t,index:n,value:e[n]}),delete e[n])})(-1),t(-2)),this.tNi().qni.addUndoChanges({type:v.TableSheetUndoType.RemoveCrosRowFilterInfo,changedData:i})},D.prototype.oze=function(t){var e,n,i,o,u,u,t,r,a,e=t.index,n=t.filterItemMap,i=t.filteredColumnCache,o=t.filteredColumns,u=t.relatedColFilters,u=void 0===u?[]:u,t=t.sortMap,r=void 0===t?[]:t,a=this;a.rrt&&(a.rrt[e]=n.oldValue),M(null==o?void 0:o.index)||(a.Mct[o.index]=o.oldValue),a.hst&&(a.hst[e]=i.oldValue),u.forEach(function(t){var e=t.col,t=t.value;a.MTi[e]=t}),r.forEach(function(t){var e=t.level,n=t.index,t=t.value;r&&r[e]&&(r[e][n]=t)})},D.prototype.UIi=function(){var i,t,o,u,e,t,i=this,t=this,o=t.eIi(),u=t.HMi();this.lTi={},Object.keys(this.rrt).forEach(function(t){var e,e;M(i.rrt[t])||(e=o.uPt(t),e=u[e].value,M(i.lTi[e])&&(i.lTi[e]={}),i.lTi[e].filterItemMap=i.rrt[t])}),M(this.hst)||Object.keys(this.hst).forEach(function(t){var e,e,e=o.uPt(t),e=u[e].value;M(i.lTi[e])&&(i.lTi[e]={}),i.lTi[e].filteredColumnCache=i.hst[t]}),Object.keys(this.MTi).forEach(function(t){var e,n,e=o.uPt(t),n=u[e].value;M(i.lTi[n])&&(i.lTi[n]={}),e=o.uPt(i.MTi[t]),i.lTi[n].relatedColFilter=u[e].value}),M(this.sTi)||(e=this.sTi,(t=function(t){var n=e[t];M(n)||Object.keys(n).forEach(function(t){var e,e,e=o.uPt(t),e=u[e].value;M(i.lTi[e])&&(i.lTi[e]={}),M(i.lTi[e].sortMap)&&(i.lTi[e].sortMap={}),i.lTi[e].sortMap=n[t]})})(-1),t(-2)),0===Object.keys(this.lTi).length&&delete this.lTi},D.prototype.QIi=function(){var u,t,e,r,a,c,u=this,t=this,e=t.Mf,r=t.eIi(),a=this.lTi,c=t.HMi();M(a)||(delete t.lTi,this.rrt=[],this.Mct=[],this.hst={},this.MTi={},M(this.sTi)||(M(this.sTi[-1])||(this.sTi[-1]={}),M(this.sTi[-2]))||(this.sTi[-2]={}),Object.keys(a).forEach(function(e){var n,i,o,t,n=a[e],i=(0,h.findIndex)(c,function(t){return t.value===e});M(i)||(i=r.BPt(i),M(n.filterItemMap)||(u.rrt[i]=n.filterItemMap,u.Mct.push(i),u.hst[i]=n.filteredColumnCache),M(n.relatedColFilter)||(u.MTi[i]=r.BPt((0,h.findIndex)(c,function(t){return t.value===n.relatedColFilter}))),M(u.sTi))||M(n.sortMap)||(o=u.sTi,(t=function(t){var e;o[t][n.sortMap.index=i]=n.sortMap})(-1),t(-2))}),e.repaint())},e.GroupHideRowFilter=D},"./dist/plugins/tableSheet/tablesheet-override.js":function(t,e,n){var i,o,g,u,r,a,c,s,b,v,n,l,a,M,I,f,m,d,h,N,D;function j(t,e,n){var i=D.call(this,t,e,n,[new g.Range(-1,-1,-1,-1)])||this;return i.nT=g.SheetArea.colHeader,Object.defineProperty(i,"sheetArea",{get:function(){return i.nT}}),i}function C(t){return{field:t.rYt,ascending:t.ascending,compare:t.compareFunction}}function w(t){for(var e,n,i,o,e,n=0,i=f(h);n<i.length;n++)o=i[n],h[o].call(t)}function p(t){var t=t.Mf;t.hA=g.ClipboardPasteOptions.values,t.lF=g.CopyToOptions.value,t.JT=!1,t.UB=!1}function x(t,e,n,i,o,u){var r,a,i,a,n,r,a,i,c,s,l,a,r=t.NMi;return r.isAlternatingRowStyleColumn(n)&&(a=r.FDi(n),r)&&(M(i)||i===g.SheetArea.viewport)&&!t.oNi&&!a&&0<=e&&0<=n&&(i=r.options.alternatingRowOptions)&&(a=i.step||[1,1],n=M(a[0])?1:a[0],r=M(a[1])?1:a[1],a=i.style,!M(a))&&(c=i=void 0,Array.isArray(a)?(i=a[0],c=a[1]):i=a,u=!0,a=void 0,a=(l=Math.floor(e%(s=n+r)))<n?i:c)&&(o.h5(a,!1,45),(0,g.composeParentStyle)(a,o,t,!1,45,!0)),u}i=this&&this.__extends||(N=function(t,e){return(N=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}N(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),Object.defineProperty(e,"__esModule",{value:!0}),e.overrideProperties=e.overrideMethods=e.ColumnHeaderStateRule=e.SR=void 0,o=n("Common"),g=n("Core"),u=n("ConditionalFormatting"),r=n("./dist/plugins/tableSheet/tableSheet.interface.js"),a=n("./dist/plugins/tableSheet/tableSheet.res.en.js"),c=n("./dist/plugins/tableSheet/tableSheet-grouping.js"),s=n("./dist/plugins/tableSheet/tablesheet-filter.js"),b=n("./dist/plugins/tableSheet/tablesheet-filter-utils.js"),v=n("./dist/plugins/tableSheet/tableSheet-undo.js"),e.SR={en:a},n=new o.Common.ResourceManager(e.SR,"TableSheet"),l=n.getResource.bind(n),a=o.Common.lt,M=a.ht,I=a.q8,f=Object.keys,m=null,D=u.StateRule,i(j,D),e.ColumnHeaderStateRule=j,h={aL:function(){var t,e,n,t,e=this.Mf,n=new s.GroupHideRowFilter(new g.Range(-1,-1,-1,-1),this);n.filterDialogVisibleInfo({sortByValue:!0,sortByColor:!1,filterByColor:!1,filterByValue:!0,listFilterArea:!0}),e.rowFilter(n)},OTi:function(){var h=this,g=h.Mf;g.sortRangeOriginal=g.sortRange,g.sortRange=function(t,e,n,i,o,u,r,a,c){var s,l,f,d,u,s,l=this.getDataSource();return!!l&&(d=(f=g.rowFilter()).Dze(),u=u.map(function(t){return f.setSortInfo(t),t.rYt=h.dDi(t.index),C(t)}),f.jFt()&&(u=f.xno().map(C)),l.sort(u),this.qni.addUndoChanges({type:v.TableSheetUndoType.SortMapChanged,oldData:d}),!0)}},ETi:function(){var t,o,i,n,t=this,o=this,i=o.Mf;i.onItemRemoved=function(t){this.zw()},i.onItemGotten=function(t,e,n){var i;return t&&t.isGroup&&!M(t.OFt)?o.keo()===r.GroupLayoutMode.tabular&&o.DMi(t.OFt,e)?n:m:o.VDi(e)?n:void 0},i.TF=function(){return t},i.hP=function(){var t=this.parent;return!t||t.getActiveSheetTab()===this.NMi},i.FT=function(){return l().Exp_InvalidOperationInProtectForTableSheet},M(i.UTi)&&(i.UTi=i.jT,i.jT=function(){for(var t,e,n,t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];this.sw&&((n=this.C6(this.sw.rowViewportIndex,this.sw.colViewportIndex))===g.SheetArea.rowHeader||n===g.SheetArea.corner)||i.UTi.apply(i,t)}),i.Ci=function(t,e,n){var t,t=n===g.SheetArea.rowHeader&&o.CDi(t,e)?o.ODi(t):i.CP(t,e,n);return t},n=i.AO.bind(i),i.AO=function(){var t=o.getDataView(),e;return 0<(t&&t.length()||0)?n():[]}},GTi:function(){var d,h,d=this,h=this.Mf;h.getCompositeStyle=function(t,e,n,i,o,u,r){var i,a,c,s,l,f,i,o;if(void 0===r&&(r={}),M(n)&&(n=3),a=this,c=new g.Style,s=!1,l=d.nIe(t,e,n),0!==n){if(s=a.IL(t,e,n,c,s,i),r.hasTableStyle)return c;s=a.xL(t,e,n,c,s,r.table,r.isForFilter),s=(i=a.NL(t,e,n,c,s,r.pivotTable,r.isForFilter,r.pivotTableStyle)).changed,c=i.destInfo,s=a.kL(t,n,c,s,o),(f=1===n&&t<h.Ut.getFreeAreaRowCount(n))||l||(s=a.DL(e,n,c,s,u)),i=I(r.rowSpacingIndex),l||i||(s=x(a,t,e,n,c,s))}return a._L(n,c,s,r.ignoreSheet),(0,b.setInsertedRowCrossStyle)(d,t,e,c,n),l&&(c.cellButtons=[],c.locked=!0),3===n&&d.iDi(t,e)&&(c.locked=!0),3!==n||!d.zDi(t)||M(u)||M(u.formatter)||(o=void 0,(o="string"==typeof u.formatter?u.formatter:u.formatter.formatCached)&&-1<o.indexOf("=")&&(c.formatter=m)),c}},l0t:function(){var r,u,o,t,a,r=this,u=r.Mf,o=u._1,t=o.getStateMethods;t[4]=function(t,e,n,i,o){if(n)return!1;if(o===g.SheetArea.colHeader){if(!(0,c.nMi)(this,t,e))return!1;var n=(0,b.getColumnInfo)(r,e);if(n)return n.readonly}return r.pNi(e)},t[1024]=function(t,e,n,i,o){return!n&&o===g.SheetArea.colHeader&&!!(0,c.nMi)(this,t,e)&&e<u.frozenColumnCount()},t[2048]=function(t,e,n,i,o){var n;return!n&&o===g.SheetArea.colHeader&&!!(0,c.nMi)(this,t,e)&&!!(n=(0,b.getColumnInfo)(r,e))&&n.isPrimaryKey},t[4096]=function(t,e,n,i,o){var n,u;return!n&&o===g.SheetArea.colHeader&&!!(0,c.nMi)(this,t,e)&&!!(n=(0,b.getColumnInfo)(r,e))&&(u=!(n.isPrimaryKey||n.readonly))&&n.required},t[g.RowColumnStates.dirty]=function(t,e,n){var i=r.getDataView();return!(!n||!i)&&(i.isUpdated(t)||i.isInserted(t))},t[g.RowColumnStates.inserted]=function(t){var e=r.getDataView();return e&&e.isInserted(t)},t[256]=function(t){var e=r.getDataView();return e&&e.isUpdated(t)},a=t[g.RowColumnStates.selected],t[g.RowColumnStates.selected]=function(t,e,n,i){if("function"==typeof a)return a.call(o.context,t,e,n,i)||r.aDi(t)}}},e.overrideMethods=w,e.overrideProperties=p},DataManager:function(t){t.exports=e.Data},Common:function(t){t.exports=e.Spread},CalcEngine:function(t){t.exports=e.Spread.CalcEngine},Core:function(t){t.exports=e.Spread.Sheets},AutoMerge:function(t){t.exports=e.Spread.Sheets.AutoMerge},Bindings:function(t){t.exports=e.Spread.Sheets.Bindings},SheetsCalc:function(t){t.exports=e.Spread.Sheets.CalcEngine},CellTypes:function(t){t.exports=e.Spread.Sheets.CellTypes},ConditionalFormatting:function(t){t.exports=e.Spread.Sheets.ConditionalFormatting},Validation:function(t){t.exports=e.Spread.Sheets.DataValidation},Filters:function(t){t.exports=e.Spread.Sheets.Filter},FormulaTextBox:function(t){t.exports=e.Spread.Sheets.FormulaTextBox},Print:function(t){t.exports=e.Spread.Sheets.Print},StatusBar:function(t){t.exports=e.Spread.Sheets.StatusBar},Tables:function(t){t.exports=e.Spread.Sheets.Tables}},i={},t=o("./dist/plugins/tableSheet/tableSheet.entry.js"),(((e=void 0===e?{}:e).Spread=e.Spread||{}).Sheets=e.Spread.Sheets||{}).TableSheet=t}(),e.Spread.Common.CultureManager.reloadResources&&e.Spread.Common.CultureManager.reloadResources("TableSheet")});