{% extends "layout/default.html" %}

{% block main_content %}
<div class="page-content">
    <h3 class="header smaller lighter">InStock股票系统</h3>
    <div class="clearfix">
        <div class="pull-left tableTools-container">
            <h3>功能介绍</h3>
        </div>
    </div>
     <p dir="auto" class="red">一：综合选股</p>
    <div class="snippet-clipboard-content notranslate position-relative overflow-auto">
        <pre class="notranslate"><code>
            综合选股支持股票范围、基本面、技术面、消息面、人气指标、行情数据等方面共200多个信息栏目进行自由组合选股。选股条件分为以下大类：
            1.股票范围
            市场、 行业、地区、 概念、 风格、指数成份、 上市时间。
            2.基本面
            估值指标、每股指标、盈利能力、成长能力、资本结构与偿债能力、股本股东。
            3.技术面
            MACD金叉、KDJ金叉、放量突破、低位资金净流入、高位资金净流出、向上突破均线、均线多头排列、均线空头排列、连涨放量、下跌无量、一根大阳线、两根大阳线、旭日东升、强势多方、炮拨云见日、七仙女下凡(七连阴)、八仙过海(八连阳)、九阳神功(九连阳)、四串阳、天量法则、放量上攻、穿头破脚、倒转锤头、射击之星、黄昏之星、曙光初现、身怀六甲、乌云盖顶、早晨之星、窄幅整理。
            4.消息面
            公告大事、机构关注情况、机构持股家数、机构持股比例。
            5.人气指标
            股吧人气排名、人气排名变化、人气排名连涨、人气排名连跌、人气排名创新高、人气排名创新低、新晋粉丝占比、铁杆粉丝占比、7日关注排名、今日浏览排名。
            6.行情数据
            股价表现、成交情况、资金流向、行情统计、沪深股通。
        </code></pre>
    </div>

    <p dir="auto" class="red">二：股票每日数据</p>
    <div class="snippet-clipboard-content notranslate position-relative overflow-auto">
        <pre class="notranslate"><code>
            包括每日股票数据、股票资金流向、股票分红配送、股票龙虎榜、股票大宗交易、股票基本面数据、行业资金流向、概念资金流向、早盘抢筹数据、尾盘抢筹数据、涨停原因揭密、每日ETF数据。

            抓取A股票每日数据，主要为一些关键数据，同时封装抓取方法，方便扩展系统获取个人关注的数据。
        </code></pre>
    </div>


    <p dir="auto" class="red">三：计算股票指标</p>
    <div class="snippet-clipboard-content notranslate position-relative overflow-auto">
        <pre class="notranslate"><code>
            基于talib、pandas 计算指标，计算高效准确。调整个别指标公式，确保结果和同花顺、通信达结果一致。 指标：

            1、MACD 2、KDJ 3、BOLL 4、TRIX，TRMA 5、CR 6、SMA 7、RSI
            8、VR，MAVR 9、ROC 10、DMI，+DI，-DI，DX，ADX，ADXR 11、W&R
            12、CCI 13、TR、ATR 14、DMA、AMA 15、OBV 16、SAR 17、PSY
            18、BRAR 19、EMV 20、BIAS 21、TEMA  22、MFI 23、VWMA
            24、PPO 25、WT 26、Supertrend  27、DPO  28、VHF  29、RVI
            30、FI 31、ENE 32、STOCHRSI
        </code></pre>
    </div>

    <p dir="auto" class="red">四：根据指标判定可能买入卖出的股票</p>
    <div class="snippet-clipboard-content notranslate position-relative overflow-auto">
        <pre class="notranslate"><code>
            KDJ:
            1、超买区：K值在80以上，D值在70以上，J值大于90时为超买。一般情况下，股价有可能下跌。投资者应谨慎行事，局外人不应再追涨，局内人应适时卖出。
            2、超卖区：K值在20以下，D值在30以下为超卖区。一般情况下，股价有可能上涨，反弹的可能性增大。局内人不应轻易抛出股票，局外人可寻机入场。

            RSI:
            1、当六日指标上升到达80时，表示股市已有超买现象，如果一旦继续上升，超过90以上时，则表示已到严重超买的警戒区，股价已形成头部，极可能在短期内反转回转。
            2、当六日强弱指标下降至20时，表示股市有超卖现象，如果一旦继续下降至10以下时则表示已到严重超卖区域，股价极可能有止跌回升的机会。

            CCI:
            1、当CCI＞﹢100时，表明股价已经进入非常态区间——超买区间，股价的异动现象应多加关注。
            2、当CCI＜﹣100时，表明股价已经进入另一个非常态区间——超卖区间，投资者可以逢低吸纳股票。

            CR:
            1、跌穿a、b、c、d四条线，再由低点向上爬升160时，为短线获利的一个良机，应适当卖出股票。
            2、CR跌至40以下时，是建仓良机。

            WR:
            1、当％R线达到20时，市场处于超买状况，走势可能即将见顶。
            2、当％R线达到80时，市场处于超卖状况，股价走势随时可能见底。

            VR:
            1、获利区域160－450根据情况获利了结。
            2、低价区域40－70可以买进。
        </code></pre>
    </div>

    <p dir="auto" class="red">五：K线形态识别</p>
    <div class="snippet-clipboard-content notranslate position-relative overflow-auto">
        <pre class="notranslate"><code>
            K线形态识别返回的结果有三种： 负：出现卖出信号 0：没有出现该形态 正：出现买入信号

            1、两只乌鸦
            三日K线模式，第一天长阳，第二天高开收阴，第三天再次高开继续收阴， 收盘比前一日收盘价低，预示股价下跌。

            2、三只乌鸦
            三日K线模式，连续三根阴线，每日收盘价都下跌且接近最低价， 每日开盘价都在上根K线实体内，预示股价下跌。

            3、三内部上涨和下跌
            三日K线模式，母子信号+长K线，以三内部上涨为例，K线为阴阳阳， 第三天收盘价高于第一天开盘价，第二天K线在第一天K线内部，预示着股价上涨。

            4、三线打击
            四日K线模式，前三根阳线，每日收盘价都比前一日高， 开盘价在前一日实体内，第四日市场高开，收盘价低于第一日开盘价，预示股价下跌。

            5、三外部上涨和下跌
            三日K线模式，与三内部上涨和下跌类似，K线为阴阳阳，但第一日与第二日的K线形态相反， 以三外部上涨为例，第一日K线在第二日K线内部，预示着股价上涨。

            6、南方三星
            三日K线模式，与大敌当前相反，三日K线皆阴，第一日有长下影线， 第二日与第一日类似，K线整体小于第一日，第三日无下影线实体信号， 成交价格都在第一日振幅之内，预示下跌趋势反转，股价上升。

            7、三个白兵
            三日K线模式，三日K线皆阳， 每日收盘价变高且接近最高价，开盘价在前一日实体上半部，预示股价上升。

            8、弃婴
            三日K线模式，第二日价格跳空且收十字星（开盘价与收盘价接近， 最高价最低价相差不大），预示趋势反转，发生在顶部下跌，底部上涨。

            9、大敌当前
            三日K线模式，三日都收阳，每日收盘价都比前一日高， 开盘价都在前一日实体以内，实体变短，上影线变长。

            10、捉腰带线
            两日K线模式，下跌趋势中，第一日阴线， 第二日开盘价为最低价，阳线，收盘价接近最高价，预示价格上涨。

            11、脱离
            五日K线模式，以看涨脱离为例，下跌趋势中，第一日长阴线，第二日跳空阴线，延续趋势开始震荡， 第五日长阳线，收盘价在第一天收盘价与第二天开盘价之间，预示价格上涨。

            12、收盘缺影线
            一日K线模式，以阳线为例，最低价低于开盘价，收盘价等于最高价， 预示着趋势持续。

            13、藏婴吞没
            四日K线模式，下跌趋势中，前两日阴线无影线 ，第二日开盘、收盘价皆低于第二日，第三日倒锤头， 第四日开盘价高于前一日最高价，收盘价低于前一日最低价，预示着底部反转。

            14、反击线
            二日K线模式，与分离线类似。

            15、乌云压顶
            二日K线模式，第一日长阳，第二日开盘价高于前一日最高价， 收盘价处于前一日实体中部以下，预示着股价下跌。

            16、十字
            一日K线模式，开盘价与收盘价基本相同。

            17、十字星
            一日K线模式，开盘价与收盘价基本相同，上下影线不会很长，预示着当前趋势反转。

            18、蜻蜓十字/T形十字
            一日K线模式，开盘后价格一路走低， 之后收复，收盘价与开盘价相同，预示趋势反转。

            19、吞噬模式
            两日K线模式，分多头吞噬和空头吞噬，以多头吞噬为例，第一日为阴线， 第二日阳线，第一日的开盘价和收盘价在第二日开盘价收盘价之内，但不能完全相同。

            20、十字暮星
            三日K线模式，基本模式为暮星，第二日收盘价和开盘价相同，预示顶部反转。

            21、暮星
            三日K线模式，与晨星相反，上升趋势中, 第一日阳线，第二日价格振幅较小，第三日阴线，预示顶部反转。

            22、向上/下跳空并列阳线
            二日K线模式，上升趋势向上跳空，下跌趋势向下跳空, 第一日与第二日有相同开盘价，实体长度差不多，则趋势持续。

            23、墓碑十字/倒T十字
            一日K线模式，开盘价与收盘价相同，上影线长，无下影线，预示底部反转。

            24、锤头
            一日K线模式，实体较短，无上影线， 下影线大于实体长度两倍，处于下跌趋势底部，预示反转。

            25、上吊线
            一日K线模式，形状与锤子类似，处于上升趋势的顶部，预示着趋势反转。

            26、母子线
            二日K线模式，分多头母子与空头母子，两者相反，以多头母子为例，在下跌趋势中，第一日K线长阴， 第二日开盘价收盘价在第一日价格振幅之内，为阳线，预示趋势反转，股价上升。

            27、十字孕线
            二日K线模式，与母子线类似，若第二日K线是十字线， 便称为十字孕线，预示着趋势反转。

            28、风高浪大线
            三日K线模式，具有极长的上/下影线与短的实体，预示着趋势反转。

            29、陷阱
            三日K线模式，与母子类似，第二日价格在前一日实体范围内, 第三日收盘价高于前两日，反转失败，趋势继续。

            30、修正陷阱
            三日K线模式，与陷阱类似，上升趋势中，第三日跳空高开； 下跌趋势中，第三日跳空低开，反转失败，趋势继续。

            31、家鸽
            二日K线模式，与母子线类似，不同的的是二日K线颜色相同， 第二日最高价、最低价都在第一日实体之内，预示着趋势反转。

            32、三胞胎乌鸦
            三日K线模式，上涨趋势中，三日都为阴线，长度大致相等， 每日开盘价等于前一日收盘价，收盘价接近当日最低价，预示价格下跌。

            33、颈内线
            二日K线模式，下跌趋势中，第一日长阴线， 第二日开盘价较低，收盘价略高于第一日收盘价，阳线，实体较短，预示着下跌继续。

            34、倒锤头
            一日K线模式，上影线较长，长度为实体2倍以上， 无下影线，在下跌趋势底部，预示着趋势反转。

            35、反冲形态
            二日K线模式，与分离线类似，两日K线为秃线，颜色相反，存在跳空缺口。

            36、由较长缺影线决定的反冲形态
            二日K线模式，与反冲形态类似，较长缺影线决定价格的涨跌。

            37、梯底
            五日K线模式，下跌趋势中，前三日阴线， 开盘价与收盘价皆低于前一日开盘、收盘价，第四日倒锤头，第五日开盘价高于前一日开盘价， 阳线，收盘价高于前几日价格振幅，预示着底部反转。

            38、长脚十字
            一日K线模式，开盘价与收盘价相同居当日价格中部，上下影线长， 表达市场不确定性。

            39、长蜡烛
            一日K线模式，K线实体长，无上下影线。

            40、光头光脚/缺影线
            一日K线模式，上下两头都没有影线的实体， 阴线预示着熊市持续或者牛市反转，阳线相反。

            41、相同低价
            二日K线模式，下跌趋势中，第一日长阴线， 第二日阴线，收盘价与前一日相同，预示底部确认，该价格为支撑位。

            42、铺垫
            五日K线模式，上涨趋势中，第一日阳线，第二日跳空高开影线， 第三、四日短实体影线，第五日阳线，收盘价高于前四日，预示趋势持续。

            43、十字晨星
            三日K线模式， 基本模式为晨星，第二日K线为十字星，预示底部反转。

            44、晨星
            三日K线模式，下跌趋势，第一日阴线， 第二日价格振幅较小，第三天阳线，预示底部反转。

            45、颈上线
            二日K线模式，下跌趋势中，第一日长阴线，第二日开盘价较低， 收盘价与前一日最低价相同，阳线，实体较短，预示着延续下跌趋势。

            46、刺透形态
            两日K线模式，下跌趋势中，第一日阴线，第二日收盘价低于前一日最低价， 收盘价处在第一日实体上部，预示着底部反转。

            47、黄包车夫
            一日K线模式，与长腿十字线类似， 若实体正好处于价格振幅中点，称为黄包车夫。

            48、上升/下降三法
            五日K线模式，以上升三法为例，上涨趋势中， 第一日长阳线，中间三日价格在第一日范围内小幅震荡， 第五日长阳线，收盘价高于第一日收盘价，预示股价上升。

            49、分离线
            二日K线模式，上涨趋势中，第一日阴线，第二日阳线， 第二日开盘价与第一日相同且为最低价，预示着趋势继续。

            50、射击之星
            一日K线模式，上影线至少为实体长度两倍， 没有下影线，预示着股价下跌

            51、短蜡烛
            一日K线模式，实体短，无上下影线

            52、纺锤
            一日K线，实体小。

            53、停顿形态
            三日K线模式，上涨趋势中，第二日长阳线， 第三日开盘于前一日收盘价附近，短阳线，预示着上涨结束

            54、条形三明治
            三日K线模式，第一日长阴线，第二日阳线，开盘价高于前一日收盘价， 第三日开盘价高于前两日最高价，收盘价于第一日收盘价相同。

            55、探水竿
            一日K线模式，大致与蜻蜓十字相同，下影线长度长。

            56、跳空并列阴阳线
            三日K线模式，分上涨和下跌，以上升为例， 前两日阳线，第二日跳空，第三日阴线，收盘价于缺口中，上升趋势持续。

            57、插入
            二日K线模式，与颈上线类似，下跌趋势中，第一日长阴线，第二日开盘价跳空， 收盘价略低于前一日实体中部，与颈上线相比实体较长，预示着趋势持续。

            58、三星
            三日K线模式，由三个十字组成， 第二日十字必须高于或者低于第一日和第三日，预示着反转。

            59、奇特三河床
            三日K线模式，下跌趋势中，第一日长阴线，第二日为锤头，最低价创新低，第三日开盘价低于第二日收盘价，收阳线， 收盘价不高于第二日收盘价，预示着反转，第二日下影线越长可能性越大。

            60、向上跳空的两只乌鸦
            三日K线模式，第一日阳线，第二日跳空以高于第一日最高价开盘， 收阴线，第三日开盘价高于第二日，收阴线，与第一日比仍有缺口。

            61、上升/下降跳空三法
            五日K线模式，以上升跳空三法为例，上涨趋势中，第一日长阳线，第二日短阳线，第三日跳空阳线，第四日阴线，开盘价与收盘价于前两日实体内， 第五日长阳线，收盘价高于第一日收盘价，预示股价上升。
        </code></pre>
    </div>

    <p dir="auto" class="red">六：筹码分布</p>
    <div class="snippet-clipboard-content notranslate position-relative overflow-auto">
        <pre class="notranslate"><code>
            筹码分布通过计算一定时间范围内股票的:最高价、最低价、成交数，输出对应价格成交数占整个流通盘比值的分布图形。计算高效准确，结果与东方财富等专业软件的一致，缺省计算210个交易日的成本，可以自行设定时间范围。
        </code></pre>
    </div>

    <p dir="auto" class="red">七：策略选股</p>
    <div class="snippet-clipboard-content notranslate position-relative overflow-auto">
        <pre class="notranslate"><code>
            1、放量上涨
                1）当日比前一天上涨小于2%或收盘价小于开盘价。
                2）当日成交额不低于2亿。
                3）当日成交量/5日平均成交量>=2。
            <br>
            2、均线多头
                MA30向上
                1）30日前的30日均线<20日前的30日均线<10日前的30日均线<当日的30日均线。
                2）(当日的30日均线/30日前的30日均线)>1.2。
            <br>
            3、停机坪
                1）最近15日有涨幅大于9.5%，且必须是放量上涨。
                2）紧接的下个交易日必须高开，收盘价必须上涨，且与开盘价不能大于等于相差3%。
                3）接下2、3个交易日必须高开，收盘价必须上涨，且与开盘价不能大于等于相差3%，且每天涨跌幅在5%间。
            <br>
            4、回踩年线
                1）分2个时间段：前段=最近60交易日最高收盘价之前交易日(长度>0)，后段=最高价当日及后面的交易日。
                2）前段由年线(250日)以下向上突破。
                3）后段必须在年线以上运行，且后段最低价日与最高价日相差必须在10-50日间。
                4）回踩伴随缩量：最高价日交易量/后段最低价日交易量>2,后段最低价/最高价<0.8。
            <br>
            5、突破平台
                1）60日内某日收盘价>=60日均线>开盘价。
                2）且【1】放量上涨。
                3）且【1】间之前时间，任意一天收盘价与60日均线偏离在-5%~20%之间。
            <br>
            6、无大幅回撤
                1）当日收盘价比60日前的收盘价的涨幅小于0.6。
                2）最近60日，不能有单日跌幅超7%、高开低走7%、两日累计跌幅10%、两日高开低走累计10%。
            <br>
            7、海龟交易法则
                最后一个交易日收市价为指定区间内最高价。
                1）当日收盘价>=最近60日最高收盘价。
            <br>
            8、高而窄的旗形
                1）必须至少上市交易60日。
                2）当日收盘价/之前24~10日的最低价>=1.9。
                3）之前24~10日必须连续两天涨幅大于等于9.5%。
            <br>
            9、放量跌停。
                1）跌>9.5%。
                2）成交额不低于2亿。
                3）成交量至少是5日平均成交量的4倍。
            <br>
            10、低ATR成长
                1）必须至少上市交易250日。
                2）最近10个交易日的最高收盘价必须比最近10个交易日的最低收盘价高1.1倍。
            <br>
            11、股票基本面选股
                1）市盈率小于等于20，且大于0。
                2）市净率小于等于10。
                3）净资产收益率大于等于15。
        </code></pre>
    </div>

    <p dir="auto" class="red">八：选股验证</p>
    <div class="snippet-clipboard-content notranslate position-relative overflow-auto">
        <pre class="notranslate"><code>
            对指标、策略等选出的股票进行回测，验证其准确性。
        </code></pre>
    </div>

    <p dir="auto" class="red">九：选股验证</p>
    <div class="snippet-clipboard-content notranslate position-relative overflow-auto">
        <pre class="notranslate"><code>
            对指标、策略等选出的股票进行回测，验证策略的成功率，是否可用。
        </code></pre>
    </div>
    <p dir="auto" class="red">十：自动交易</p>
    <div class="snippet-clipboard-content notranslate position-relative overflow-auto">
        <pre class="notranslate"><code>
            支持自动交易，内置自动打新股的策略及示例策略，由于涉及金钱，规避可能存在风险，没有提供其他交易策略。

            具有交易日志，以及支持为每个交易策略配置交易日志。

            特别提醒：交易日10:00点会触发打新，不想打新的删除stagging.py或不要启动“交易服务”。
        </code></pre>
    </div>
    <p dir="auto" class="red">十一：关注功能</p>
    <div class="snippet-clipboard-content notranslate position-relative overflow-auto">
        <pre class="notranslate"><code>
            支持股票关注，关注股票在各个模块(含有的)置顶、标红显示。
        </code></pre>
    </div>
    <p dir="auto" class="red">十二：支持批量</p>
    <div class="snippet-clipboard-content notranslate position-relative overflow-auto">
        <pre class="notranslate"><code>
            可以通过时间段、枚举时间、当前时间进行指标计算、策略选股及回测等。同时支持智能识别交易日，可以输入任意日期。

            具体执行设置如下：

            ------整体作业，支持批量作业------
            当前时间作业 python execute_daily_job.py
            单个时间作业 python execute_daily_job.py 2022-03-01
            枚举时间作业 python execute_daily_job.py 2022-01-01,2021-02-08,2022-03-12
            区间时间作业 python execute_daily_job.py 2022-01-01 2022-03-01

            ------单功能作业，支持批量作业，回测数据自动填补到当前
            综合选股作业 python selection_data_daily_job.py
            基础数据实时作业 python basic_data_daily_job.py
            基础数据收盘2小时后作业 python backtest_data_daily_job.py
            基础数据非实时作业 python basic_data_other_daily_job.py
            指标数据作业 python indicators_data_daily_job.py
            K线形态作业 klinepattern_data_daily_job.py
            策略数据作业 python strategy_data_daily_job.py
            回测数据 python backtest_data_daily_job.py
        </code></pre>
    </div>
</div>
{% end %}