{% extends "layout/main.html" %}

{% block meta %}
{% include "common/meta.html" %}
<style>
.config-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.config-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.config-card.disabled {
    opacity: 0.6;
    background-color: #f8f9fa;
}

.category-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border-radius: 8px 8px 0 0;
    margin-bottom: 0;
}

.module-item {
    padding: 15px;
    border-bottom: 1px solid #eee;
}

.module-item:last-child {
    border-bottom: none;
    border-radius: 0 0 8px 8px;
}

.module-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.module-title {
    font-weight: bold;
    font-size: 16px;
    color: #333;
}

.module-description {
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
}

.module-meta {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #888;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.status-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
}

.status-enabled {
    background-color: #d4edda;
    color: #155724;
}

.status-disabled {
    background-color: #f8d7da;
    color: #721c24;
}

.data-size-small { color: #28a745; }
.data-size-medium { color: #ffc107; }
.data-size-large { color: #dc3545; }

.control-panel {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.execution-plan {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.task-status {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.progress-container {
    margin: 15px 0;
}

.log-container {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    max-height: 300px;
    overflow-y: auto;
    font-family: monospace;
    font-size: 12px;
}

.log-entry {
    margin-bottom: 5px;
    padding: 2px 0;
}

.btn-group-custom {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #007bff;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.category-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: rgba(255,255,255,0.1);
    border-radius: 4px;
    margin-top: 10px;
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
{% end %}

{% block header %}
{% include "common/header.html" %}
{% end %}

{% block left_menu %}
{% include "common/left_menu.html" %}
{% end %}

{% block main_content %}
<div class="page-content">
    <div class="page-header">
        <h1>
            <i class="fa fa-cogs"></i>
            数据更新配置管理
            <small>
                <i class="fa fa-angle-double-right"></i>
                灵活控制数据采集模块
            </small>
        </h1>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel">
        <div class="row">
            <div class="col-md-8">
                <h4><i class="fa fa-play-circle"></i> 执行控制</h4>
                <div class="btn-group-custom">
                    <button id="refreshBtn" class="btn btn-info btn-sm">
                        <i class="fa fa-refresh"></i> 刷新配置
                    </button>
                    <button id="selectAllBtn" class="btn btn-secondary btn-sm">
                        <i class="fa fa-check-square"></i> 全选
                    </button>
                    <button id="selectNoneBtn" class="btn btn-secondary btn-sm">
                        <i class="fa fa-square-o"></i> 全不选
                    </button>
                    <button id="saveConfigBtn" class="btn btn-warning btn-sm">
                        <i class="fa fa-save"></i> 保存配置
                    </button>
                    <button id="startUpdateBtn" class="btn btn-success btn-sm">
                        <i class="fa fa-play"></i> 开始更新
                    </button>
                    <button id="stopUpdateBtn" class="btn btn-danger btn-sm" disabled>
                        <i class="fa fa-stop"></i> 停止更新
                    </button>
                </div>
            </div>
            <div class="col-md-4">
                <h4><i class="fa fa-filter"></i> 快速筛选</h4>
                <div class="btn-group-custom">
                    <button class="btn btn-outline-primary btn-sm filter-btn" data-category="basic">基础数据</button>
                    <button class="btn btn-outline-success btn-sm filter-btn" data-category="technical">技术分析</button>
                    <button class="btn btn-outline-warning btn-sm filter-btn" data-category="strategy">交易策略</button>
                </div>
            </div>
        </div>

        <!-- 执行计划 -->
        <div id="executionPlan" class="execution-plan" style="display: none;">
            <h5><i class="fa fa-list-ol"></i> 执行计划</h5>
            <div id="planContent"></div>
        </div>
    </div>

    <!-- 任务状态 -->
    <div id="taskStatus" class="task-status" style="display: none;">
        <h4><i class="fa fa-tasks"></i> 任务状态</h4>
        <div id="currentTask"></div>
        <div id="taskHistory"></div>
    </div>

    <!-- 配置列表 -->
    <div id="configContainer">
        <div class="text-center">
            <div class="loading-spinner"></div>
            <p>正在加载配置...</p>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    var currentConfigs = {};
    var refreshInterval = null;
    
    console.log('数据更新配置页面初始化');
    
    // 初始化
    loadConfigs();
    
    // 刷新配置
    $('#refreshBtn').click(function() {
        console.log('刷新配置按钮被点击');
        loadConfigs();
    });
    
    // 全选/全不选
    $('#selectAllBtn').click(function() {
        $('.module-switch input').prop('checked', true).trigger('change');
    });
    
    $('#selectNoneBtn').click(function() {
        $('.module-switch input').prop('checked', false).trigger('change');
    });
    
    // 保存配置
    $('#saveConfigBtn').click(function() {
        saveAllConfigs();
    });
    
    // 开始更新
    $('#startUpdateBtn').click(function() {
        startUpdate();
    });
    
    // 停止更新
    $('#stopUpdateBtn').click(function() {
        stopUpdate();
    });
    
    // 类别筛选
    $('.filter-btn').click(function() {
        var category = $(this).data('category');
        filterByCategory(category);
    });
    
    // 加载配置
    function loadConfigs() {
        console.log('开始加载配置');
        
        $.ajax({
            url: '/instock/admin/data-config/api',
            type: 'POST',
            data: { action: 'get_configs' },
            dataType: 'json',
            success: function(response) {
                console.log('配置加载成功:', response);
                if (response.success) {
                    currentConfigs = response.data.configs;
                    var categories = response.data.categories;
                    renderConfigs(currentConfigs, categories);
                } else {
                    alert('加载配置失败: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                console.log('配置加载失败:', xhr, status, error);
                alert('加载配置失败: ' + error);
            }
        });
    }
    
    // 渲染配置
    function renderConfigs(configs, categories) {
        var html = '';
        
        for (var categoryId in configs) {
            var categoryModules = configs[categoryId];
            var categoryInfo = categories[categoryId] || { name: categoryId, color: '#6c757d', icon: 'fa-folder' };
            
            html += '<div class="config-card" data-category="' + categoryId + '">';
            html += '<div class="category-header" style="background-color: ' + categoryInfo.color + '">';
            html += '<h5><i class="fa ' + categoryInfo.icon + '"></i> ' + categoryInfo.name + '</h5>';
            html += '<div class="category-stats">';
            html += '<span>模块数量: ' + categoryModules.length + '</span>';
            html += '<span>启用: ' + categoryModules.filter(m => m.enabled).length + '</span>';
            html += '</div>';
            html += '</div>';
            
            for (var i = 0; i < categoryModules.length; i++) {
                var module = categoryModules[i];
                html += renderModuleItem(module);
            }
            
            html += '</div>';
        }
        
        $('#configContainer').html(html);
        
        // 绑定事件
        bindEvents();
    }
    
    // 渲染单个模块
    function renderModuleItem(module) {
        var html = '<div class="module-item ' + (module.enabled ? '' : 'disabled') + '" data-module="' + module.module_id + '">';
        
        html += '<div class="module-header">';
        html += '<div class="module-title">' + module.name + '</div>';
        html += '<div class="module-controls">';
        html += '<label class="switch module-switch">';
        html += '<input type="checkbox" ' + (module.enabled ? 'checked' : '') + ' data-module="' + module.module_id + '">';
        html += '<span class="slider"></span>';
        html += '</label>';
        html += '</div>';
        html += '</div>';
        
        html += '<div class="module-description">' + module.description + '</div>';
        
        html += '<div class="module-meta">';
        html += '<div class="meta-item">';
        html += '<i class="fa fa-sort-numeric-asc"></i>';
        html += '<span>优先级: ' + module.priority + '</span>';
        html += '</div>';
        html += '<div class="meta-item">';
        html += '<i class="fa fa-clock-o"></i>';
        html += '<span>预估: ' + Math.round(module.estimated_time / 60) + '分钟</span>';
        html += '</div>';
        html += '<div class="meta-item data-size-' + module.data_size + '">';
        html += '<i class="fa fa-database"></i>';
        html += '<span>数据量: ' + module.data_size + '</span>';
        html += '</div>';
        html += '<div class="meta-item">';
        html += '<i class="fa fa-calendar"></i>';
        html += '<span>频率: ' + module.update_frequency + '</span>';
        html += '</div>';
        html += '<div class="meta-item">';
        html += '<span class="status-badge ' + (module.enabled ? 'status-enabled' : 'status-disabled') + '">';
        html += module.enabled ? '已启用' : '已禁用';
        html += '</span>';
        html += '</div>';
        html += '</div>';
        
        html += '</div>';
        
        return html;
    }
    
    // 绑定事件
    function bindEvents() {
        // 模块开关切换
        $('.module-switch input').off('change').on('change', function() {
            var moduleId = $(this).data('module');
            var enabled = $(this).is(':checked');
            var moduleItem = $(this).closest('.module-item');
            
            if (enabled) {
                moduleItem.removeClass('disabled');
                moduleItem.find('.status-badge').removeClass('status-disabled').addClass('status-enabled').text('已启用');
            } else {
                moduleItem.addClass('disabled');
                moduleItem.find('.status-badge').removeClass('status-enabled').addClass('status-disabled').text('已禁用');
            }
            
            console.log('模块状态切换:', moduleId, enabled);
        });
    }
    
    // 保存所有配置
    function saveAllConfigs() {
        var configs = [];
        
        $('.module-switch input').each(function() {
            var moduleId = $(this).data('module');
            var enabled = $(this).is(':checked');
            
            configs.push({
                module_id: moduleId,
                enabled: enabled
            });
        });
        
        console.log('保存配置:', configs);
        
        $.ajax({
            url: '/instock/admin/data-config/api',
            type: 'POST',
            data: {
                action: 'batch_update',
                configs: JSON.stringify(configs)
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    alert('配置保存成功');
                    loadConfigs(); // 重新加载配置
                } else {
                    alert('配置保存失败: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                alert('配置保存失败: ' + error);
            }
        });
    }
    
    // 开始更新
    function startUpdate() {
        var selectedModules = [];
        $('.module-switch input:checked').each(function() {
            selectedModules.push($(this).data('module'));
        });
        
        if (selectedModules.length === 0) {
            alert('请至少选择一个模块');
            return;
        }
        
        console.log('开始更新，选中模块:', selectedModules);
        
        $.ajax({
            url: '/instock/admin/data-config/api',
            type: 'POST',
            data: {
                action: 'start_update',
                modules: JSON.stringify(selectedModules)
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    alert('更新任务已启动: ' + response.data.task_id);
                    $('#startUpdateBtn').prop('disabled', true);
                    $('#stopUpdateBtn').prop('disabled', false);
                    startRefresh();
                } else {
                    alert('启动失败: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                alert('启动失败: ' + error);
            }
        });
    }
    
    // 停止更新
    function stopUpdate() {
        $.ajax({
            url: '/instock/admin/data-config/api',
            type: 'POST',
            data: { action: 'stop_update' },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    alert('任务已停止');
                    $('#startUpdateBtn').prop('disabled', false);
                    $('#stopUpdateBtn').prop('disabled', true);
                    stopRefresh();
                } else {
                    alert('停止失败: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                alert('停止失败: ' + error);
            }
        });
    }
    
    // 开始刷新任务状态
    function startRefresh() {
        if (refreshInterval) {
            clearInterval(refreshInterval);
        }
        
        refreshInterval = setInterval(function() {
            refreshTaskStatus();
        }, 2000);
        
        $('#taskStatus').show();
    }
    
    // 停止刷新
    function stopRefresh() {
        if (refreshInterval) {
            clearInterval(refreshInterval);
            refreshInterval = null;
        }
    }
    
    // 刷新任务状态
    function refreshTaskStatus() {
        $.ajax({
            url: '/instock/admin/data-config/api',
            type: 'POST',
            data: { action: 'get_status' },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    renderTaskStatus(response.data);
                    
                    // 如果没有当前任务，停止刷新
                    if (!response.data.current_task) {
                        $('#startUpdateBtn').prop('disabled', false);
                        $('#stopUpdateBtn').prop('disabled', true);
                        stopRefresh();
                    }
                }
            },
            error: function() {
                // 忽略错误，继续刷新
            }
        });
    }
    
    // 渲染任务状态
    function renderTaskStatus(data) {
        var html = '';
        
        if (data.current_task) {
            var task = data.current_task;
            html += '<h5>当前任务: ' + task.id + '</h5>';
            html += '<div class="progress-container">';
            html += '<div class="progress">';
            html += '<div class="progress-bar" style="width: ' + task.progress + '%">' + task.progress + '%</div>';
            html += '</div>';
            html += '</div>';
            
            if (task.current_module) {
                html += '<p><strong>当前模块:</strong> ' + task.current_module + '</p>';
            }
            
            if (task.logs && task.logs.length > 0) {
                html += '<div class="log-container">';
                for (var i = task.logs.length - 1; i >= Math.max(0, task.logs.length - 20); i--) {
                    var log = task.logs[i];
                    html += '<div class="log-entry">[' + log.time + '] ' + log.message + '</div>';
                }
                html += '</div>';
            }
        } else {
            html += '<p>暂无运行中的任务</p>';
        }
        
        $('#currentTask').html(html);
        
        // 渲染历史任务
        if (data.task_history && data.task_history.length > 0) {
            var historyHtml = '<h5>任务历史</h5>';
            for (var i = 0; i < Math.min(5, data.task_history.length); i++) {
                var task = data.task_history[i];
                historyHtml += '<div class="task-history-item">';
                historyHtml += '<strong>' + task.id + '</strong> - ';
                historyHtml += '<span class="badge badge-' + (task.status === 'completed' ? 'success' : task.status === 'failed' ? 'danger' : 'warning') + '">';
                historyHtml += task.status;
                historyHtml += '</span>';
                historyHtml += ' (' + task.start_time + ')';
                historyHtml += '</div>';
            }
            $('#taskHistory').html(historyHtml);
        }
    }
    
    // 类别筛选
    function filterByCategory(category) {
        if (category) {
            $('.config-card').hide();
            $('.config-card[data-category="' + category + '"]').show();
        } else {
            $('.config-card').show();
        }
    }
});
</script>
{% end %}
