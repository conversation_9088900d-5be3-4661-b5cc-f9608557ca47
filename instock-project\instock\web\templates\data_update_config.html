{% extends "layout/main.html" %}

{% block meta %}
{% include "common/meta.html" %}
<style>
.config-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.config-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.config-card.disabled {
    opacity: 0.6;
    background-color: #f8f9fa;
}

.category-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border-radius: 8px 8px 0 0;
    margin-bottom: 0;
}

.module-item {
    padding: 15px;
    border-bottom: 1px solid #eee;
}

.module-item:last-child {
    border-bottom: none;
    border-radius: 0 0 8px 8px;
}

.module-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.module-title {
    font-weight: bold;
    font-size: 16px;
    color: #333;
}

.module-description {
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
}

.module-meta {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #888;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.status-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
}

.status-enabled {
    background-color: #d4edda;
    color: #155724;
}

.status-disabled {
    background-color: #f8d7da;
    color: #721c24;
}

.data-size-small { color: #28a745; }
.data-size-medium { color: #ffc107; }
.data-size-large { color: #dc3545; }

.control-panel {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.execution-plan {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.task-status {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.progress-container {
    margin: 15px 0;
}

.log-container {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    max-height: 300px;
    overflow-y: auto;
    font-family: monospace;
    font-size: 12px;
}

.log-entry {
    margin-bottom: 5px;
    padding: 2px 0;
}

.btn-group-custom {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-btn.active {
    background-color: #007bff !important;
    color: white !important;
    border-color: #007bff !important;
}

.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #007bff;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.category-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: rgba(255,255,255,0.1);
    border-radius: 4px;
    margin-top: 10px;
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 统计信息样式 */
.stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 8px;
    margin-top: 10px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #007bff;
}

.stat-label {
    font-size: 13px;
    color: #666;
}

.stat-value {
    font-weight: bold;
    color: #333;
}

/* 执行计划样式 */
.execution-plan {
    margin-top: 15px;
}

.plan-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 5px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 12px;
}

.plan-item.selected {
    background: #e3f2fd;
    border-color: #2196f3;
}

.plan-module-name {
    font-weight: 500;
    color: #333;
}

.plan-module-time {
    color: #666;
    font-size: 11px;
}

/* 右侧面板固定样式 */
.col-md-4 .control-panel,
.col-md-4 .execution-plan,
.col-md-4 .task-status {
    position: sticky;
    top: 20px;
}

/* 模块项优化 */
.module-item {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    background: white;
}

.module-item:hover {
    background: #fafbfc;
    transform: translateX(2px);
}

.module-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.module-title {
    font-weight: 600;
    font-size: 16px;
    color: #2c3e50;
    flex: 1;
    line-height: 1.3;
}

.module-controls {
    margin-left: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.module-description {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 12px;
    line-height: 1.5;
    padding-left: 2px;
}

.module-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
    font-size: 12px;
    color: #6c757d;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    background: #f8f9fa;
    border-radius: 4px;
    white-space: nowrap;
}

.meta-item i {
    width: 14px;
    text-align: center;
}

/* 类别头部优化 */
.category-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 15px;
    border-radius: 8px 8px 0 0;
    margin-bottom: 0;
}

.category-header h5 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.category-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 15px;
    background: rgba(255,255,255,0.1);
    border-radius: 4px;
    margin-top: 8px;
    font-size: 12px;
}

/* 时间选择面板样式 */
.time-selection {
    margin-top: 15px;
}

.time-selection .form-group {
    margin-bottom: 12px;
}

.time-selection label {
    font-size: 12px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 4px;
    display: block;
}

.time-selection .form-control {
    border-radius: 4px;
    border: 1px solid #ced4da;
    font-size: 12px;
}

/* 进度显示样式 */
.progress-section {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.progress-title {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.progress-percent {
    font-weight: bold;
    color: #007bff;
    font-size: 14px;
}

.progress-lg {
    height: 12px;
    border-radius: 6px;
}

.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
    transition: width 0.3s ease;
}

.progress-info {
    margin-top: 6px;
}

.progress-info small {
    color: #6c757d;
    font-size: 11px;
}

/* 按钮优化 */
.btn-lg {
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.btn-lg:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* 状态徽章优化 */
.status-badge {
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 任务详情样式 */
.task-details, .task-logs, .task-history {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
}

.task-details h6, .task-logs h6, .task-history h6 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 13px;
    font-weight: 600;
}

.task-info {
    display: grid;
    gap: 6px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
    border-bottom: 1px solid #f8f9fa;
    font-size: 12px;
}

.info-item .label {
    color: #6c757d;
    font-weight: 500;
}

.info-item code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
}

/* 日志样式 */
.log-container {
    max-height: 200px;
    overflow-y: auto;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 8px;
    font-family: 'Courier New', monospace;
}

.log-entry {
    margin-bottom: 4px;
    padding: 2px 0;
    font-size: 11px;
    line-height: 1.4;
}

.log-entry.log-success {
    color: #28a745;
}

.log-entry.log-error {
    color: #dc3545;
}

.log-entry.log-info {
    color: #007bff;
}

.log-time {
    color: #6c757d;
    font-weight: 500;
}

.log-message {
    color: #495057;
}

/* 历史任务样式 */
.history-item {
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.history-item:last-child {
    border-bottom: none;
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.history-id {
    font-family: monospace;
    font-size: 11px;
    color: #495057;
    font-weight: 500;
}

.history-time {
    font-size: 10px;
    color: #6c757d;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .col-md-4 .control-panel,
    .col-md-4 .execution-plan,
    .col-md-4 .task-status {
        position: static;
    }

    .btn-group-custom {
        flex-direction: column;
        gap: 5px;
    }

    .module-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .module-controls {
        margin-left: 0;
        margin-top: 8px;
    }

    .module-meta {
        grid-template-columns: 1fr;
    }
}
</style>
{% end %}

{% block header %}
{% include "common/header.html" %}
{% end %}

{% block left_menu %}
{% include "common/left_menu.html" %}
{% end %}

{% block main_content %}
<div class="page-content">
    <div class="page-header">
        <h1>
            <i class="fa fa-cogs"></i>
            数据更新配置管理
            <small>
                <i class="fa fa-angle-double-right"></i>
                灵活控制数据采集模块
            </small>
        </h1>
    </div>

    <!-- 主要内容区域 -->
    <div class="row">
        <!-- 左侧配置面板 -->
        <div class="col-md-8">
            <!-- 控制面板 -->
            <div class="control-panel">
                <div class="row">
                    <div class="col-md-8">
                        <h4><i class="fa fa-play-circle"></i> 执行控制</h4>
                        <div class="btn-group-custom">
                            <button id="refreshBtn" class="btn btn-info btn-sm">
                                <i class="fa fa-refresh"></i> 刷新配置
                            </button>
                            <button id="selectAllBtn" class="btn btn-secondary btn-sm">
                                <i class="fa fa-check-square"></i> 全选
                            </button>
                            <button id="selectNoneBtn" class="btn btn-secondary btn-sm">
                                <i class="fa fa-square-o"></i> 全不选
                            </button>
                            <button id="saveConfigBtn" class="btn btn-warning btn-sm">
                                <i class="fa fa-save"></i> 保存配置
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h4><i class="fa fa-filter"></i> 快速筛选</h4>
                        <div class="btn-group-custom">
                            <button class="btn btn-outline-primary btn-sm filter-btn" data-category="basic">基础数据</button>
                            <button class="btn btn-outline-success btn-sm filter-btn" data-category="technical">技术分析</button>
                            <button class="btn btn-outline-warning btn-sm filter-btn" data-category="strategy">交易策略</button>
                            <button class="btn btn-outline-secondary btn-sm filter-btn" data-category="">全部</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 配置列表 -->
            <div id="configContainer">
                <div class="text-center">
                    <div class="loading-spinner"></div>
                    <p>正在加载配置...</p>
                </div>
            </div>
        </div>

        <!-- 右侧状态面板 -->
        <div class="col-md-4">
            <!-- 时间选择面板 -->
            <div class="control-panel">
                <h4><i class="fa fa-calendar"></i> 更新时间设置</h4>
                <div class="time-selection">
                    <div class="form-group">
                        <label for="updateMode">更新模式:</label>
                        <select id="updateMode" class="form-control form-control-sm">
                            <option value="latest">最新数据 (今日)</option>
                            <option value="range">指定时间范围</option>
                            <option value="backfill">数据回补</option>
                        </select>
                    </div>

                    <div id="dateRangePanel" style="display: none;">
                        <div class="form-group">
                            <label for="startDate">开始日期:</label>
                            <input type="date" id="startDate" class="form-control form-control-sm">
                        </div>
                        <div class="form-group">
                            <label for="endDate">结束日期:</label>
                            <input type="date" id="endDate" class="form-control form-control-sm">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="updateFrequency">更新频率:</label>
                        <select id="updateFrequency" class="form-control form-control-sm">
                            <option value="once">仅此一次</option>
                            <option value="daily">每日定时</option>
                            <option value="weekly">每周定时</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 执行控制按钮 -->
            <div class="control-panel">
                <h4><i class="fa fa-rocket"></i> 任务执行</h4>
                <div class="btn-group-custom" style="flex-direction: column; gap: 10px;">
                    <button id="startUpdateBtn" class="btn btn-success btn-lg">
                        <i class="fa fa-play"></i> 开始更新选中模块
                    </button>
                    <button id="stopUpdateBtn" class="btn btn-danger btn-lg" disabled>
                        <i class="fa fa-stop"></i> 停止当前任务
                    </button>
                </div>

                <!-- 统计信息 -->
                <div id="statsInfo" class="mt-3">
                    <h5><i class="fa fa-bar-chart"></i> 统计信息</h5>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">总模块数:</span>
                            <span id="totalModules" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">已启用:</span>
                            <span id="enabledModules" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">预估时间:</span>
                            <span id="estimatedTime" class="stat-value">0分钟</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 执行计划 -->
            <div id="executionPlan" class="execution-plan">
                <h5><i class="fa fa-list-ol"></i> 执行计划</h5>
                <div id="planContent">
                    <p class="text-muted">请选择要执行的模块</p>
                </div>
            </div>

            <!-- 任务状态 -->
            <div id="taskStatus" class="task-status" style="display: none;">
                <h4><i class="fa fa-tasks"></i> 任务状态</h4>

                <!-- 整体进度 -->
                <div id="overallProgress" class="progress-section">
                    <div class="progress-header">
                        <span class="progress-title">整体进度</span>
                        <span id="overallPercent" class="progress-percent">0%</span>
                    </div>
                    <div class="progress progress-lg">
                        <div id="overallProgressBar" class="progress-bar progress-bar-striped progress-bar-animated"
                             style="width: 0%"></div>
                    </div>
                    <div class="progress-info">
                        <small id="progressText">准备开始...</small>
                    </div>
                </div>

                <!-- 当前模块进度 -->
                <div id="moduleProgress" class="progress-section" style="display: none;">
                    <div class="progress-header">
                        <span id="currentModuleName" class="progress-title">当前模块</span>
                        <span id="modulePercent" class="progress-percent">0%</span>
                    </div>
                    <div class="progress">
                        <div id="moduleProgressBar" class="progress-bar" style="width: 0%"></div>
                    </div>
                    <div class="progress-info">
                        <small id="moduleProgressText">等待开始...</small>
                    </div>
                </div>

                <!-- 任务详情 -->
                <div id="currentTask"></div>
                <div id="taskHistory"></div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    var currentConfigs = {};
    var refreshInterval = null;
    
    console.log('数据更新配置页面初始化');
    
    // 初始化
    loadConfigs();
    initializeDateInputs();

    // 刷新配置
    $('#refreshBtn').click(function() {
        console.log('刷新配置按钮被点击');
        loadConfigs();
    });

    // 更新模式切换
    $('#updateMode').change(function() {
        var mode = $(this).val();
        if (mode === 'range' || mode === 'backfill') {
            $('#dateRangePanel').show();
        } else {
            $('#dateRangePanel').hide();
        }
    });
    
    // 全选/全不选
    $('#selectAllBtn').click(function() {
        $('.module-switch input').prop('checked', true).trigger('change');
    });
    
    $('#selectNoneBtn').click(function() {
        $('.module-switch input').prop('checked', false).trigger('change');
    });
    
    // 保存配置
    $('#saveConfigBtn').click(function() {
        saveAllConfigs();
    });
    
    // 开始更新
    $('#startUpdateBtn').click(function() {
        startUpdate();
    });
    
    // 停止更新
    $('#stopUpdateBtn').click(function() {
        stopUpdate();
    });
    
    // 类别筛选
    $('.filter-btn').click(function() {
        var category = $(this).data('category');
        filterByCategory(category);
    });

    // 初始化日期输入
    function initializeDateInputs() {
        var today = new Date();
        var yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);

        $('#endDate').val(formatDate(yesterday));

        var weekAgo = new Date(today);
        weekAgo.setDate(weekAgo.getDate() - 7);
        $('#startDate').val(formatDate(weekAgo));
    }

    // 格式化日期
    function formatDate(date) {
        var year = date.getFullYear();
        var month = String(date.getMonth() + 1).padStart(2, '0');
        var day = String(date.getDate()).padStart(2, '0');
        return year + '-' + month + '-' + day;
    }
    
    // 加载配置
    function loadConfigs() {
        console.log('开始加载配置');
        
        $.ajax({
            url: '/instock/admin/data-config/api',
            type: 'POST',
            data: { action: 'get_configs' },
            dataType: 'json',
            success: function(response) {
                console.log('配置加载成功:', response);
                if (response.success) {
                    currentConfigs = response.data.configs;
                    var categories = response.data.categories;
                    renderConfigs(currentConfigs, categories);
                } else {
                    alert('加载配置失败: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                console.log('配置加载失败:', xhr, status, error);
                alert('加载配置失败: ' + error);
            }
        });
    }
    
    // 渲染配置
    function renderConfigs(configs, categories) {
        var html = '';
        var totalModules = 0;
        var enabledModules = 0;
        var totalTime = 0;

        for (var categoryId in configs) {
            var categoryModules = configs[categoryId];
            var categoryInfo = categories[categoryId] || { name: categoryId, color: '#6c757d', icon: 'fa-folder' };

            totalModules += categoryModules.length;
            var categoryEnabled = categoryModules.filter(m => m.enabled).length;
            enabledModules += categoryEnabled;

            // 计算该类别的总时间
            var categoryTime = categoryModules.filter(m => m.enabled).reduce((sum, m) => sum + (m.estimated_time || 0), 0);
            totalTime += categoryTime;

            html += '<div class="config-card" data-category="' + categoryId + '">';
            html += '<div class="category-header" style="background: linear-gradient(135deg, ' + categoryInfo.color + ' 0%, ' + adjustColor(categoryInfo.color, -20) + ' 100%)">';
            html += '<h5><i class="fa ' + categoryInfo.icon + '"></i> ' + categoryInfo.name + '</h5>';
            html += '<div class="category-stats">';
            html += '<span>模块: ' + categoryModules.length + '</span>';
            html += '<span>启用: ' + categoryEnabled + '</span>';
            html += '<span>时间: ' + Math.round(categoryTime / 60) + '分钟</span>';
            html += '</div>';
            html += '</div>';

            for (var i = 0; i < categoryModules.length; i++) {
                var module = categoryModules[i];
                html += renderModuleItem(module);
            }

            html += '</div>';
        }

        $('#configContainer').html(html);

        // 更新统计信息
        updateStats(totalModules, enabledModules, totalTime);

        // 绑定事件
        bindEvents();

        // 更新执行计划
        updateExecutionPlan();
    }

    // 调整颜色亮度
    function adjustColor(color, percent) {
        var num = parseInt(color.replace("#",""), 16);
        var amt = Math.round(2.55 * percent);
        var R = (num >> 16) + amt;
        var G = (num >> 8 & 0x00FF) + amt;
        var B = (num & 0x0000FF) + amt;
        return "#" + (0x1000000 + (R<255?R<1?0:R:255)*0x10000 + (G<255?G<1?0:G:255)*0x100 + (B<255?B<1?0:B:255)).toString(16).slice(1);
    }

    // 更新统计信息
    function updateStats(total, enabled, time) {
        $('#totalModules').text(total);
        $('#enabledModules').text(enabled);
        $('#estimatedTime').text(Math.round(time / 60) + '分钟');
    }
    
    // 渲染单个模块
    function renderModuleItem(module) {
        var html = '<div class="module-item ' + (module.enabled ? '' : 'disabled') + '" data-module="' + module.module_id + '">';
        
        html += '<div class="module-header">';
        html += '<div class="module-title">' + module.name + '</div>';
        html += '<div class="module-controls">';
        html += '<label class="switch module-switch">';
        html += '<input type="checkbox" ' + (module.enabled ? 'checked' : '') + ' data-module="' + module.module_id + '">';
        html += '<span class="slider"></span>';
        html += '</label>';
        html += '</div>';
        html += '</div>';
        
        html += '<div class="module-description">' + module.description + '</div>';
        
        html += '<div class="module-meta">';
        html += '<div class="meta-item">';
        html += '<i class="fa fa-sort-numeric-asc"></i>';
        html += '<span>优先级: ' + module.priority + '</span>';
        html += '</div>';
        html += '<div class="meta-item">';
        html += '<i class="fa fa-clock-o"></i>';
        html += '<span>预估: ' + Math.round(module.estimated_time / 60) + '分钟</span>';
        html += '</div>';
        html += '<div class="meta-item data-size-' + module.data_size + '">';
        html += '<i class="fa fa-database"></i>';
        html += '<span>数据量: ' + module.data_size + '</span>';
        html += '</div>';
        html += '<div class="meta-item">';
        html += '<i class="fa fa-calendar"></i>';
        html += '<span>频率: ' + module.update_frequency + '</span>';
        html += '</div>';
        html += '<div class="meta-item">';
        html += '<span class="status-badge ' + (module.enabled ? 'status-enabled' : 'status-disabled') + '">';
        html += module.enabled ? '已启用' : '已禁用';
        html += '</span>';
        html += '</div>';
        html += '</div>';
        
        html += '</div>';
        
        return html;
    }
    
    // 绑定事件
    function bindEvents() {
        // 模块开关切换
        $('.module-switch input').off('change').on('change', function() {
            var moduleId = $(this).data('module');
            var enabled = $(this).is(':checked');
            var moduleItem = $(this).closest('.module-item');

            if (enabled) {
                moduleItem.removeClass('disabled');
                moduleItem.find('.status-badge').removeClass('status-disabled').addClass('status-enabled').text('已启用');
            } else {
                moduleItem.addClass('disabled');
                moduleItem.find('.status-badge').removeClass('status-enabled').addClass('status-disabled').text('已禁用');
            }

            console.log('模块状态切换:', moduleId, enabled);

            // 更新统计信息和执行计划
            updateStatsFromUI();
            updateExecutionPlan();
        });
    }

    // 从UI更新统计信息
    function updateStatsFromUI() {
        var totalModules = $('.module-switch input').length;
        var enabledModules = $('.module-switch input:checked').length;
        var totalTime = 0;

        $('.module-switch input:checked').each(function() {
            var moduleId = $(this).data('module');
            // 从当前配置中查找模块信息
            for (var categoryId in currentConfigs) {
                var modules = currentConfigs[categoryId];
                for (var i = 0; i < modules.length; i++) {
                    if (modules[i].module_id === moduleId) {
                        totalTime += modules[i].estimated_time || 0;
                        break;
                    }
                }
            }
        });

        updateStats(totalModules, enabledModules, totalTime);
    }

    // 更新执行计划
    function updateExecutionPlan() {
        var selectedModules = [];

        $('.module-switch input:checked').each(function() {
            var moduleId = $(this).data('module');
            // 从当前配置中查找模块信息
            for (var categoryId in currentConfigs) {
                var modules = currentConfigs[categoryId];
                for (var i = 0; i < modules.length; i++) {
                    if (modules[i].module_id === moduleId) {
                        selectedModules.push(modules[i]);
                        break;
                    }
                }
            }
        });

        // 按优先级排序
        selectedModules.sort(function(a, b) {
            return a.priority - b.priority;
        });

        var html = '';
        if (selectedModules.length === 0) {
            html = '<p class="text-muted">请选择要执行的模块</p>';
        } else {
            html = '<div class="plan-summary mb-2">';
            html += '<small class="text-muted">将按以下顺序执行 ' + selectedModules.length + ' 个模块:</small>';
            html += '</div>';

            for (var i = 0; i < selectedModules.length; i++) {
                var module = selectedModules[i];
                html += '<div class="plan-item selected">';
                html += '<div class="plan-module-name">' + (i + 1) + '. ' + module.name + '</div>';
                html += '<div class="plan-module-time">' + Math.round(module.estimated_time / 60) + '分钟</div>';
                html += '</div>';
            }
        }

        $('#planContent').html(html);
    }
    
    // 保存所有配置
    function saveAllConfigs() {
        var configs = [];
        
        $('.module-switch input').each(function() {
            var moduleId = $(this).data('module');
            var enabled = $(this).is(':checked');
            
            configs.push({
                module_id: moduleId,
                enabled: enabled
            });
        });
        
        console.log('保存配置:', configs);
        
        $.ajax({
            url: '/instock/admin/data-config/api',
            type: 'POST',
            data: {
                action: 'batch_update',
                configs: JSON.stringify(configs)
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    alert('配置保存成功');
                    loadConfigs(); // 重新加载配置
                } else {
                    alert('配置保存失败: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                alert('配置保存失败: ' + error);
            }
        });
    }
    
    // 开始更新
    function startUpdate() {
        var selectedModules = [];
        $('.module-switch input:checked').each(function() {
            selectedModules.push($(this).data('module'));
        });

        if (selectedModules.length === 0) {
            alert('请至少选择一个模块');
            return;
        }

        // 获取时间设置
        var updateMode = $('#updateMode').val();
        var startDate = $('#startDate').val();
        var endDate = $('#endDate').val();
        var updateFrequency = $('#updateFrequency').val();

        // 验证日期范围
        if ((updateMode === 'range' || updateMode === 'backfill') && (!startDate || !endDate)) {
            alert('请选择开始和结束日期');
            return;
        }

        if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
            alert('开始日期不能晚于结束日期');
            return;
        }

        console.log('开始更新，选中模块:', selectedModules);
        console.log('更新设置:', { updateMode, startDate, endDate, updateFrequency });

        // 显示任务状态面板
        $('#taskStatus').show();
        updateProgressDisplay(0, '准备启动任务...', selectedModules.length);

        $.ajax({
            url: '/instock/admin/data-config/api',
            type: 'POST',
            data: {
                action: 'start_update',
                modules: JSON.stringify(selectedModules),
                update_mode: updateMode,
                start_date: startDate,
                end_date: endDate,
                update_frequency: updateFrequency
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    showSuccessMessage('更新任务已启动: ' + response.data.task_id);
                    $('#startUpdateBtn').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 执行中...');
                    $('#stopUpdateBtn').prop('disabled', false);
                    startRefresh();
                } else {
                    showErrorMessage('启动失败: ' + response.message);
                    $('#taskStatus').hide();
                }
            },
            error: function(xhr, status, error) {
                showErrorMessage('启动失败: ' + error);
                $('#taskStatus').hide();
            }
        });
    }
    
    // 停止更新
    function stopUpdate() {
        $.ajax({
            url: '/instock/admin/data-config/api',
            type: 'POST',
            data: { action: 'stop_update' },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    showSuccessMessage('任务已停止');
                    resetTaskButtons();
                    stopRefresh();
                } else {
                    showErrorMessage('停止失败: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                showErrorMessage('停止失败: ' + error);
            }
        });
    }

    // 重置任务按钮状态
    function resetTaskButtons() {
        $('#startUpdateBtn').prop('disabled', false).html('<i class="fa fa-play"></i> 开始更新选中模块');
        $('#stopUpdateBtn').prop('disabled', true);
    }

    // 更新进度显示
    function updateProgressDisplay(percent, message, totalModules, currentModule) {
        $('#overallPercent').text(percent + '%');
        $('#overallProgressBar').css('width', percent + '%');
        $('#progressText').text(message);

        if (currentModule) {
            $('#moduleProgress').show();
            $('#currentModuleName').text(currentModule);
            // 这里可以添加单个模块的进度逻辑
        } else {
            $('#moduleProgress').hide();
        }
    }

    // 显示成功消息
    function showSuccessMessage(message) {
        // 可以使用更好的通知组件，这里先用alert
        alert('✅ ' + message);
    }

    // 显示错误消息
    function showErrorMessage(message) {
        // 可以使用更好的通知组件，这里先用alert
        alert('❌ ' + message);
    }
    
    // 开始刷新任务状态
    function startRefresh() {
        if (refreshInterval) {
            clearInterval(refreshInterval);
        }
        
        refreshInterval = setInterval(function() {
            refreshTaskStatus();
        }, 2000);
        
        $('#taskStatus').show();
    }
    
    // 停止刷新
    function stopRefresh() {
        if (refreshInterval) {
            clearInterval(refreshInterval);
            refreshInterval = null;
        }
    }
    
    // 刷新任务状态
    function refreshTaskStatus() {
        $.ajax({
            url: '/instock/admin/data-config/api',
            type: 'POST',
            data: { action: 'get_status' },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    renderTaskStatus(response.data);
                    
                    // 如果没有当前任务，停止刷新
                    if (!response.data.current_task) {
                        $('#startUpdateBtn').prop('disabled', false);
                        $('#stopUpdateBtn').prop('disabled', true);
                        stopRefresh();
                    }
                }
            },
            error: function() {
                // 忽略错误，继续刷新
            }
        });
    }
    
    // 渲染任务状态
    function renderTaskStatus(data) {
        if (data.current_task) {
            var task = data.current_task;

            // 更新整体进度
            updateProgressDisplay(
                task.progress || 0,
                task.current_module ? '正在执行: ' + task.current_module : '准备中...',
                task.modules ? task.modules.length : 0,
                task.current_module
            );

            // 渲染任务详情
            var html = '';
            html += '<div class="task-details mt-3">';
            html += '<h6><i class="fa fa-info-circle"></i> 任务详情</h6>';
            html += '<div class="task-info">';
            html += '<div class="info-item"><span class="label">任务ID:</span> <code>' + task.id + '</code></div>';
            html += '<div class="info-item"><span class="label">开始时间:</span> ' + task.start_time + '</div>';
            html += '<div class="info-item"><span class="label">模块总数:</span> ' + (task.modules ? task.modules.length : 0) + '</div>';
            html += '<div class="info-item"><span class="label">当前状态:</span> <span class="badge badge-primary">' + task.status + '</span></div>';
            html += '</div>';
            html += '</div>';

            if (task.logs && task.logs.length > 0) {
                html += '<div class="task-logs mt-3">';
                html += '<h6><i class="fa fa-list"></i> 执行日志</h6>';
                html += '<div class="log-container">';
                for (var i = Math.max(0, task.logs.length - 10); i < task.logs.length; i++) {
                    var log = task.logs[i];
                    var logClass = '';
                    if (log.message.includes('✅')) logClass = 'log-success';
                    else if (log.message.includes('❌')) logClass = 'log-error';
                    else if (log.message.includes('开始执行')) logClass = 'log-info';

                    html += '<div class="log-entry ' + logClass + '">';
                    html += '<span class="log-time">[' + log.time + ']</span> ';
                    html += '<span class="log-message">' + log.message + '</span>';
                    html += '</div>';
                }
                html += '</div>';
                html += '</div>';
            }

            $('#currentTask').html(html);
        } else {
            $('#currentTask').html('<p class="text-muted">暂无运行中的任务</p>');
            resetTaskButtons();
        }

        // 渲染历史任务
        if (data.task_history && data.task_history.length > 0) {
            var historyHtml = '<div class="task-history mt-3">';
            historyHtml += '<h6><i class="fa fa-history"></i> 最近任务</h6>';
            for (var i = 0; i < Math.min(3, data.task_history.length); i++) {
                var task = data.task_history[i];
                var statusClass = task.status === 'completed' ? 'success' : task.status === 'failed' ? 'danger' : 'warning';
                var statusIcon = task.status === 'completed' ? 'check' : task.status === 'failed' ? 'times' : 'clock-o';

                historyHtml += '<div class="history-item">';
                historyHtml += '<div class="history-header">';
                historyHtml += '<span class="history-id">' + task.id + '</span>';
                historyHtml += '<span class="badge badge-' + statusClass + '"><i class="fa fa-' + statusIcon + '"></i> ' + task.status + '</span>';
                historyHtml += '</div>';
                historyHtml += '<div class="history-time">' + task.start_time + '</div>';
                historyHtml += '</div>';
            }
            historyHtml += '</div>';
            $('#taskHistory').html(historyHtml);
        }
    }
    
    // 类别筛选
    function filterByCategory(category) {
        // 更新按钮状态
        $('.filter-btn').removeClass('active');
        if (category) {
            $('.filter-btn[data-category="' + category + '"]').addClass('active');
            $('.config-card').hide();
            $('.config-card[data-category="' + category + '"]').show();
        } else {
            $('.filter-btn[data-category=""]').addClass('active');
            $('.config-card').show();
        }
    }
});
</script>
{% end %}
