# Mermaid 图表无障碍设计指南

## 📋 设计原则

### 1. 通用设计原则
基于 [WCAG 2.1](https://www.w3.org/WAI/WCAG21/Understanding/) 和 [Section 508](https://www.section508.gov/) 标准：

- **可感知性 (Perceivable)**: 信息必须以用户能够感知的方式呈现
- **可操作性 (Operable)**: 界面组件必须是可操作的
- **可理解性 (Understandable)**: 信息和UI操作必须是可理解的
- **健壮性 (Robust)**: 内容必须足够健壮，能被各种辅助技术解释

### 2. 颜色设计原则
- **高对比度**: 文字与背景对比度 ≥ 4.5:1 (AA级) 或 ≥ 7:1 (AAA级)
- **色彩独立**: 不仅依靠颜色传达信息
- **色盲友好**: 避免红绿、蓝黄等色盲难以区分的组合
- **环境适应**: 在不同光照和显示环境下保持可读性

## 🎨 标准颜色方案

### 核心配色系统
```mermaid
%% 用户交互 - 蓝色系 (信任、稳定)
style UserNode fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#1976d2

%% 核心逻辑 - 紫色系 (智能、创新)  
style ProcessNode fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#7b1fa2

%% 执行引擎 - 橙色系 (活力、行动)
style ExecuteNode fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#f57c00

%% 完成状态 - 绿色系 (成功、完成)
style CompleteNode fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#388e3c

%% API处理 - 灰色系 (中性、工具)
style ApiNode fill:#f5f5f5,stroke:#424242,stroke-width:2px,color:#424242
```

### 语义化配色
| 语义 | 背景色 | 文字色 | 使用场景 | 对比度 |
|------|--------|--------|----------|--------|
| 信息 | `#e1f5fe` | `#0288d1` | 一般信息提示 | 7.8:1 |
| 成功 | `#e8f5e8` | `#388e3c` | 操作成功 | 7.1:1 |
| 警告 | `#fff8e1` | `#f9a825` | 注意事项 | 6.2:1 |
| 错误 | `#ffebee` | `#d32f2f` | 错误信息 | 8.9:1 |

## 🔧 实施规范

### Mermaid 样式语法
```mermaid
%% 完整样式定义格式
style NodeId fill:背景色,stroke:边框色,stroke-width:边框宽度,color:文字色

%% 示例
style A fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#1976d2
```

### 样式参数说明
- **fill**: 节点背景色，使用浅色 (#e0-#f9 范围)
- **color**: 文字颜色，使用深色 (#00-#80 范围)  
- **stroke**: 边框颜色，与文字颜色相同
- **stroke-width**: 边框宽度，推荐 2px

### 节点类型映射
```mermaid
graph TD
    A[开始节点] --> B{判断节点}
    B --> C[处理节点]
    C --> D[结束节点]
    
    %% 开始节点 - 用户交互样式
    style A fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#1976d2
    
    %% 判断节点 - API处理样式  
    style B fill:#f5f5f5,stroke:#424242,stroke-width:2px,color:#424242
    
    %% 处理节点 - 核心逻辑样式
    style C fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#7b1fa2
    
    %% 结束节点 - 完成状态样式
    style D fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#388e3c
```

## 📱 响应式设计

### 移动设备适配
```mermaid
%% 移动设备使用更粗边框
style MobileNode fill:#e3f2fd,stroke:#1976d2,stroke-width:3px,color:#1976d2
```

### 打印友好设计
```mermaid
%% 打印时使用最高对比度
style PrintNode fill:#ffffff,stroke:#000000,stroke-width:2px,color:#000000
```

### 高对比度模式
```css
@media (prefers-contrast: high) {
  .mermaid .node {
    fill: #ffffff !important;
    color: #000000 !important;
    stroke: #000000 !important;
    stroke-width: 3px !important;
  }
}
```

## 🧪 测试与验证

### 对比度测试
1. **自动化测试**
   ```javascript
   // 使用 axe-core 进行无障碍测试
   const axe = require('@axe-core/puppeteer');
   const results = await axe.analyze();
   ```

2. **手动测试工具**
   - [WebAIM Contrast Checker](https://webaim.org/resources/contrastchecker/)
   - [Colour Contrast Analyser](https://www.tpgi.com/color-contrast-checker/)

### 色盲测试
1. **在线模拟器**
   - [Coblis Color Blindness Simulator](https://www.color-blindness.com/coblis-color-blindness-simulator/)
   - [Stark](https://www.getstark.co/)

2. **浏览器扩展**
   - **Colorblinding** (Chrome)
   - **Let's get color blind** (Firefox)

### 多环境测试
- **操作系统**: Windows, macOS, Linux
- **浏览器**: Chrome, Firefox, Safari, Edge
- **设备**: 桌面、平板、手机
- **主题**: 明亮、深色、高对比度

## 📋 质量检查清单

### 设计阶段
- [ ] 所有颜色组合对比度 ≥ 4.5:1 (AA级)
- [ ] 推荐对比度 ≥ 7:1 (AAA级)
- [ ] 不仅依靠颜色区分信息
- [ ] 色盲友好测试通过
- [ ] 支持高对比度模式

### 开发阶段  
- [ ] 使用标准样式模板
- [ ] 添加适当的边框和间距
- [ ] 文字大小适中 (≥ 12px)
- [ ] 响应式设计适配

### 测试阶段
- [ ] 自动化无障碍测试通过
- [ ] 手动对比度检查通过
- [ ] 多环境兼容性测试通过
- [ ] 用户体验测试反馈良好

## 🚀 最佳实践

### 1. 渐进增强策略
```mermaid
%% 基础样式 (黑白兼容)
style BaseNode fill:#ffffff,stroke:#000000,stroke-width:2px,color:#000000

%% 增强样式 (彩色环境)
style EnhancedNode fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#1976d2
```

### 2. 语义化命名
```mermaid
%% 使用语义化的节点ID
style StartProcess fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#1976d2
style DataValidation fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#7b1fa2
style SuccessResult fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#388e3c
```

### 3. 一致性原则
- 相同类型的节点使用相同样式
- 建立样式库和组件系统
- 定期审查和更新标准

### 4. 文档化
- 记录每种颜色的使用场景
- 提供样式模板和示例
- 建立设计系统文档

## 📚 参考标准

### 国际标准
- **WCAG 2.1**: [Web Content Accessibility Guidelines](https://www.w3.org/WAI/WCAG21/Understanding/)
- **Section 508**: [美国联邦无障碍标准](https://www.section508.gov/)
- **EN 301 549**: [欧洲无障碍标准](https://www.etsi.org/deliver/etsi_en/301500_301599/301549/)

### 设计指南
- **Material Design**: [Accessibility](https://material.io/design/usability/accessibility.html)
- **Apple HIG**: [Color and Contrast](https://developer.apple.com/design/human-interface-guidelines/accessibility/overview/color-and-contrast/)
- **Microsoft Fluent**: [Inclusive Design](https://www.microsoft.com/design/inclusive/)

### 技术文档
- **Mermaid**: [Official Documentation](https://mermaid-js.github.io/mermaid/)
- **CSS Color**: [MDN Color Reference](https://developer.mozilla.org/en-US/docs/Web/CSS/color)
- **Contrast Ratio**: [WCAG Definition](https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html)

这个指南确保了 Mermaid 图表在设计和实现过程中始终遵循无障碍设计原则，为所有用户提供良好的使用体验。
