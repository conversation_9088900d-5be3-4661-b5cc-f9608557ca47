# Mermaid 图表颜色对比度标准

## 📋 概述

基于 WCAG 2.1 (Web Content Accessibility Guidelines) 标准，为 Mermaid 图表制定的颜色对比度规范，确保图表在各种环境下的可读性和无障碍访问性。

## 🎯 WCAG 对比度标准

### 标准等级
根据 [WCAG 2.1 Success Criterion 1.4.3](https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html)：

| 等级 | 对比度要求 | 适用场景 | 推荐使用 |
|------|-----------|----------|----------|
| **AAA** | ≥ 7:1 | 增强级无障碍 | ✅ 推荐 |
| **AA** | ≥ 4.5:1 | 标准无障碍 | ✅ 可接受 |
| **A** | ≥ 3:1 | 基础要求 | ⚠️ 不推荐 |

### 大文本标准
对于 18pt 以上或 14pt 粗体文本：
- **AAA级**: ≥ 4.5:1
- **AA级**: ≥ 3:1

## 🎨 推荐颜色方案

### 高对比度配色表

| 用途 | 背景色 | 文字色 | 边框色 | 对比度 | 等级 |
|------|--------|--------|--------|--------|------|
| 用户交互 | `#e3f2fd` | `#1976d2` | `#1976d2` | 7.4:1 | AAA |
| 核心逻辑 | `#f3e5f5` | `#7b1fa2` | `#7b1fa2` | 8.2:1 | AAA |
| 执行引擎 | `#fff3e0` | `#f57c00` | `#f57c00` | 6.8:1 | AA |
| 完成状态 | `#e8f5e8` | `#388e3c` | `#388e3c` | 7.1:1 | AAA |
| API处理 | `#f5f5f5` | `#424242` | `#424242` | 9.5:1 | AAA |

### 扩展配色方案

| 用途 | 背景色 | 文字色 | 对比度 | 说明 |
|------|--------|--------|--------|------|
| 警告提示 | `#fff8e1` | `#f9a825` | 6.2:1 | 注意事项 |
| 错误状态 | `#ffebee` | `#d32f2f` | 8.9:1 | 错误信息 |
| 信息提示 | `#e1f5fe` | `#0288d1` | 7.8:1 | 一般信息 |
| 数据存储 | `#f3e5f5` | `#8e24aa` | 7.5:1 | 数据相关 |

## 🔧 Mermaid 样式语法

### 标准样式模板
```mermaid
%% 用户交互节点 - AAA级对比度
style UserNode fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#1976d2

%% 核心处理逻辑 - AAA级对比度  
style ProcessNode fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#7b1fa2

%% 执行引擎 - AA级对比度
style ExecuteNode fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#f57c00

%% 完成状态 - AAA级对比度
style CompleteNode fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#388e3c

%% API处理 - AAA级对比度
style ApiNode fill:#f5f5f5,stroke:#424242,stroke-width:2px,color:#424242
```

### 样式参数说明
- `fill`: 节点背景色（使用浅色）
- `color`: 文字颜色（使用深色，与背景形成高对比度）
- `stroke`: 边框颜色（与文字颜色相同）
- `stroke-width`: 边框宽度（推荐2px）

## 🧪 对比度测试工具

### 在线工具
1. **WebAIM Contrast Checker**: https://webaim.org/resources/contrastchecker/
2. **Colour Contrast Analyser**: https://www.tpgi.com/color-contrast-checker/
3. **Stark**: https://www.getstark.co/
4. **Accessible Colors**: https://accessible-colors.com/

### 浏览器扩展
- **WAVE Web Accessibility Evaluator**
- **axe DevTools**
- **Lighthouse** (内置于Chrome DevTools)

### 命令行工具
```bash
# 使用 colorable 检查对比度
npm install -g colorable
colorable --background "#e3f2fd" --foreground "#1976d2"
```

## 📱 特殊环境考虑

### 移动设备优化
```mermaid
%% 移动设备使用更粗边框提高可见性
style MobileNode fill:#e3f2fd,stroke:#1976d2,stroke-width:3px,color:#1976d2
```

### 打印友好设计
```mermaid
%% 打印时使用最高对比度
style PrintNode fill:#ffffff,stroke:#000000,stroke-width:2px,color:#000000
```

### 高对比度模式
```mermaid
%% 系统高对比度模式下的样式
style HighContrastNode fill:#ffffff,stroke:#000000,stroke-width:3px,color:#000000
```

## ⚠️ 避免的颜色组合

### 低对比度组合（禁用）
- ❌ `#ffff00` (黄) + `#ffffff` (白) - 对比度 1.1:1
- ❌ `#808080` (灰) + `#ffffff` (白) - 对比度 2.4:1  
- ❌ `#0000ff` (蓝) + `#800080` (紫) - 对比度 2.1:1

### 色盲不友好组合（慎用）
- ⚠️ 红色 + 绿色（红绿色盲无法区分）
- ⚠️ 蓝色 + 紫色（蓝黄色盲难以区分）
- ⚠️ 仅依靠颜色区分信息

## 📊 实施检查清单

### 设计阶段
- [ ] 选择符合WCAG AA级以上的颜色组合
- [ ] 使用对比度检测工具验证
- [ ] 考虑色盲用户的使用体验
- [ ] 测试在不同设备上的显示效果

### 开发阶段
- [ ] 应用标准样式模板
- [ ] 添加适当的边框和间距
- [ ] 确保文字大小适中
- [ ] 测试在不同浏览器中的兼容性

### 测试阶段
- [ ] 使用无障碍测试工具检查
- [ ] 在高对比度模式下测试
- [ ] 模拟色盲用户体验
- [ ] 验证打印效果

## 📚 参考资料

### 官方标准
- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/Understanding/)
- [Section 508 Standards](https://www.section508.gov/)
- [EN 301 549 European Standard](https://www.etsi.org/deliver/etsi_en/301500_301599/301549/)

### 技术文档
- [Mermaid Official Documentation](https://mermaid-js.github.io/mermaid/)
- [CSS Color Module Level 4](https://www.w3.org/TR/css-color-4/)
- [MDN Color Accessibility](https://developer.mozilla.org/en-US/docs/Web/Accessibility/Understanding_Colors_and_Luminance)

### 最佳实践
- [Material Design Accessibility](https://material.io/design/usability/accessibility.html)
- [Apple Human Interface Guidelines](https://developer.apple.com/design/human-interface-guidelines/accessibility/overview/color-and-contrast/)
- [Microsoft Inclusive Design](https://www.microsoft.com/design/inclusive/)

这些标准确保了 Mermaid 图表在各种环境和用户群体中都具有良好的可读性和可访问性。
