graph TD
    A[用户启动回补] --> B{选择回补方式}
    
    B --> C[方案1: data_backfill.sh脚本]
    B --> D[方案2: 直接执行Python脚本]
    
    C --> C1[显示菜单选项]
    C1 --> C2[1. 回补最近一个月]
    C1 --> C3[2. 回补指定日期区间]
    C1 --> C4[3. 回补指定日期]
    C1 --> C5[4. 回补多个指定日期]
    C1 --> C6[5. 查看数据回补状态]
    
    C2 --> E[docker exec InStock python execute_daily_job.py 2024-07-19 2024-08-19]
    C3 --> F[docker exec InStock python execute_daily_job.py START_DATE END_DATE]
    C4 --> G[docker exec InStock python execute_daily_job.py SINGLE_DATE]
    C5 --> H[docker exec InStock python execute_daily_job.py DATE1,DATE2,DATE3]
    
    D --> D1[execute_daily_job.py]
    D --> D2[basic_data_daily_job.py]
    D --> D3[indicators_data_daily_job.py]
    D --> D4[strategy_data_daily_job.py]
    
    E --> I[execute_daily_job.py主流程]
    F --> I
    G --> I
    H --> I
    
    I --> I1[第1步: init_job.main - 创建数据库]
    I1 --> I2[第2.1步: basic_data_daily_job.main - 股票基础数据]
    I2 --> I3[第2.2步: selection_data_daily_job.main - 综合选股数据]
    I3 --> I4[第3步: 并发执行多个任务]
    
    I4 --> I4A[basic_data_other_daily_job.main - 其他基础数据]
    I4 --> I4B[indicators_data_daily_job.main - 技术指标数据]
    I4 --> I4C[klinepattern_data_daily_job.main - K线形态数据]
    I4 --> I4D[strategy_data_daily_job.main - 策略数据]
    
    I4A --> I5[第6步: backtest_data_daily_job.main - 回测数据]
    I4B --> I5
    I4C --> I5
    I4D --> I5
    
    I5 --> I6[第7步: basic_data_after_close_daily_job.main - 闭盘后数据]
    I6 --> I7[任务完成 - 记录执行时间]
    
    %% 用户交互节点 - 浅蓝色背景，深蓝色文字
    style A fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#1976d2

    %% 核心处理逻辑 - 浅紫色背景，深紫色文字
    style I fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#7b1fa2

    %% 完成状态 - 浅绿色背景，深绿色文字
    style I7 fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#388e3c

    %% 方案选择 - 浅橙色背景，深橙色文字
    style C fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#f57c00
    style D fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#f57c00

    %% 执行步骤 - 浅灰色背景，深灰色文字
    style I1 fill:#f5f5f5,stroke:#424242,stroke-width:2px,color:#424242
    style I2 fill:#f5f5f5,stroke:#424242,stroke-width:2px,color:#424242
    style I3 fill:#f5f5f5,stroke:#424242,stroke-width:2px,color:#424242
    style I4 fill:#f5f5f5,stroke:#424242,stroke-width:2px,color:#424242
    style I5 fill:#f5f5f5,stroke:#424242,stroke-width:2px,color:#424242
    style I6 fill:#f5f5f5,stroke:#424242,stroke-width:2px,color:#424242
