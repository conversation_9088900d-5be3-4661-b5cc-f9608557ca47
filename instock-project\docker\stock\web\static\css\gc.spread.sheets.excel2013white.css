@charset "UTF-8";
/*----- css variables -----*/
:root {
  --sjs-theme-font-family: "Segoe UI", Cal<PERSON><PERSON>, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
  --sjs-theme-font-size: 12px;
  --sjs-theme-accent: #217346;
  --sjs-theme-accent-color: white;
  --sjs-theme-background: white;
  --sjs-theme-color: #333;
  --sjs-theme-light-gray-bg: rgba(51,51,51,0.05);
  --sjs-theme-gray-bg: rgba(51,51,51,.1);
  --sjs-theme-border-color: #DAE2ED;
  --sjs-theme-error-color: #BE1F1F;
}

/*-----common css start-----*/
/*-----common css start-----*/
.gc-clear-float:after {
  content: "";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

[gcuielement=gcSpread]:after {
  content: "";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.gc-theme-version {
  position: absolute;
  z-index: 2013;
}

.gc-footer-corner {
  background-color: #f6f6f6;
}

.gc-grayArea {
  background-color: white;
}

.gc-corner-hover {
  background-color: white;
}

.gc-corner-selected {
  background-color: white;
}

.gc-corner-normal {
  background-color: white;
}

.gc-rowHeaderFill {
  background-color: #e4ecf7;
}

.gc-colHeaderFill {
  background-image: -webkit-linear-gradient(top, #f6fafb 12.5%, #d2dbeb);
  /* For Chrome and Safari */
  background-image: -moz-linear-gradient(top, #f6fafb 12.5%, #d2dbeb);
  /* For old Fx (3.6 to 15) */
  background-image: -ms-linear-gradient(top, #f6fafb 12.5%, #d2dbeb);
  /* For pre-releases of IE 10*/
  background-image: -o-linear-gradient(top, #f6fafb 12.5%, #d2dbeb);
  /* For old Opera (11.1 to 12.0) */
  background-image: linear-gradient(to bottom, #f6fafb 12.5%, #d2dbeb);
  /* Standard syntax; must be last */
  background-color: #d2dbeb;
}

.gc-selection {
  background-color: rgba(20, 20, 20, 0.2);
  border-color: #217346;
  color: rgba(240, 240, 240, 0.7);
}

.gc-drag-header-mask {
  background-color: rgba(20, 20, 20, 0.2);
}

.gc-drag-header-indicator {
  border-color: #217346;
}

.gc-drag-indicator {
  border-color: #217346;
}

.gc-gridlineColor {
  border-color: #d4d4d4;
}

.gc-group {
  background-color: white;
  color: #ababab;
}

.gc-group-box {
  background-color: white;
  color: #666666;
  border-color: #828790;
}

.gc-group-line {
  border-width: 2px;
  border-style: solid;
  border-color: #ababab;
}

.fillSmartTag {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAKT2lDQ1BQaG90b3Nob3AgSUNDIHByb2ZpbGUAAHjanVNnVFPpFj333vRCS4iAlEtvUhUIIFJCi4AUkSYqIQkQSoghodkVUcERRUUEG8igiAOOjoCMFVEsDIoK2AfkIaKOg6OIisr74Xuja9a89+bN/rXXPues852zzwfACAyWSDNRNYAMqUIeEeCDx8TG4eQuQIEKJHAAEAizZCFz/SMBAPh+PDwrIsAHvgABeNMLCADATZvAMByH/w/qQplcAYCEAcB0kThLCIAUAEB6jkKmAEBGAYCdmCZTAKAEAGDLY2LjAFAtAGAnf+bTAICd+Jl7AQBblCEVAaCRACATZYhEAGg7AKzPVopFAFgwABRmS8Q5ANgtADBJV2ZIALC3AMDOEAuyAAgMADBRiIUpAAR7AGDIIyN4AISZABRG8lc88SuuEOcqAAB4mbI8uSQ5RYFbCC1xB1dXLh4ozkkXKxQ2YQJhmkAuwnmZGTKBNA/g88wAAKCRFRHgg/P9eM4Ors7ONo62Dl8t6r8G/yJiYuP+5c+rcEAAAOF0ftH+LC+zGoA7BoBt/qIl7gRoXgugdfeLZrIPQLUAoOnaV/Nw+H48PEWhkLnZ2eXk5NhKxEJbYcpXff5nwl/AV/1s+X48/Pf14L7iJIEyXYFHBPjgwsz0TKUcz5IJhGLc5o9H/LcL//wd0yLESWK5WCoU41EScY5EmozzMqUiiUKSKcUl0v9k4t8s+wM+3zUAsGo+AXuRLahdYwP2SycQWHTA4vcAAPK7b8HUKAgDgGiD4c93/+8//UegJQCAZkmScQAAXkQkLlTKsz/HCAAARKCBKrBBG/TBGCzABhzBBdzBC/xgNoRCJMTCQhBCCmSAHHJgKayCQiiGzbAdKmAv1EAdNMBRaIaTcA4uwlW4Dj1wD/phCJ7BKLyBCQRByAgTYSHaiAFiilgjjggXmYX4IcFIBBKLJCDJiBRRIkuRNUgxUopUIFVIHfI9cgI5h1xGupE7yAAygvyGvEcxlIGyUT3UDLVDuag3GoRGogvQZHQxmo8WoJvQcrQaPYw2oefQq2gP2o8+Q8cwwOgYBzPEbDAuxsNCsTgsCZNjy7EirAyrxhqwVqwDu4n1Y8+xdwQSgUXACTYEd0IgYR5BSFhMWE7YSKggHCQ0EdoJNwkDhFHCJyKTqEu0JroR+cQYYjIxh1hILCPWEo8TLxB7iEPENyQSiUMyJ7mQAkmxpFTSEtJG0m5SI+ksqZs0SBojk8naZGuyBzmULCAryIXkneTD5DPkG+Qh8lsKnWJAcaT4U+IoUspqShnlEOU05QZlmDJBVaOaUt2ooVQRNY9aQq2htlKvUYeoEzR1mjnNgxZJS6WtopXTGmgXaPdpr+h0uhHdlR5Ol9BX0svpR+iX6AP0dwwNhhWDx4hnKBmbGAcYZxl3GK+YTKYZ04sZx1QwNzHrmOeZD5lvVVgqtip8FZHKCpVKlSaVGyovVKmqpqreqgtV81XLVI+pXlN9rkZVM1PjqQnUlqtVqp1Q61MbU2epO6iHqmeob1Q/pH5Z/YkGWcNMw09DpFGgsV/jvMYgC2MZs3gsIWsNq4Z1gTXEJrHN2Xx2KruY/R27iz2qqaE5QzNKM1ezUvOUZj8H45hx+Jx0TgnnKKeX836K3hTvKeIpG6Y0TLkxZVxrqpaXllirSKtRq0frvTau7aedpr1Fu1n7gQ5Bx0onXCdHZ4/OBZ3nU9lT3acKpxZNPTr1ri6qa6UbobtEd79up+6Ynr5egJ5Mb6feeb3n+hx9L/1U/W36p/VHDFgGswwkBtsMzhg8xTVxbzwdL8fb8VFDXcNAQ6VhlWGX4YSRudE8o9VGjUYPjGnGXOMk423GbcajJgYmISZLTepN7ppSTbmmKaY7TDtMx83MzaLN1pk1mz0x1zLnm+eb15vft2BaeFostqi2uGVJsuRaplnutrxuhVo5WaVYVVpds0atna0l1rutu6cRp7lOk06rntZnw7Dxtsm2qbcZsOXYBtuutm22fWFnYhdnt8Wuw+6TvZN9un2N/T0HDYfZDqsdWh1+c7RyFDpWOt6azpzuP33F9JbpL2dYzxDP2DPjthPLKcRpnVOb00dnF2e5c4PziIuJS4LLLpc+Lpsbxt3IveRKdPVxXeF60vWdm7Obwu2o26/uNu5p7ofcn8w0nymeWTNz0MPIQ+BR5dE/C5+VMGvfrH5PQ0+BZ7XnIy9jL5FXrdewt6V3qvdh7xc+9j5yn+M+4zw33jLeWV/MN8C3yLfLT8Nvnl+F30N/I/9k/3r/0QCngCUBZwOJgUGBWwL7+Hp8Ib+OPzrbZfay2e1BjKC5QRVBj4KtguXBrSFoyOyQrSH355jOkc5pDoVQfujW0Adh5mGLw34MJ4WHhVeGP45wiFga0TGXNXfR3ENz30T6RJZE3ptnMU85ry1KNSo+qi5qPNo3ujS6P8YuZlnM1VidWElsSxw5LiquNm5svt/87fOH4p3iC+N7F5gvyF1weaHOwvSFpxapLhIsOpZATIhOOJTwQRAqqBaMJfITdyWOCnnCHcJnIi/RNtGI2ENcKh5O8kgqTXqS7JG8NXkkxTOlLOW5hCepkLxMDUzdmzqeFpp2IG0yPTq9MYOSkZBxQqohTZO2Z+pn5mZ2y6xlhbL+xW6Lty8elQfJa7OQrAVZLQq2QqboVFoo1yoHsmdlV2a/zYnKOZarnivN7cyzytuQN5zvn//tEsIS4ZK2pYZLVy0dWOa9rGo5sjxxedsK4xUFK4ZWBqw8uIq2Km3VT6vtV5eufr0mek1rgV7ByoLBtQFr6wtVCuWFfevc1+1dT1gvWd+1YfqGnRs+FYmKrhTbF5cVf9go3HjlG4dvyr+Z3JS0qavEuWTPZtJm6ebeLZ5bDpaql+aXDm4N2dq0Dd9WtO319kXbL5fNKNu7g7ZDuaO/PLi8ZafJzs07P1SkVPRU+lQ27tLdtWHX+G7R7ht7vPY07NXbW7z3/T7JvttVAVVN1WbVZftJ+7P3P66Jqun4lvttXa1ObXHtxwPSA/0HIw6217nU1R3SPVRSj9Yr60cOxx++/p3vdy0NNg1VjZzG4iNwRHnk6fcJ3/ceDTradox7rOEH0x92HWcdL2pCmvKaRptTmvtbYlu6T8w+0dbq3nr8R9sfD5w0PFl5SvNUyWna6YLTk2fyz4ydlZ19fi753GDborZ752PO32oPb++6EHTh0kX/i+c7vDvOXPK4dPKy2+UTV7hXmq86X23qdOo8/pPTT8e7nLuarrlca7nuer21e2b36RueN87d9L158Rb/1tWeOT3dvfN6b/fF9/XfFt1+cif9zsu72Xcn7q28T7xf9EDtQdlD3YfVP1v+3Njv3H9qwHeg89HcR/cGhYPP/pH1jw9DBY+Zj8uGDYbrnjg+OTniP3L96fynQ89kzyaeF/6i/suuFxYvfvjV69fO0ZjRoZfyl5O/bXyl/erA6xmv28bCxh6+yXgzMV70VvvtwXfcdx3vo98PT+R8IH8o/2j5sfVT0Kf7kxmTk/8EA5jz/GMzLdsAAAAgY0hSTQAAeiUAAICDAAD5/wAAgOkAAHUwAADqYAAAOpgAABdvkl/FRgAAAFJJREFUeNrclEEKACAIBOfp/Xy7i4WSRnRYBIVRVhFJVIhyEAxllQa5E/wBSnsU6Rza2nugqNmASi57C/KKNg/Iqn+iVWzx6M4bOdUEAAD//wMAAYRMfiNaiqEAAAAASUVORK5CYII=);
  width: 18px;
  height: 18px;
}

.fillSmartTagDown {
  height: 18px;
  width: 10px;
  background-repeat: no-repeat;
  background-image: url(data:image/png;base64,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);
}

.gc-group-dot {
  color: #ababab;
}

.gc-group-box-expand,
.gc-group-box-collapsed {
  border-color: transparent;
  background-color: transparent;
}

.gc-tabStripBackground {
  background-image: -webkit-linear-gradient(top, #f6f6f6, #f6f6f6);
  /* For Chrome and Safari */
  background-image: -moz-linear-gradient(top, #f6f6f6, #f6f6f6);
  /* For old Fx (3.6 to 15) */
  background-image: -ms-linear-gradient(top, #f6f6f6, #f6f6f6);
  /* For pre-releases of IE 10*/
  background-image: -o-linear-gradient(top, #f6f6f6, #f6f6f6);
  /* For old Opera (11.1 to 12.0) */
  background-image: linear-gradient(to bottom, #f6f6f6, #f6f6f6);
  /* Standard syntax; must be last */
  background-color: #f6f6f6;
  border-color: #ababab;
}

.gc-tabStripResizeBarInner {
  color: #b3b3b3;
}

.gc-navButton-hover {
  border-color: #439467;
}

.gc-all-sheets-button-hover {
  border-color: #439467;
}

.gc-tab-indicator-bottom {
  width: 0;
  position: absolute;
  border-style: solid;
  border-color: black;
  border-width: 4px;
  display: none;
  border-bottom: 0;
  border-left-color: transparent;
  border-right-color: transparent;
}

.gc-tab-indicator-top {
  width: 0;
  position: absolute;
  border-style: solid;
  border-color: black;
  border-width: 4px;
  display: none;
  border-top: 0;
  border-left-color: transparent;
  border-right-color: transparent;
}

.gc-tab-indicator-left {
  width: 0;
  position: absolute;
  border-style: solid;
  border-color: black;
  border-width: 4px;
  display: none;
  border-left: 0;
  border-top-color: transparent;
  border-bottom-color: transparent;
}

.gc-tab-indicator-right {
  width: 0;
  position: absolute;
  border-style: solid;
  border-color: black;
  border-width: 4px;
  display: none;
  border-right: 0;
  border-top-color: transparent;
  border-bottom-color: transparent;
}

.gc-navButton-highlight {
  border-color: #0a6332;
}

.gc-navButton-normal {
  border-color: #c6c6c6;
}

.gc-all-sheets-button-highlight {
  border-color: #0a6332;
}

.gc-all-sheets-button-normal {
  border-color: #c6c6c6;
}

.gc-tabStripNewTab-highlight {
  border-color: #777777;
}

.gc-tabStripNewTab-hover {
  border-color: #439467;
}

.gc-navMoreButton-highlight {
  border-color: #0a6332;
}

.gc-navMoreButton-hover {
  border-color: #439467;
}

.gc-tab-strip-all-sheets-highlight {
  border-color: #0a6332;
}

.gc-tab-strip-all-sheets-hover {
  border-color: #439467;
}

.gc-columnHeader-hover, .gc-add-column-button-hover {
  color: #444444;
  background-image: none;
  background-color: #9fd5b7;
  border-style: solid;
  border-left-color: #efefef !important;
  border-right-color: #d5ded5 !important;
  border-bottom-color: #ababab !important;
}

.gc-columnHeader-selected, .gc-add-column-button-selected {
  color: #217346;
  background-color: #d3f0e0;
  background-image: none;
  border-style: solid;
  border-left-color: #efefef !important;
  border-right-color: #d5ded5 !important;
  border-bottom-color: #ababab !important;
}

.gc-columnHeader-highlight, .gc-add-column-button-highlight {
  color: #217346;
  background-image: none;
  background-color: #e1e1e1;
  border-style: solid;
  border-left-color: #efefef !important;
  border-right-color: #d5ded5 !important;
  border-bottom-color: #ababab !important;
}

.gc-columnHeader-normal, .gc-add-column-button-normal {
  color: #444444;
  background-image: none;
  background-color: white;
  border-style: solid;
  border-left-color: #efefef !important;
  border-right-color: #d5ded5 !important;
  border-bottom-color: #ababab !important;
}

.gc-rowHeader-hover, .gc-add-row-button-hover {
  color: #444444;
  background-color: #9fd5b7;
  background-image: none;
  border-style: solid;
  border-top-color: #efefef !important;
  border-right-color: #ababab !important;
  border-bottom-color: #d5ded5 !important;
}

.gc-rowHeader-selected, .gc-add-row-button-selected {
  color: #217346;
  background-color: #d3f0e0;
  background-image: none;
  border-style: solid;
  border-top-color: #efefef !important;
  border-right-color: #ababab !important;
  border-bottom-color: #d5ded5 !important;
}

.gc-rowHeader-highlight, .gc-add-row-button-highlight {
  color: #217346;
  background-color: #e1e1e1;
  background-image: none;
  border-style: solid;
  border-top-color: #efefef !important;
  border-right-color: #ababab !important;
  border-bottom-color: #d5ded5 !important;
}

.gc-rowHeader-normal, .gc-add-row-button-normal {
  color: #444444;
  background-color: white;
  background-image: none;
  border-style: solid;
  border-top-color: #efefef !important;
  border-right-color: #ababab !important;
  border-bottom-color: #d5ded5 !important;
}

.gc-corner-triangle-normal {
  background-image: -webkit-linear-gradient(top, #dfdfdf, #dfdfdf);
  /* For Chrome and Safari */
  background-image: -moz-linear-gradient(top, #dfdfdf, #dfdfdf);
  /* For old Fx (3.6 to 15) */
  background-image: -ms-linear-gradient(top, #dfdfdf, #dfdfdf);
  /* For pre-releases of IE 10*/
  background-image: -o-linear-gradient(top, #dfdfdf, #dfdfdf);
  /* For old Opera (11.1 to 12.0) */
  background-image: linear-gradient(to bottom, #dfdfdf, #dfdfdf);
  /* Standard syntax; must be last */
  background-color: #dfdfdf;
  border-style: solid;
  border-left-color: #efefef !important;
  border-right-color: #d5ded5 !important;
  border-top-color: #efefef !important;
  border-bottom-color: #d5ded5 !important;
}

#gc-search-tr .ui-widget-header {
  background: #fcfdfd;
  background-color: #fcfdfd;
  border: 0;
}

.gc-corner-triangle-hover {
  background-image: -webkit-linear-gradient(top, #9e9e9e, #9e9e9e);
  /* For Chrome and Safari */
  background-image: -moz-linear-gradient(top, #9e9e9e, #9e9e9e);
  /* For old Fx (3.6 to 15) */
  background-image: -ms-linear-gradient(top, #9e9e9e, #9e9e9e);
  /* For pre-releases of IE 10*/
  background-image: -o-linear-gradient(top, #9e9e9e, #9e9e9e);
  /* For old Opera (11.1 to 12.0) */
  background-image: linear-gradient(to bottom, #9e9e9e, #9e9e9e);
  /* Standard syntax; must be last */
  background-color: #9e9e9e;
  border-style: solid;
  border-left-color: #efefef !important;
  border-right-color: #d5ded5 !important;
  border-top-color: #efefef !important;
  border-bottom-color: #d5ded5 !important;
}

.gc-corner-triangle-selected {
  background-image: -webkit-linear-gradient(top, #217346, #217346);
  /* For Chrome and Safari */
  background-image: -moz-linear-gradient(top, #217346, #217346);
  /* For old Fx (3.6 to 15) */
  background-image: -ms-linear-gradient(top, #217346, #217346);
  /* For pre-releases of IE 10*/
  background-image: -o-linear-gradient(top, #217346, #217346);
  /* For old Opera (11.1 to 12.0) */
  background-image: linear-gradient(to bottom, #217346, #217346);
  /* Standard syntax; must be last */
  background-color: #217346;
  border-style: solid;
  border-left-color: #efefef !important;
  border-right-color: #d5ded5 !important;
  border-top-color: #efefef !important;
  border-bottom-color: #d5ded5 !important;
}

.gc-tab-normal {
  color: #444444;
  background-image: none;
  background-color: transparent;
  border-style: solid;
  border-left-color: #ababab;
  border-bottom-color: #217346;
}

.gc-tab-hover {
  color: #252627;
  background-image: none;
  background-color: transparent;
  border-style: solid;
  border-left-color: #ababab;
  border-bottom-color: #217346;
}

.gc-tab-selected {
  color: black;
  background-image: none;
  background-color: white;
  border-style: solid;
  border-left-color: #999999;
  border-bottom-color: #217346;
}

.gc-tab-activeNotSelected {
  color: #217346;
  background-image: none;
  background-color: transparent;
  border-style: solid;
  border-left-color: #ababab;
}

.gc-tab-active {
  color: #217346;
  background-image: none;
  background-color: white;
  border-style: solid;
  border-left-color: #ababab;
  border-bottom-color: #217346;
}

.gc-gradientButton {
  background-image: -webkit-linear-gradient(top, #f6fafb, #d2dbeb);
  /* For Chrome and Safari */
  background-image: -moz-linear-gradient(top, #f6fafb, #d2dbeb);
  /* For old Fx (3.6 to 15) */
  background-image: -ms-linear-gradient(top, #f6fafb, #d2dbeb);
  /* For pre-releases of IE 10*/
  background-image: -o-linear-gradient(top, #f6fafb, #d2dbeb);
  /* For old Opera (11.1 to 12.0) */
  background-image: linear-gradient(to bottom, #f6fafb, #d2dbeb);
  /* Standard syntax; must be last */
  background-color: #dddddd;
}

.gc-sheetTabEditor::-ms-clear {
  display: none;
}

.gc-layout-table {
  font-size: 12px;
  width: 100%;
  height: 100%;
  font-family: "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
}

.gc-layout-table-first-column {
  width: 21px;
  border-right: 1px solid #ccc;
  text-align: right;
  padding-top: 7px;
}

.gc-layout-table-last-column {
  width: 18px;
}

.gc-filter-sort-desc-container {
  border-bottom: 1px solid #ccc;
}

.gc-filter-top10-rank {
  outline: none;
}

.gc-filter-top10-rank-illegal:focus {
  outline: 1px solid red;
}

.gc-filter-checked {
  border: 1px solid #999;
  background-color: rgba(140, 140, 140, 0.2);
}

.gc-filterDialog-rightArrow {
  display: inline-block;
  height: 10px;
  background-repeat: no-repeat;
  background-position: center center;
  width: 100%;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAAXNSR0IArs4c6QAAAGBJREFUGBljYCAHzJo1a8L///+ZsOlFEQQqygcqXr1q1SpOdMUoCqGSQe/fv987c+ZMEWTF2BSC5C0ZGRkXEaPwONAZccgKWZA5UPY6QUHBmLCwsO9Y5CBC+HyNUxPZEgCPEyCkHm49GwAAAABJRU5ErkJggg==);
}

.gc-filter-disable-item .gc-filterDialog-rightArrow {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAAXNSR0IArs4c6QAAAGVJREFUGBljYCAH3L17d8L///+ZCOoFKvwPxGsfP37Mia4Ym+6g379/73327JkIsmJsChmA1lt+//59EbJCFmQOjM3IyHicg4MjDsYH0dhMXMfKyuosJSX1BlkhCptoX6PoIpcDAIoHJuArInm+AAAAAElFTkSuQmCC);
}

.gc-filter-dialog-style {
  text-align: initial;
  background: #fcfdfd;
  font-family: "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
  font-size: 12px;
  border: thin solid #a7abb0;
  cursor: default;
}
.gc-filter-dialog-style select {
  color: black;
  font-weight: normal;
  font-style: normal;
  background-color: rgb(255, 255, 255);
  border: 1px solid rgb(118, 118, 118);
  -webkit-appearance: auto;
  -moz-appearance: auto;
}
.gc-filter-dialog-style input {
  color: black;
  font-weight: normal;
  font-style: normal;
}

.gc-filter-dialog-style.gc-filter-dialog-resizable {
  resize: both;
  overflow: hidden;
}

.gc-filter-dialog-style #gc-search-tr {
  height: 62px;
}

#first-condition-expected > input,
#sec-condition-expected > input {
  background-color: #fff;
}

.gc-filter-disable-item i {
  color: #c4bec2;
}
.gc-filter-disable-item i:hover {
  color: #c4bec2;
}
.gc-filter-disable-item input {
  background-color: lightgray;
  opacity: 0.3;
}

.gc-search-outer-div {
  border: none;
  background-image: none;
  background-color: white;
  margin: 4px 0px 0px 4px;
  color: #1e395b;
  font-weight: normal;
}

div.gc-search-outer-div input::-ms-clear {
  display: none;
}

#gc-filterSearch {
  width: calc(100% - 15px);
  height: 21px;
  border: 1px solid #ababab;
  margin-left: 7px;
  margin-top: 4px;
  margin-bottom: 0px;
  padding: 0;
  font-size: 1em;
  background-color: white;
  color: black;
  float: none;
}
#gc-filterSearch:hover, #gc-filterSearch:active {
  background-color: white;
}

.gc-filter-button-active {
  border: 1px solid #569de5;
  border-radius: 0;
  background-image: -webkit-linear-gradient(top, #daecfc, #c4e0fc);
  /* For Chrome and Safari */
  background-image: -moz-linear-gradient(top, #daecfc, #c4e0fc);
  /* For old Fx (3.6 to 15) */
  background-image: -ms-linear-gradient(top, #daecfc, #c4e0fc);
  /* For pre-releases of IE 10*/
  background-image: -o-linear-gradient(top, #daecfc, #c4e0fc);
  /* For old Opera (11.1 to 12.0) */
  background-image: linear-gradient(to bottom, #daecfc, #c4e0fc);
  /* Standard syntax; must be last */
  background-color: #ffe475;
  font-weight: normal;
  color: black;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.gc-check-uncheck-all {
  float: left;
  width: 16px;
  height: 16px;
  display: inline-block;
}

.gc-filter-check-outerDiv .gc-check-image,
.gc-fill-type-item .gc-check-image {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjExR/NCNwAAAGxJREFUOE+ljsENgDAMAzsaQ3QMxP4/wAhXwTKhEY9TlZOdtK1b/4WVFaysYGUFKxMWdY/hA5T3+x0+BjJYJmOJBoF+87UMYhAwzFBaBnFwYZ1j/kKFltIycHLqMrHyhEvSMrCygpUVrJyntwPdKU02VXQw7gAAAABJRU5ErkJggg==);
}

.gc-filter-check-outerDiv .gc-uncheck-image,
.gc-fill-type-item .gc-uncheck-image {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjExR/NCNwAAAIJJREFUOE+lkssNgDAMQzsCw3UMxEocGKWDQSLVUj5GJeLwhPyI0x7a9qP/gsoKVFagskIUm3ALp3GKZvX63/q0QIcAlqAMXMcFIQ6z7DouTGLptawkMVmeDJi8BFsGQ0jzUcRyvEla4oLAhvVrveu4IOAdxJOwZPkOylBZgcrv9PYAV9tkcyJlS4sAAAAASUVORK5CYII=);
}

.gc-check-image,
.gc-uncheck-image {
  background-position: center;
}

.gc-filter-check-outerDiv {
  height: 18px;
  margin-top: 4px;
}

[gcuielement=gcSpread] i {
  font-style: normal;
}
[gcuielement=gcSpread] i.gc-filter-check-style {
  cursor: pointer;
  color: #1e395b;
  text-shadow: none;
}
[gcuielement=gcSpread] i.gc-filter-check {
  text-decoration: none;
}
[gcuielement=gcSpread] i.gc-filter-check:hover {
  text-decoration: underline;
}

.gc-filter-function-tr i:active {
  border-color: #e3e3e3;
  outline: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.gc-filter-sort {
  /*border: 1px solid transparent;*/
  font-weight: normal;
  color: #222222;
  white-space: nowrap;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
}

.gc-filter-item-link #gc-majorFilter {
  width: 181px;
  color: #222222;
}
.gc-filter-item-link #gc-majorFilter:hover {
  width: 181px;
  color: #222222;
}

.gc-filter-hover {
  border-radius: 0px;
  outline: none;
}

.gc-filter-item {
  position: relative;
  cursor: default;
  font-weight: normal;
  border-style: solid;
  border-color: transparent;
}

.gc-filter-item-container {
  border: 1px solid #a7abb0;
  border-radius: 3px;
  margin: 4px 0px 4px 4px;
  overflow: hidden;
}

.gc-filter-item-input {
  float: left;
  clear: left;
  margin: 3px !important;
}

.gc-filter-item-text {
  font-family: Lucida Grande, Lucida Sans, Arial, sans-serif;
  font-size: 12px;
  margin: 2px;
  white-space: nowrap;
  word-wrap: normal;
  float: left;
  clear: right;
}

.gc-filter-button-container {
  display: flex;
  justify-content: flex-end;
}

.gc-filter-button {
  width: 90px;
  height: 27px;
  margin: 2px 1px 5px;
}

.gc-filter-button-disable {
  opacity: 0.35;
  background-image: none;
}

#gc-filterOK {
  box-sizing: border-box;
  margin-left: 13px;
  margin-bottom: 5px;
  float: left;
}

#gc-filterCancel {
  box-sizing: border-box;
  margin-bottom: 5px;
  float: left;
}

.gc-smartMenu-item-default {
  border: 1px solid transparent;
  background-color: white;
  background-image: none;
  font-weight: normal;
  color: #1e395b;
  border-radius: 0;
}

.gc-filter-button-default {
  border: 1px solid #acacac;
  background-image: -webkit-linear-gradient(top, #f0f0f0, #e5e5e5);
  /* For Chrome and Safari */
  background-image: -moz-linear-gradient(top, #f0f0f0, #e5e5e5);
  /* For old Fx (3.6 to 15) */
  background-image: -ms-linear-gradient(top, #f0f0f0, #e5e5e5);
  /* For pre-releases of IE 10*/
  background-image: -o-linear-gradient(top, #f0f0f0, #e5e5e5);
  /* For old Opera (11.1 to 12.0) */
  background-image: linear-gradient(to bottom, #f0f0f0, #e5e5e5);
  font-weight: normal;
  color: black;
  border-radius: 0;
}

.gc-filter-button-hover {
  border: 1px solid #7eb4ea;
  background-image: -webkit-linear-gradient(top, #ecf4fc, #dcecfc);
  /* For Chrome and Safari */
  background-image: -moz-linear-gradient(top, #ecf4fc, #dcecfc);
  /* For old Fx (3.6 to 15) */
  background-image: -ms-linear-gradient(top, #ecf4fc, #dcecfc);
  /* For pre-releases of IE 10*/
  background-image: -o-linear-gradient(top, #ecf4fc, #dcecfc);
  /* For old Opera (11.1 to 12.0) */
  background-image: linear-gradient(to bottom, #ecf4fc, #dcecfc);
  /* Standard syntax; must be last */
  background-color: #d3f0e0;
  border-radius: 0;
  cursor: pointer;
  color: black;
  font-weight: normal;
  text-shadow: none;
}

.gc-smart-tag-default {
  border: 1px solid #ababab;
  background: white;
  color: #1e395b;
  font-weight: normal;
  border-radius: 0;
}

.gc-filter-item-hover, .gc-smart-tag-hover, .gc-smartMenu-item-hover {
  font-weight: normal;
  text-shadow: none;
  color: #1d5987;
}

.gc-smartMenu-item-hover {
  border: 1px solid #86bfa0;
  background-image: none;
  background-color: #d3f0e0;
}

.gc-smart-tag-hover {
  border: 1px solid #9fd5b7;
  background-image: none;
  background-color: white;
}

.gc-filter-item-hover {
  border: 1px solid transparent;
  background-image: none;
  background-color: #d3f0e0;
}

.gc-smart-tag-active {
  border: 1px solid #9fd5b7;
  background-image: none;
  background-color: #9fd5b7;
  font-weight: normal;
  color: #262626;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.gc-menu-item-input {
  width: 16px;
  height: 16px;
  margin: 1px;
  float: left;
  display: inline-block;
}

.gc-menu-item-text {
  font-size: 12px;
  font-weight: normal;
  display: inline-block;
  float: left;
  padding-top: 2px;
  font-family: Arial;
}

.gc-fill-menu-container {
  box-shadow: rgba(0, 0, 0, 0.4) 1px 2px 5px;
  cursor: default;
}

.gc-toolstrip-default {
  background: white;
  border: 1px solid #c6c6c6;
}

.gc-toolstrip-button-style {
  color: black;
  background: white;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.gc-toolstrip-button-style:active {
  color: black;
  background: white;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.gc-tab-tip-span {
  background: #d6e6f9;
  color: black;
  border: 1px solid #d6e6f9;
  font-weight: normal;
}

.gc-spread-toolTip {
  border: 1px solid #bebebe;
  border-radius: 0px;
  background-image: none;
  background-color: white;
  font-weight: normal;
  color: #217346;
  pointer-events: none;
}

.gc-spread-pivot-toolTip {
  border: 1px solid #bebebe;
  border-radius: 0px;
  background-color: white;
  font-weight: normal;
  color: black;
}

.gc-spread-template-sheet-toolTip {
  border: 1px solid #bebebe;
  border-radius: 0px;
  background-color: white;
  font-weight: normal;
  color: #212121;
  padding: 10px;
}

.gc-no-user-select {
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/*-----common css end-----*/
/*-----formula textbox start-----*/
/* function autocomplete */
.gcsj-func-ac-popup {
  margin: 0;
  padding: 0;
  background: #fff;
  border: 1px solid rgba(0, 0, 0, 0.2);
  font-family: arial, sans-serif;
  font-size: 12px;
  line-height: 22px;
  position: absolute;
  min-width: 300px;
  z-index: 2001;
  -webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.gcsj-func-ac-row {
  margin: 0;
  cursor: default;
  padding: 2px 10px;
  color: #666666;
}

.gcsj-func-ac-row-name {
  color: #222;
  font-size: 13px;
  font-family: inconsolata, monospace, arial, sans, sans-serif;
  margin: -2px 0;
}

.gcsj-func-ac-row-description {
  width: 280px;
  color: #666;
  display: none;
  font-size: 11px;
  margin: -2px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.gcsj-ac-row-active {
  background-color: #f5f5f5;
  color: #000;
  border-top: 1px solid #ebebeb;
  border-bottom: 1px solid #ebebeb;
  padding: 1px 10px;
}
.gcsj-ac-row-active .gcsj-func-ac-row-description {
  display: block;
}

/*  function help */
.gcsj-func-help-popup {
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.2);
  color: #222;
  font-size: 11px;
  word-wrap: break-word;
  position: absolute;
  width: 320px;
  z-index: 2001;
  -webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.gcsj-func-help-title {
  background-color: #f5f5f5;
  color: #222;
  font-size: 13px;
  padding: 1px 0 1px 10px;
}

.gcsj-func-help-body {
  border-top: 1px solid #ebebeb;
  font-family: arial, sans-serif;
  overflow: hidden;
}

.gcsj-func-help-content {
  padding-bottom: 2px;
}

.gcsj-func-help-section {
  padding: 5px 10px;
}

.gcsj-func-help-section-title {
  font-size: 11px;
  color: #666;
}

.gcsj-func-help-section-content {
  font-size: 11px;
}

.gcsj-func-help-formula {
  font-family: inconsolata, monospace, arial, sans, sans-serif;
  padding: 1px 0;
}

.gcsj-func-help-paramter {
  padding-left: 1px;
}

.gcsj-func-help-paramter-active {
  background-color: #feb;
}

.gcsj-func-gray-formula {
  color: gray;
}

/* color text */
.gcsj-func-color-content {
  white-space: pre-wrap;
}

/*-----formula textbox end-----*/
/*-----floatingobject start-----*/
.gc-floatingobject-selected {
  border: 1px solid #939393;
}

.gc-floatingobject-unselected {
  background-color: transparent;
  border: 1px solid transparent;
}

.gc-floatingobject-container {
  position: absolute;
  overflow: hidden;
  box-sizing: content-box;
}

.gc-floatingobject-background-cover {
  -webkit-background-size: cover;
  /* For WebKit*/
  -moz-background-size: cover;
  /* Mozilla*/
  -o-background-size: cover;
  /* Opera*/
  background-size: cover;
  /* Generic*/
}

.gc-floatingobject-moving-container {
  position: absolute;
  overflow: hidden;
}

.gc-floatingobject-moving-div {
  position: absolute;
  border: 1px solid black;
}

.gc-floatingobject-resize-indicator {
  box-sizing: content-box;
}

.gc-floatingobject-resize-indicator-select {
  background-color: white;
  border-radius: 2px;
  -moz-border-radius: 1px;
  border: 1px solid #939393;
  z-index: 100;
}

.gc-floatingobject-resize-indicator-unSelect {
  display: none;
}

.gc-floatingobject-absolute {
  position: absolute;
}

.gc-floatingobject-content-container {
  box-sizing: content-box;
}

/*-----floatingobject end-----*/
/*-----scrollbar start-----*/
/*scrollbar*/
/*scrollbar*/
.gc-scroll-container {
  background-color: #eaeaea;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.gc-scroll-corner-all {
  border-radius: 2px;
}

.gc-scroll-arrow {
  background-color: white;
  border-style: solid;
  border-color: #ababab;
  background-image: none;
  border-radius: 0;
}
.gc-scroll-arrow .gc-scroll-arrowUp {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjExR/NCNwAAAClJREFUKFNjGEmgvLz8P5RJHABpgGGoEH6ArIEojdg0wDBUyShAAAYGAHSXJkH1wN/VAAAAAElFTkSuQmCC);
  background-repeat: no-repeat;
}

.gc-scroll-arrowUp {
  background-position: center;
}

.gc-scroll-arrow .gc-scroll-arrowDown {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjExR/NCNwAAACRJREFUKFNjGAVYQHl5+X9cGKoEOyBZAwyQrAEGSNYwpAEDAwBvhSZBmzrLGgAAAABJRU5ErkJggg==);
  background-repeat: no-repeat;
}

.gc-scroll-arrowDown {
  background-position: center;
}

.gc-scroll-arrow .gc-scroll-arrowLeft {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjExR/NCNwAAADBJREFUKFNjGMKgvLz8P5RJHABpIEkTTAPRmpA1EK0JBMjSBAJkaQIBsjQNNGBgAABe7iZBxoz5vwAAAABJRU5ErkJggg==);
  background-repeat: no-repeat;
}

.gc-scroll-arrowLeft {
  background-position: center;
}

.gc-scroll-arrow .gc-scroll-arrowRight {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjExR/NCNwAAAC5JREFUKFNjGIKgvLz8P5RJPABpIlkjTBNJGpE1Ea2RZA0gQLIGECBZw2ACDAwAhS4mQZAuqGcAAAAASUVORK5CYII=);
  background-repeat: no-repeat;
}

.gc-scroll-arrowRight {
  background-position: center;
}

.gc-scroll-bar .gc-scrollbar-vertical {
  background-image: none;
  background-repeat: no-repeat;
}

.gc-scrollbar-vertical {
  background-position: center;
}

.gc-scroll-bar .gc-scrollbar-horizontal {
  text-indent: 0;
  background-image: none;
  background-repeat: no-repeat;
}

.gc-horizontal-scrollbar {
  background-color: #f6f6f6;
  border-top-color: #ababab;
}

.gc-vertical-scrollbar {
  background-color: #f6f6f6;
  border-left-color: #ababab;
}

.gc-scrollbar-horizontal {
  background-position: center;
}

.gc-scrollbar-wrapper {
  background-color: transparent;
}

.gc-scroll-bar {
  border-style: solid;
  border-color: #ababab;
  background: white;
  -moz-border-radius: 0px;
  -webkit-border-radius: 0px;
  border-radius: 0px;
}

.gc-scroll-arrow-hover {
  border-style: solid;
  border-color: #777777;
  background: white;
}

.gc-scrollbar-stateHover {
  border-style: solid;
  border-color: #ababab;
  background: #f0f0f0;
}

.gc-scroll-arrow:active,
.gc-scroll-bar:active,
.gc-scrollbar-stateActive {
  border-style: solid;
  border-color: #777777;
  background-color: #f0f0f0;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.gc-scroll-mobile-container {
  padding: 2px;
  opacity: 1;
  transition-duration: 0.3s;
  transition-property: width, height, background-color, transform, opacity;
  overflow: hidden;
  box-sizing: border-box;
}

.gc-scroll-mobile-state-hide {
  opacity: 0;
}

.gc-scroll-mobile-container-vertical {
  width: 10px;
}

.gc-scroll-mobile-container-horizontal {
  height: 10px;
}

.gc-scroll-mobile-state-hover.gc-scroll-mobile-container-vertical {
  width: 14px;
}

.gc-scroll-mobile-state-hover.gc-scroll-mobile-container-horizontal {
  height: 14px;
}

.gc-scroll-mobile-track {
  box-sizing: border-box;
  transition-duration: 0.3s;
  transition-property: background-color, transform, opacity;
  background-color: transparent; /* clear internal style */
  box-shadow: none; /* clear internal style */
  margin-right: 3px; /* the distance between hScrollbar and hDockScrollbar: padding * 1.5 */
}

.gc-scroll-mobile-container > .gc-scroll-mobile-track:nth-last-child(1) {
  margin-right: 0;
}

.gc-scroll-mobile-thumb {
  box-sizing: border-box;
  border-width: 1px;
  transition-duration: 0.3s;
  transition-property: background-color, transform, opacity;
}

.gc-scroll-mobile-thumb.ui-button {
  margin: 0; /* clear external themes style */
  padding: 0; /* clear external themes style */
}

/*-----scrollbar end-----*/
/*-----contextmenu start-----*/
.gc-ui-contextmenu-container {
  box-shadow: rgba(0, 0, 0, 0.4) 1px 2px 5px;
  font-family: "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
  font-size: 9pt;
  background: white;
  border: 1px solid #c6c6c6;
  color: #444444;
  cursor: default;
  min-width: 188px;
}

.gc-ui-contextmenu-separator {
  height: 1px;
  margin-left: 20px;
  margin-right: 5px;
  background-color: #e3e3e3;
  overflow: hidden;
}

.gc-ui-contextmenu-menuitem,
.gc-ui-contextmenu-nonselective-menuitem {
  border: 1px solid transparent;
  background-image: none;
  font-weight: normal;
  border-radius: 0;
  text-decoration: none;
  white-space: nowrap;
  overflow: hidden;
}

.gc-ui-contextmenu-menuitem-content {
  padding: 3px;
  margin: 1px;
  overflow: hidden;
  display: flex;
}

.gc-ui-contextmenu-icon {
  width: 16px;
  height: 16px;
  margin-left: 0;
  display: inline-block;
  margin-right: 12px;
}

.gc-ui-contextmenu-text {
  display: inline-block;
  padding-right: 23px;
  padding-left: 0;
  white-space: pre;
}

.gc-ui-contextmenu-disable {
  color: #b1b1b1 !important;
}

.gc-ui-contextmenu-group-header {
  border: 1px solid transparent;
  border-radius: 0;
  padding: 5px;
  font-weight: bold;
  background-color: #dddddd;
  overflow: hidden;
}

.gc-ui-contextmenu-groupitems-container {
  border: 1px solid transparent;
  background-image: none;
  font-weight: normal;
  color: #222222;
  border-radius: 0;
  padding: 5px;
  overflow: hidden;
  margin-left: 28px;
}

.gc-ui-contextmenu-groupitem {
  display: inline-block;
  float: left;
  min-width: 24px;
}
.gc-ui-contextmenu-groupitem .gc-ui-contextmenu-icon {
  width: 24px;
  height: 24px;
  margin: 0px;
}

.gc-ui-contextmenu-hover {
  background: #d3f0e0;
  background-image: none;
  font-weight: normal;
  text-shadow: none;
}

.gc-ui-contextmenu-menuitem-content .gc-ui-contextmenu-sup-indicator {
  width: 16px;
  height: 16px;
  display: inline-block;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAgCAYAAABU1PscAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTM4IDc5LjE1OTgyNCwgMjAxNi8wOS8xNC0wMTowOTowMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTcgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkFEODY1RjVGQzhGMjExRTdBMDNDRDU1NEFCMEVGRTIxIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkFEODY1RjYwQzhGMjExRTdBMDNDRDU1NEFCMEVGRTIxIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6QUQ4NjVGNURDOEYyMTFFN0EwM0NENTU0QUIwRUZFMjEiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6QUQ4NjVGNUVDOEYyMTFFN0EwM0NENTU0QUIwRUZFMjEiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz6xjVcxAAAAg0lEQVR42uzYMQ6AIAyF4RaFwXgT738PV+PMBVxRqx4A3RTM/yYGhn60hAQ1M6k5TioPAAAAAAAAAAAAAAAAAAAA8EdA33emqjLOccjt0ZJ/Jc7izftWUlqzdRYPeKq1mjtwdsLowBdpGnd70EUDQvCybbte6ykuvroR4iED8EIOAQYApagr3uYsgU4AAAAASUVORK5CYII=);
  background-position: -32px -16px;
  background-repeat: no-repeat;
  margin-left: auto;
}

.gc-ui-contextmenu-splitMenu-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.gc-ui-contextmenu-splitMenu-content {
  flex-grow: 1;
}

.gc-ui-contextmenu-splitMenu-icon {
  margin-right: 3px;
}

.gc-ui-contextmenu-split-sub-menu .gc-ui-contextmenu-groupitems-container {
  margin: 0;
}
.gc-ui-contextmenu-split-sub-menu .gc-ui-contextmenu-group-header .gc-ui-contextmenu-icon {
  width: 0;
  height: 0;
}

.gc-ui-contextmenu-subitems-container {
  position: absolute;
  left: 98%;
  display: none;
  margin-top: -3px;
}

.gc-ui-contextmenu-scroll-wrapper {
  overflow-y: auto;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.gc-ui-contextmenu-scroll-wrapper::-webkit-scrollbar {
  display: none;
}

.gc-ui-contextmenu-scroll-indicator {
  width: 100%;
  height: 12px;
  border-style: solid;
  box-sizing: border-box;
  border-width: 1px;
  border-color: #979593;
  background: white;
  text-align: center;
  position: absolute;
  color: #444444;
  font-family: "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
  font-size: 12px;
  line-height: 9px;
  cursor: default;
}

.gc-ui-contextmenu-menuitem-all-sheets-select-sheets-background-color {
  background: #dddddd;
}

.gc-spread-pivot-selected {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAO5JREFUOBFjYBgFRIfAjRs3eI8dO7bz+PHjRsiaWJA5uNhnzpxhffny5dr///+7MjExgfQ4w9QywRggev/+/Rx79+6NRBYDamJ8//79/J8/f7r+/v373q9fv6KQ5VEM+Pbt24rv378v27ZtWwFMEZDdCdQUDdT8+u/fv+4uLi4vYXIgmhGZs3HjRr9///6tBYoxA3EqEPMAXTCBkZHxKzMzs6Ofn99pZPUgNooBIIGVK1eCvLAEqJEB6F9GIP0XSPuFhoZuB8mjAwwDQAoWL16cAtQ4G6o4MS4ubgGUTTw1b968wrlz51YRr2PkqgQA82JlH8nI1QYAAAAASUVORK5CYII=);
  background-repeat: no-repeat;
}

.gc-spread-pivotRefresh {
  background-image: url(data:image/svg+xml;base64,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);
  background-repeat: no-repeat;
}

.gc-spread-removeField {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMTZwdCIgaGVpZ2h0PSIxNnB0IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSI+CjxnIGlkPSJzdXJmYWNlMSI+CjxwYXRoIHN0eWxlPSIgc3Ryb2tlOm5vbmU7ZmlsbC1ydWxlOm5vbnplcm87ZmlsbDpyZ2IoOTguMDM5MjE2JSw0NS44ODIzNTMlLDI1LjQ5MDE5NiUpO2ZpbGwtb3BhY2l0eToxOyIgZD0iTSAxMS44Mzk4NDQgNC45MjE4NzUgQyAxMi4wNTQ2ODggNC43MTA5MzggMTIuMDU0Njg4IDQuMzY3MTg4IDExLjgzOTg0NCA0LjE1NjI1IEMgMTEuNjMyODEyIDMuOTQ5MjE5IDExLjI4OTA2MiAzLjk0OTIxOSAxMS4wNzgxMjUgNC4xNTYyNSBMIDggNy4yMzgyODEgTCA0LjkyMTg3NSA0LjE1NjI1IEMgNC43MTA5MzggMy45NDkyMTkgNC4zNjcxODggMy45NDkyMTkgNC4xNTYyNSA0LjE1NjI1IEMgMy45NDkyMTkgNC4zNjcxODggMy45NDkyMTkgNC43MTA5MzggNC4xNTYyNSA0LjkyMTg3NSBMIDcuMjM4MjgxIDggTCA0LjE1NjI1IDExLjA3ODEyNSBDIDMuOTQ5MjE5IDExLjI4OTA2MiAzLjk0OTIxOSAxMS42MzI4MTIgNC4xNTYyNSAxMS44NDM3NSBDIDQuMzY3MTg4IDEyLjA1NDY4OCA0LjcxMDkzOCAxMi4wNTQ2ODggNC45MjE4NzUgMTEuODQzNzUgTCA4IDguNzYxNzE5IEwgMTEuMDc4MTI1IDExLjg0Mzc1IEMgMTEuMjg5MDYyIDEyLjA1NDY4OCAxMS42MzI4MTIgMTIuMDU0Njg4IDExLjgzOTg0NCAxMS44NDM3NSBDIDEyLjA1NDY4OCAxMS42MzI4MTIgMTIuMDU0Njg4IDExLjI4OTA2MiAxMS44Mzk4NDQgMTEuMDc4MTI1IEwgOC43NjE3MTkgOCBaIE0gMTEuODM5ODQ0IDQuOTIxODc1ICIvPgo8L2c+Cjwvc3ZnPgo=);
  background-repeat: no-repeat;
}

.gc-spread-pivotShowSubtotal {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAO5JREFUOBFjYBgFRIfAjRs3eI8dO7bz+PHjRsiaWJA5uNhnzpxhffny5dr///+7MjExgfQ4w9QywRggev/+/Rx79+6NRBYDamJ8//79/J8/f7r+/v373q9fv6KQ5VEM+Pbt24rv378v27ZtWwFMEZDdCdQUDdT8+u/fv+4uLi4vYXIgmhGZs3HjRr9///6tBYoxA3EqEPMAXTCBkZHxKzMzs6Ofn99pZPUgNooBIIGVK1eCvLAEqJEB6F9GIP0XSPuFhoZuB8mjAwwDQAoWL16cAtQ4G6o4MS4ubgGUTTw1b968wrlz51YRr2PkqgQA82JlH8nI1QYAAAAASUVORK5CYII=);
  background-repeat: no-repeat;
}

.gc-spread-pivotShowDetails {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMTZwdCIgaGVpZ2h0PSIxNnB0IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSI+CjxnIGlkPSJzdXJmYWNlMSI+CjxwYXRoIHN0eWxlPSIgc3Ryb2tlOm5vbmU7ZmlsbC1ydWxlOmV2ZW5vZGQ7ZmlsbDpyZ2IoNDAlLDQwJSw0MCUpO2ZpbGwtb3BhY2l0eToxOyIgZD0iTSAxMi42Njc5NjkgMTAuNjY3OTY5IEwgMTIuNjY3OTY5IDExLjMzMjAzMSBMIDEwIDExLjMzMjAzMSBMIDEwIDEwLjY2Nzk2OSBaIE0gMTIuNjY3OTY5IDguNjY3OTY5IEwgMTIuNjY3OTY5IDkuMzMyMDMxIEwgNy4zMzIwMzEgOS4zMzIwMzEgTCA3LjMzMjAzMSA4LjY2Nzk2OSBaIE0gMTIuNjY3OTY5IDYuNjY3OTY5IEwgMTIuNjY3OTY5IDcuMzMyMDMxIEwgMTAgNy4zMzIwMzEgTCAxMCA2LjY2Nzk2OSBaIE0gMTIuNjY3OTY5IDQuNjY3OTY5IEwgMTIuNjY3OTY5IDUuMzMyMDMxIEwgNy4zMzIwMzEgNS4zMzIwMzEgTCA3LjMzMjAzMSA0LjY2Nzk2OSBaIE0gMTIuNjY3OTY5IDQuNjY3OTY5ICIvPgo8cGF0aCBzdHlsZT0iIHN0cm9rZTpub25lO2ZpbGwtcnVsZTpldmVub2RkO2ZpbGw6cmdiKDI3LjA1ODgyNCUsNzIuOTQxMTc2JSw0My41Mjk0MTIlKTtmaWxsLW9wYWNpdHk6MTsiIGQ9Ik0gNS4zMzIwMzEgNS4zMzIwMzEgTCA1LjMzMjAzMSA2LjY2Nzk2OSBMIDYuNjY3OTY5IDYuNjY3OTY5IEwgNi42Njc5NjkgNy4zMzIwMzEgTCA1LjMzMjAzMSA3LjMzMjAzMSBMIDUuMzMyMDMxIDguNjY3OTY5IEwgNC42Njc5NjkgOC42Njc5NjkgTCA0LjY2Nzk2OSA3LjMzMjAzMSBMIDMuMzMyMDMxIDcuMzMyMDMxIEwgMy4zMzIwMzEgNi42Njc5NjkgTCA0LjY2Nzk2OSA2LjY2Nzk2OSBMIDQuNjY3OTY5IDUuMzMyMDMxIFogTSA1LjMzMjAzMSA1LjMzMjAzMSAiLz4KPC9nPgo8L3N2Zz4K);
  background-repeat: no-repeat;
}

.gc-spread-openValueFieldSettingDialogCmd {
  background-image: url(data:image/svg+xml;base64,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);
  background-repeat: no-repeat;
}

.gc-spread-pivotTableUnGroup {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMTVwdCIgaGVpZ2h0PSIxNXB0IiB2aWV3Qm94PSIwIDAgMTUgMTUiIHZlcnNpb249IjEuMSI+CjxnIGlkPSJzdXJmYWNlMSI+CjxwYXRoIHN0eWxlPSIgc3Ryb2tlOm5vbmU7ZmlsbC1ydWxlOmV2ZW5vZGQ7ZmlsbDpyZ2IoOTYuODYyNzQ1JSw5Ny42NDcwNTklLDk4LjQzMTM3MyUpO2ZpbGwtb3BhY2l0eToxOyIgZD0iTSA4Ljc1IDQuMzc1IEwgMTIuNSA0LjM3NSBMIDEyLjUgMTEuMjUgTCA4Ljc1IDExLjI1IFogTSA4Ljc1IDQuMzc1ICIvPgo8cGF0aCBzdHlsZT0iIHN0cm9rZTpub25lO2ZpbGwtcnVsZTpldmVub2RkO2ZpbGw6cmdiKDk2Ljg2Mjc0NSUsOTcuNjQ3MDU5JSw5OC40MzEzNzMlKTtmaWxsLW9wYWNpdHk6MTsiIGQ9Ik0gMy4xMjUgNi4yNSBMIDYuMjUgNi4yNSBMIDYuMjUgOS4zNzUgTCAzLjEyNSA5LjM3NSBaIE0gMy4xMjUgNi4yNSAiLz4KPHBhdGggc3R5bGU9IiBzdHJva2U6bm9uZTtmaWxsLXJ1bGU6bm9uemVybztmaWxsOnJnYig1MS4zNzI1NDklLDUxLjM3MjU0OSUsNTEuMzcyNTQ5JSk7ZmlsbC1vcGFjaXR5OjE7IiBkPSJNIDUgMTEuODc1IEwgNSAxMC42MjUgTCA1LjYyNSAxMC42MjUgTCA1LjYyNSAxMS4yNSBMIDcuNSAxMS4yNSBMIDcuNSAxMS44NzUgWiBNIDcuNSAzLjc1IEwgNy41IDQuMzc1IEwgNS42MjUgNC4zNzUgTCA1LjYyNSA1IEwgNSA1IEwgNSAzLjc1IFogTSA3LjUgMy43NSAiLz4KPHBhdGggc3R5bGU9IiBzdHJva2U6bm9uZTtmaWxsLXJ1bGU6bm9uemVybztmaWxsOnJnYig5OC4wMzkyMTYlLDQ1Ljg4MjM1MyUsMjUuNDkwMTk2JSk7ZmlsbC1vcGFjaXR5OjE7IiBkPSJNIDYuODc1IDUuNjI1IEwgNi44NzUgMTAgTCAyLjUgMTAgTCAyLjUgNS42MjUgWiBNIDYuMjUgNi42OTE0MDYgTCAzLjU2NjQwNiA5LjM3NSBMIDYuMjUgOS4zNzUgWiBNIDUuODA4NTk0IDYuMjUgTCAzLjEyNSA2LjI1IEwgMy4xMjUgOC45MzM1OTQgWiBNIDUuODA4NTk0IDYuMjUgIi8+CjxwYXRoIHN0eWxlPSIgc3Ryb2tlOm5vbmU7ZmlsbC1ydWxlOm5vbnplcm87ZmlsbDpyZ2IoNDAlLDQwJSw0MCUpO2ZpbGwtb3BhY2l0eToxOyIgZD0iTSAxMi41IDExLjg3NSBMIDguMTI1IDExLjg3NSBMIDguMTI1IDMuNzUgTCAxMi41IDMuNzUgWiBNIDExLjg3NSA5LjM3NSBMIDguNzUgOS4zNzUgTCA4Ljc1IDExLjI1IEwgMTEuODc1IDExLjI1IFogTSAxMS44NzUgNi44NzUgTCA4Ljc1IDYuODc1IEwgOC43NSA4Ljc1IEwgMTEuODc1IDguNzUgWiBNIDExLjg3NSA0LjM3NSBMIDguNzUgNC4zNzUgTCA4Ljc1IDYuMjUgTCAxMS44NzUgNi4yNSBaIE0gMTEuODc1IDQuMzc1ICIvPgo8L2c+Cjwvc3ZnPgo=);
  background-repeat: no-repeat;
}

.gc-spread-pivotTableGroup {
  background-image: url(data:image/svg+xml;base64,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);
  background-repeat: no-repeat;
}

.gc-spread-pivotExpand {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMTZwdCIgaGVpZ2h0PSIxNnB0IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSI+CjxnIGlkPSJzdXJmYWNlMSI+CjxwYXRoIHN0eWxlPSIgc3Ryb2tlOm5vbmU7ZmlsbC1ydWxlOmV2ZW5vZGQ7ZmlsbDpyZ2IoNDAlLDQwJSw0MCUpO2ZpbGwtb3BhY2l0eToxOyIgZD0iTSAxMi42Njc5NjkgMTAuNjY3OTY5IEwgMTIuNjY3OTY5IDExLjMzMjAzMSBMIDEwIDExLjMzMjAzMSBMIDEwIDEwLjY2Nzk2OSBaIE0gMTIuNjY3OTY5IDguNjY3OTY5IEwgMTIuNjY3OTY5IDkuMzMyMDMxIEwgNy4zMzIwMzEgOS4zMzIwMzEgTCA3LjMzMjAzMSA4LjY2Nzk2OSBaIE0gMTIuNjY3OTY5IDYuNjY3OTY5IEwgMTIuNjY3OTY5IDcuMzMyMDMxIEwgMTAgNy4zMzIwMzEgTCAxMCA2LjY2Nzk2OSBaIE0gMTIuNjY3OTY5IDQuNjY3OTY5IEwgMTIuNjY3OTY5IDUuMzMyMDMxIEwgNy4zMzIwMzEgNS4zMzIwMzEgTCA3LjMzMjAzMSA0LjY2Nzk2OSBaIE0gMTIuNjY3OTY5IDQuNjY3OTY5ICIvPgo8cGF0aCBzdHlsZT0iIHN0cm9rZTpub25lO2ZpbGwtcnVsZTpldmVub2RkO2ZpbGw6cmdiKDI3LjA1ODgyNCUsNzIuOTQxMTc2JSw0My41Mjk0MTIlKTtmaWxsLW9wYWNpdHk6MTsiIGQ9Ik0gNS4zMzIwMzEgNS4zMzIwMzEgTCA1LjMzMjAzMSA2LjY2Nzk2OSBMIDYuNjY3OTY5IDYuNjY3OTY5IEwgNi42Njc5NjkgNy4zMzIwMzEgTCA1LjMzMjAzMSA3LjMzMjAzMSBMIDUuMzMyMDMxIDguNjY3OTY5IEwgNC42Njc5NjkgOC42Njc5NjkgTCA0LjY2Nzk2OSA3LjMzMjAzMSBMIDMuMzMyMDMxIDcuMzMyMDMxIEwgMy4zMzIwMzEgNi42Njc5NjkgTCA0LjY2Nzk2OSA2LjY2Nzk2OSBMIDQuNjY3OTY5IDUuMzMyMDMxIFogTSA1LjMzMjAzMSA1LjMzMjAzMSAiLz4KPC9nPgo8L3N2Zz4K);
  background-repeat: no-repeat;
}

.gc-spread-pivotCollapse {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMTZwdCIgaGVpZ2h0PSIxNnB0IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSI+CjxnIGlkPSJzdXJmYWNlMSI+CjxwYXRoIHN0eWxlPSIgc3Ryb2tlOm5vbmU7ZmlsbC1ydWxlOmV2ZW5vZGQ7ZmlsbDpyZ2IoNDAlLDQwJSw0MCUpO2ZpbGwtb3BhY2l0eToxOyIgZD0iTSAxMi42Njc5NjkgMTAuNjY3OTY5IEwgMTIuNjY3OTY5IDExLjMzMjAzMSBMIDEwIDExLjMzMjAzMSBMIDEwIDEwLjY2Nzk2OSBaIE0gMTIuNjY3OTY5IDguNjY3OTY5IEwgMTIuNjY3OTY5IDkuMzMyMDMxIEwgNy4zMzIwMzEgOS4zMzIwMzEgTCA3LjMzMjAzMSA4LjY2Nzk2OSBaIE0gMTIuNjY3OTY5IDYuNjY3OTY5IEwgMTIuNjY3OTY5IDcuMzMyMDMxIEwgMTAgNy4zMzIwMzEgTCAxMCA2LjY2Nzk2OSBaIE0gMTIuNjY3OTY5IDQuNjY3OTY5IEwgMTIuNjY3OTY5IDUuMzMyMDMxIEwgNy4zMzIwMzEgNS4zMzIwMzEgTCA3LjMzMjAzMSA0LjY2Nzk2OSBaIE0gMTIuNjY3OTY5IDQuNjY3OTY5ICIvPgo8cGF0aCBzdHlsZT0iIHN0cm9rZTpub25lO2ZpbGwtcnVsZTpldmVub2RkO2ZpbGw6cmdiKDk4LjAzOTIxNiUsNDUuODgyMzUzJSwyNS40OTAxOTYlKTtmaWxsLW9wYWNpdHk6MTsiIGQ9Ik0gMy4zMzIwMzEgNi42Njc5NjkgTCA2IDYuNjY3OTY5IEwgNiA3LjMzMjAzMSBMIDMuMzMyMDMxIDcuMzMyMDMxIFogTSAzLjMzMjAzMSA2LjY2Nzk2OSAiLz4KPC9nPgo8L3N2Zz4K);
  background-repeat: no-repeat;
}

.gc-spread-pivotExpandEntireField {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMTZwdCIgaGVpZ2h0PSIxNnB0IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSI+CjxnIGlkPSJzdXJmYWNlMSI+CjxwYXRoIHN0eWxlPSIgc3Ryb2tlOm5vbmU7ZmlsbC1ydWxlOmV2ZW5vZGQ7ZmlsbDpyZ2IoNDAlLDQwJSw0MCUpO2ZpbGwtb3BhY2l0eToxOyIgZD0iTSAxMi42Njc5NjkgMTAuNjY3OTY5IEwgMTIuNjY3OTY5IDExLjMzMjAzMSBMIDEwIDExLjMzMjAzMSBMIDEwIDEwLjY2Nzk2OSBaIE0gMTIuNjY3OTY5IDguNjY3OTY5IEwgMTIuNjY3OTY5IDkuMzMyMDMxIEwgNy4zMzIwMzEgOS4zMzIwMzEgTCA3LjMzMjAzMSA4LjY2Nzk2OSBaIE0gMTIuNjY3OTY5IDYuNjY3OTY5IEwgMTIuNjY3OTY5IDcuMzMyMDMxIEwgMTAgNy4zMzIwMzEgTCAxMCA2LjY2Nzk2OSBaIE0gMTIuNjY3OTY5IDQuNjY3OTY5IEwgMTIuNjY3OTY5IDUuMzMyMDMxIEwgNy4zMzIwMzEgNS4zMzIwMzEgTCA3LjMzMjAzMSA0LjY2Nzk2OSBaIE0gMTIuNjY3OTY5IDQuNjY3OTY5ICIvPgo8cGF0aCBzdHlsZT0iIHN0cm9rZTpub25lO2ZpbGwtcnVsZTpldmVub2RkO2ZpbGw6cmdiKDI3LjA1ODgyNCUsNzIuOTQxMTc2JSw0My41Mjk0MTIlKTtmaWxsLW9wYWNpdHk6MTsiIGQ9Ik0gNS4zMzIwMzEgNS4zMzIwMzEgTCA1LjMzMjAzMSA2LjY2Nzk2OSBMIDYuNjY3OTY5IDYuNjY3OTY5IEwgNi42Njc5NjkgNy4zMzIwMzEgTCA1LjMzMjAzMSA3LjMzMjAzMSBMIDUuMzMyMDMxIDguNjY3OTY5IEwgNC42Njc5NjkgOC42Njc5NjkgTCA0LjY2Nzk2OSA3LjMzMjAzMSBMIDMuMzMyMDMxIDcuMzMyMDMxIEwgMy4zMzIwMzEgNi42Njc5NjkgTCA0LjY2Nzk2OSA2LjY2Nzk2OSBMIDQuNjY3OTY5IDUuMzMyMDMxIFogTSA1LjMzMjAzMSA1LjMzMjAzMSAiLz4KPC9nPgo8L3N2Zz4K);
  background-repeat: no-repeat;
}

.gc-spread-pivotCollapseEntireField {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMTZwdCIgaGVpZ2h0PSIxNnB0IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSI+CjxnIGlkPSJzdXJmYWNlMSI+CjxwYXRoIHN0eWxlPSIgc3Ryb2tlOm5vbmU7ZmlsbC1ydWxlOmV2ZW5vZGQ7ZmlsbDpyZ2IoNDAlLDQwJSw0MCUpO2ZpbGwtb3BhY2l0eToxOyIgZD0iTSAxMi42Njc5NjkgMTAuNjY3OTY5IEwgMTIuNjY3OTY5IDExLjMzMjAzMSBMIDEwIDExLjMzMjAzMSBMIDEwIDEwLjY2Nzk2OSBaIE0gMTIuNjY3OTY5IDguNjY3OTY5IEwgMTIuNjY3OTY5IDkuMzMyMDMxIEwgNy4zMzIwMzEgOS4zMzIwMzEgTCA3LjMzMjAzMSA4LjY2Nzk2OSBaIE0gMTIuNjY3OTY5IDYuNjY3OTY5IEwgMTIuNjY3OTY5IDcuMzMyMDMxIEwgMTAgNy4zMzIwMzEgTCAxMCA2LjY2Nzk2OSBaIE0gMTIuNjY3OTY5IDQuNjY3OTY5IEwgMTIuNjY3OTY5IDUuMzMyMDMxIEwgNy4zMzIwMzEgNS4zMzIwMzEgTCA3LjMzMjAzMSA0LjY2Nzk2OSBaIE0gMTIuNjY3OTY5IDQuNjY3OTY5ICIvPgo8cGF0aCBzdHlsZT0iIHN0cm9rZTpub25lO2ZpbGwtcnVsZTpldmVub2RkO2ZpbGw6cmdiKDk4LjAzOTIxNiUsNDUuODgyMzUzJSwyNS40OTAxOTYlKTtmaWxsLW9wYWNpdHk6MTsiIGQ9Ik0gMy4zMzIwMzEgNi42Njc5NjkgTCA2IDYuNjY3OTY5IEwgNiA3LjMzMjAzMSBMIDMuMzMyMDMxIDcuMzMyMDMxIFogTSAzLjMzMjAzMSA2LjY2Nzk2OSAiLz4KPC9nPgo8L3N2Zz4K);
  background-repeat: no-repeat;
}

.gc-spread-pivotClearFilterFrom {
  background-image: url(data:image/svg+xml;base64,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);
  background-repeat: no-repeat;
}

.gc-spread-copy {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAOCAYAAADwikbvAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTM4IDc5LjE1OTgyNCwgMjAxNi8wOS8xNC0wMTowOTowMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTcgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkMwNEYxQjlFNDJCMzExRTc4OTZBRDU3QzgwMEM3QTQ3IiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkMwNEYxQjlGNDJCMzExRTc4OTZBRDU3QzgwMEM3QTQ3Ij4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6QzA0RjFCOUM0MkIzMTFFNzg5NkFENTdDODAwQzdBNDciIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6QzA0RjFCOUQ0MkIzMTFFNzg5NkFENTdDODAwQzdBNDciLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz6TXfPoAAABdUlEQVR42oSSPUsDQRCGJ+EI2CWCiKKYSrtgYyOCYCUBSSVYCBZiKhXxfyiCjYgSG0FIoSiIhUVSaJ2fYGNnYUhyH7s7u85s7i7mPMnAMLu389777EfmofFhtNaAiKAU2sq5UyllYFSwOBkXdw1Tu2+ZUVpHa4wnmtqzoV9hqgj1t28jBIAvAbhKaj2s5GMiB1HZAZISNUDOyYCUPRDUubkyPuR0/tiG5eqTeb/csD/IIkq7IJWxybG/XQYvGCbiaLsSnk/XYXGrZr84SoViNKAo3Tb2qy//EH11BBxdfUIhX4CF8okhsbBNQvbFNknQ9QZEkfhsbzpkmYPVg1fecyhWeiAm4q6bTsRrsxM5QDKNsYPfzpQdL52IK4cKfHaWYRM5Ex4vImXknCSKxDJwB1c1PzM2dC3RnpNEGB699HvgXN/WgTMZxaVqKlEsZufWy03qG54s7Zo0oijsnv97t/zC1o6bgFKQi2cxhde1led82j8CDAAflFKLtUvyvAAAAABJRU5ErkJggg==);
  background-repeat: no-repeat;
}

.gc-spread-cut {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAAQCAYAAADNo/U5AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTM4IDc5LjE1OTgyNCwgMjAxNi8wOS8xNC0wMTowOTowMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTcgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjk4M0YzQkU2NDJCMzExRTdBQzFCQzM2MzQ3Q0NEMUVDIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjk4M0YzQkU3NDJCMzExRTdBQzFCQzM2MzQ3Q0NEMUVDIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6OTgzRjNCRTQ0MkIzMTFFN0FDMUJDMzYzNDdDQ0QxRUMiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6OTgzRjNCRTU0MkIzMTFFN0FDMUJDMzYzNDdDQ0QxRUMiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz6vHhfyAAABpklEQVR42mJgwAI6lxz427xgrzIDDsCETZBPgI/p8cNHx3BqKp24fkHZhPVlyILMjEwM//8z8uDU9PLJEyEGRsZOdI3////jxKlpUXeu3+MHD75hamRkxOsneVUVmUf3H/zCZiNOTR1ZPu9lFOW0Ht6//x+kkShNINCTH3RXRl7R8uH9Bwzv375l4ONmY4iqmCaITROGu0HO+/Lxdef/f/8Y/nDpHj15g62YT1zsFrsgpzUHL9vRrVkC7zE0KbrN8nFx0NjMJST048DBd2z/2TgZOAR4Gdj4eX6x8rGzMf3978+CafX/tHN3/zL8v/9f5x8j47u/v/7c+/XlmwDbr19SvxmFrBn+/l8Nt0nVdYb5f2aOHazcwgJcgmIMZ+eYg+V0ow4L/v3Hco+JmWnOlaXmpcZZl//DA+I/I8sxVl65w8ysQtd+//wHt/nyMtv3zEx/lP78/BWhFXHs/8/3XxCSqt5L/sPY2hFH/mMLNVW/feYqAXuV4X769/vLD42g7d2MbNxrcMXP7U1OJ1Hiiek/g8Pf76+LGP58P/H/P8NmfJELEGAAG6Sldb/nz3cAAAAASUVORK5CYII=);
  background-repeat: no-repeat;
}

.gc-spread-pasteOptions {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTM4IDc5LjE1OTgyNCwgMjAxNi8wOS8xNC0wMTowOTowMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTcgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkRDMUFEQ0I5NDJCMzExRTdBMjQ1QkFFQURDMUU0MzZBIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkRDMUFEQ0JBNDJCMzExRTdBMjQ1QkFFQURDMUU0MzZBIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6REMxQURDQjc0MkIzMTFFN0EyNDVCQUVBREMxRTQzNkEiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6REMxQURDQjg0MkIzMTFFN0EyNDVCQUVBREMxRTQzNkEiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz6s+LeRAAACA0lEQVR42nxTz0tUURT+rmgTjiJEEhlEiBEEgS1yIoJqIUMi6Eb/AFfSTvqxi1rVpsSFC4kYaF2IUhS4mmhTK0MxHISiFLFFOoPz4737zr23c+97z5lxRs/jvnvuved95/vOeVfgkD19MWfq1g8mBY4xUVocNqaw4Rb3Vydw9/YtjAyl3Hrx4zd8yn7GyyuZMLjrIpIjH+oAW+zHycHnSF7NuY3r11L4swU3rG/NntmYOFGttbq3/OumtfVN3Ein6wLO9vShNqY5gCm46cuz2SYhS1Hk3jEAeg9aOZX8GNgqvvfyULwXaICICeyw330PTzL/zMpyFrmdzsSPt+MyYpCHJq6NiBpgBPxAYfzmqYaMMwsS58/1oOxt+Rib7WyJJSjOogIRDvY9Sc3bJjSmH6aR6u/DmY4T+5GEPNO09GMznKERwPJ7/WYerzLEkvlcaxxIUNKis2vCWhQrgTsKSMe5oTTh+7tJ7BaK+PV7E48y2zhgoAMRZTEhQCkE8KWOasMMuTZECu2JkxjovwySP6sMiOI8wkHsl6Vbe1LVCKiKTLYzO78SAXCPT/e2VcW2SRRzIYOyT64rImIXe0CCAcpotf936WtjteMaVCoUZrYyHAkRzrY+HgMcvhyxjT4mF3bpQteRN9FKOPKq3pnKOgBFAXfIc8GWss1K7BPvWWb/BRgAxiv0G+KDFYkAAAAASUVORK5CYII=);
  background-repeat: no-repeat;
}

.gc-spread-pasteAll {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAXCAYAAADk3wSdAAAAuUlEQVR42u3VsQrDIBAGYF/ZSTdfoI/j4pBdFPe+QrsUuoWCV064ogkmplpooQc/ITH3JSTGMFYp5xwIIYBzniKlhBACsJ4iLMaYQvvNwPX+SCcbY17NNZSitYa8t6jzZYbTdIP87lqDPdiLRoHiwV6U+lfoaqChqr35QE++A2U9z2UL3Xtp3nt4C6U5uwyOKaUAv8KhKG4RttbCMLSYvyPQ5QX+6K+gtJ4ORXHl3oMPo0f+WR9BW/ME6gakYAjbY+4AAAAASUVORK5CYII=);
  background-repeat: no-repeat;
}

.gc-spread-pasteFormula {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAXCAYAAADk3wSdAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTM4IDc5LjE1OTgyNCwgMjAxNi8wOS8xNC0wMTowOTowMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTcgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkUzMjQ5N0Y2N0U0QzExRTdCOTEyQ0NDNzNENjU1NjFDIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkUzMjQ5N0Y3N0U0QzExRTdCOTEyQ0NDNzNENjU1NjFDIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6RTMyNDk3RjQ3RTRDMTFFN0I5MTJDQ0M3M0Q2NTU2MUMiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6RTMyNDk3RjU3RTRDMTFFN0I5MTJDQ0M3M0Q2NTU2MUMiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz6KOVrPAAABtklEQVR42qxVPY8BURQ9IxKFZisSSjUKFYlmhdhEQqPWCVExWws6kdH5B0JDojMU2xASnVbiB1Bto9G83Xs3M7GyY+fDSSbvK/fcOee9d58EA+x2OzGdTnG9Xnns8XhQLBYRi8Uk/AO30cJ4POZWURRuZVnGcDiEGbiFENyRJAmLxUKoqvowoF6vc0AqlUIul5Nu4zXwpPLxKeTXFzQaDVhBv9+nWHzHSrekLiKEQ9xzuG8WOLNFMmsbZQZkmYb3Z5F2u11st1uk0+lf8y67hJPJBKfTCZVKhdunkC6XSyQSCWSzWXQ6HeekdJbP5zNCodBzNqpQKOj9drvNftZqNWeks9kMg8GAfbyX7Uj+fr+H3+83XLdF+shPW/K1ghMMBnV/e70eDoeDaLVanMwy6fF4hM/nQzgcZn+bzSY2mw3Pj0YjyZZ88jMajerjSCTCZ7Zarb7Z9vTeT0pwuVwQCATUPz01qjpa4Viv19zSLdJA0r1eL1arlUgmkz/yqcBakR6Px/WxloQsIE9ps3T5j4jpL8vlMhMQaSaT0W8WeVkqldgC6tNnGvQu5fN5MZ/PTb0SXwIMAAHPvw+5QntsAAAAAElFTkSuQmCC);
  background-repeat: no-repeat;
}

.gc-spread-pasteValues {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAXCAYAAADk3wSdAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTM4IDc5LjE1OTgyNCwgMjAxNi8wOS8xNC0wMTowOTowMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTcgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkFCQzg0RDc3N0U0QzExRTc4MzcyRDdEMjUyQjJDODFCIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkFCQzg0RDc4N0U0QzExRTc4MzcyRDdEMjUyQjJDODFCIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6QUJDODRENzU3RTRDMTFFNzgzNzJEN0QyNTJCMkM4MUIiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6QUJDODRENzY3RTRDMTFFNzgzNzJEN0QyNTJCMkM4MUIiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz7cAGVnAAABf0lEQVR42mL8//9/DgN2MKWiouL/r1+/wBx2dnaG9vZ2RiATl3o4YMIlUVRUBDawt7cXjH/+/AkWQ7cYCcMBC0zg379/rUxMTNVYNKJbNhlE9/X1MeJSwwj0PkPvvg//i50EQBoYSAFAg0F6GYB6UYKFCWQgA4UA3QwWJAmwzSQaRlpEUQJGDR01dCgYykKJZlB5QbSh169fZ9i0aRPDmTNnGFavXg0XT0pKYrh16xaYfeTIETBdU1PDcODAARBzsrGxMcPEiRNzsRoKMuzmzZsMz58/h4tt2LCBwdHRkWHevHlgvo2NDdjglpYWMP/UqVOwUm4KVkNjY2PBGKQRBgICAlDU8PDwoPA/f/6M3fvIpQ6u8AKB0NBQhujoaHQXMvj6+kJiH1rAEgWePHkCNtDPzw/sExAwMzMDB8Ps2bMZ9u/fj0hS6AbjcmVhYSGKgciAl5cXpTrBqB0XL148eebMmXC+pKQkOBUghzEsBXh4eDB8+fIFzIfGPiNAgAEAG66hf3H3yXAAAAAASUVORK5CYII=);
  background-repeat: no-repeat;
}

.gc-spread-pasteFormatting {
  background-image: url(data:image/png;base64,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);
  background-repeat: no-repeat;
}

.gc-spread-pasteValuesFormatting {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAzNJREFUSA2llU1oE1EQx3fTxASxqRaLCFEsCoqgN7XixX6AaBHsSVsPUpUiVYr58iBYUrTUmqTBIgiiIIKCl4JQKwj98OAXikXtURGkaK2oNWikaZP1N9tsWJtkE/DB7MybmfefefPmvVWVIsPn893XNG2f4aaq6lBfX1+jMf9v7vV6NTPI4rnZlk+251P6/f696XT6GjZPPnsmyKTNZmuLRqMP8vkYumyA/uGZ9UlFuRCoX968GLxA1p6M35royMwdh6ac66hf/t4ANrhNBAGfU5WniqIdyhjyZm4sMnHdjzNqlvWCY7Lpor4DjN04VRnGWCymGnKpXNbPqWo3/kaS+lJ9B0h1pQIV8cvB0Xdgzt4AoO7HkEO05YzT6azr6en5iq6T+UratMPwM/N8ONlDNjtm5F673b4jlUr5k8lkE/fhOQDtdM7WPL4FVUaJchzI9CNdsgVDNZSGbgAejEQi0znOFoqCAQDzEeA4a9+Q+SpoGt2TYDC4C7nkJihYIjIdA3yMum9gN4+h9vn5+RF0r7mIv+AtUNFRcAeyUjJlXId3MZVAAy6XqxU5+zaJn9WwDECmbYDb6JqrgEv2+2dnZ3vhz6xAzbaCJRKnsrKyDwC3QPLgvSDgUQKudbvdA2YQK1k/rMjwj+yLGahfUfIBGsBW6y13YAAU41ZJFQ1AWc5Tlm2QkzeqVoIFAoEGWvgyunWU7yWlPBgOh6fk54R5J/oJdJ3SiZaHLGA4e6BliJUyl8HcDYCXe7GH6XaCndQNinITXkNQN7qzotMDqKryWSYyYiM///kd0kGtLHi1YF348pMZIOOH5eXl0k1/CPhJLAR8hG81oiQzquvkoyjqmM74pLT0oBya+eAM22Iej8evCDhddVtsvFtHyPwe4m9oSHT6DhzqkjOqon4XRSkDUJV69+PbQNaNoVAoLus4o3BFRYUbcRyfu6LLtmT/aMIzpyUvUeHdmqasFqN0B4d8gKza2PpmVH6yHSbzLuQTUCf0Bdtbh8PxjUtYQ8BJ/C+iq6K8G7Nd1FG7dBLnnPeFTE6jd8Gn4MFEIjEBr2Q+Dm+C5NBvQYOAHoZvgr+jCU6J7S9Up17d6QSfRQAAAABJRU5ErkJggg==);
  background-repeat: no-repeat;
}

.gc-spread-pasteFormulaFormatting {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAA2JJREFUSA2llV1IVEEUx++srhvhRxZRgZUSPSSIPfUmlIJ9CD0UUYZvURDU0uoaIhWCiYK7GotG+hQIBRsFhR9EKRKC+JAvPVYYYVQvm5q0m+vu7XfuOnbTva7QwNw5c85//mfOzDlzlZGh1dfXD5mmeUrDlFLDXV1dNXr+36PP5zPtJGvndls6OTudsqGh4WQymezHVpTOvuJk1uVyXQkGgyPpMFq36iA0OndgyTDu+qu21a4ld9h10Qpub3Bs7pHbNG57q7Z91MR6dIkg5HFlTBqGeWHFkHbnepFttHDcUa2sFx6bzRKtCDC2Adqpjd3d3UrLmx1lfVypNvB6k9ZSKwKkys0SZcCt47EisO9eCPx+f00ikXiA6MnKyroWCATCLS0trvn5+RHS9CVp2pXO0Voewaxesn0B5D1kyC103GOygzG8sLDgZSzJz8+/b8dmktM6YJc7IP6EkxwIsomoBKetzGuIJJaJ1G5P6wCA7PIJIctlt0Lej9MBcv6NffFmZCtbAqM/VqvVX1Vo6RobGw9CGl1eXq6GqAW5Doet9Fw5vkwFpp07RWB0dna+x8lugAF6HUcWxMkg8iJO+hj30a0GroIorzPZT1fFxcUVXq/3txh1moq8rrH7XpTyuA1DXow8QVZN4KAA2Wo8hqfBvULfgf4M/a0mF4BjBCw8C7jC4/EcSlEZT5n3EkkCZw9Fx1zxboUQp4l4WnS0q6kh9XW8A96fMiBRqvqDQIWMo6hiXJLLbmpqKozH4zdweAeHL+jP8vLyBsiyZIo69XWMAOJ3diAEkgivtQ7yLJzpt6cP+ctacsE6RqCJNho5xmmIt7CZUifchpfstEj0zc3NuyA/LMezEc46IqWMr6Zp7BFg99h8ja+yYGijRWKLxWLHGRQ18VxjQ6GQJxKJeJgvLS4ulvOGTa3cgRrnGmsFmDCTgxSeiP80XYA25Ql2/y03N3dKdGRT6czMjFT8LNMo/Sd9yjoit8q5qQwVQbGpFg6H5YKrIevXF0sGfaYWQujPQxKiduRxTBWa99jWWbcrp5wFj+W4nLyQukfk7CcnJ8+B/e52u+/ZsDHItzP/xbEVEJFU9d9CEyfML4rSqbHQF41GyyAfpwCPtre3W2dJFPn8KwaJoAdbmNq4zLN+SXj+AJhsfWw7R2FgAAAAAElFTkSuQmCC);
  background-repeat: no-repeat;
}

.gc-spread-pasteFormulaAndNumberFormat {
  background-image: url(data:image/svg+xml;base64,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);
  background-repeat: no-repeat;
}

.gc-spread-pasteKeepSourceFormat {
  background-image: url(data:image/svg+xml;base64,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);
  background-repeat: no-repeat;
}

.gc-spread-pasteNoBorders {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMjRweCIgaGVpZ2h0PSIyNHB4IiB2aWV3Qm94PSIwIDAgMjQgMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+UGFzdGUgTm8gQm9yZGVyc+Wkh+S7vTwvdGl0bGU+CiAgICA8ZyBpZD0iUGFzdGUtTm8tQm9yZGVyc+Wkh+S7vSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPHBhdGggZD0iTTYsMiBMNiw0IEM2LDUuMTA0NTY5NSA2Ljg5NTQzMDUsNiA4LDYgTDE2LDYgQzE3LjEwNDU2OTUsNiAxOCw1LjEwNDU2OTUgMTgsNCBMMTgsMiBMMjAsMiBDMjAuNTUyMjg0NywyIDIxLDIuNDQ3NzE1MjUgMjEsMyBMMjEsMTEgTDExLDExIEwxMSwyMSBMNCwyMSBDMy40NDc3MTUyNSwyMSAzLDIwLjU1MjI4NDcgMywyMCBMMywzIEMzLDIuNDQ3NzE1MjUgMy40NDc3MTUyNSwyIDQsMiBMNiwyIFoiIGlkPSJQYXRoIiBmaWxsPSIjNzVCRUY0IiBmaWxsLXJ1bGU9Im5vbnplcm8iPjwvcGF0aD4KICAgICAgICA8cG9seWdvbiBpZD0iQ29tYmluZWQtU2hhcGUiIGZpbGw9IiNGRkZGRkYiIGZpbGwtcnVsZT0ibm9uemVybyIgcG9pbnRzPSIxOCAxMSAxMSAxMSAxMSAxOCA2IDE4IDYgNCAxOCA0Ij48L3BvbHlnb24+CiAgICAgICAgPHBhdGggZD0iTTEwLDIgTDEwLDAuODMzMzMzMzMzIEMxMCwwLjM3MzA5NjA0MiAxMC4yOTg0NzY4LDAgMTAuNjY2NjY2NywwIEwxMy4zMzMzMzMzLDAgQzEzLjcwMTUyMzIsMCAxNCwwLjM3MzA5NjA0MiAxNCwwLjgzMzMzMzMzMyBMMTQsMiBMMTYuMzMzMzMzMywyIEMxNi43MDE1MjMyLDIgMTcsMi4zNzMwOTYwNCAxNywyLjgzMzMzMzMzIEwxNyw0LjE2NjY2NjY3IEMxNyw0LjYyNjkwMzk2IDE2LjcwMTUyMzIsNSAxNi4zMzMzMzMzLDUgTDcuNjY2NjY2NjcsNSBDNy4yOTg0NzY4Myw1IDcsNC42MjY5MDM5NiA3LDQuMTY2NjY2NjcgTDcsMi44MzMzMzMzMyBDNywyLjM3MzA5NjA0IDcuMjk4NDc2ODMsMiA3LjY2NjY2NjY3LDIgTDEwLDIgWiBNMTEsMSBMMTEsMyBMMTMsMyBMMTMsMSBMMTEsMSBaIiBpZD0iU2hhcGUiIGZpbGw9IiM2NjY2NjYiIGZpbGwtcnVsZT0ibm9uemVybyI+PC9wYXRoPgogICAgICAgIDxwb2x5Z29uIGlkPSLlvaLnirbnu5PlkIgtcGF0aOWkh+S7vSIgZmlsbD0iIzg5ODk4OSIgZmlsbC1ydWxlPSJub256ZXJvIiBwb2ludHM9IjIzIDEyIDIzIDIzIDEyIDIzIDEyIDEyIj48L3BvbHlnb24+CiAgICAgICAgPHBvbHlnb24gaWQ9IuefqeW9ouWkh+S7vSIgZmlsbD0iI0ZGRkZGRiIgZmlsbC1ydWxlPSJub256ZXJvIiBwb2ludHM9IjE4IDEzIDIyIDEzIDIyIDE3IDE4IDE3Ij48L3BvbHlnb24+CiAgICAgICAgPHBvbHlnb24gaWQ9IuefqeW9ouWkh+S7vS0zIiBmaWxsPSIjRkZGRkZGIiBmaWxsLXJ1bGU9Im5vbnplcm8iIHBvaW50cz0iMTMgMTMgMTcgMTMgMTcgMTcgMTMgMTciPjwvcG9seWdvbj4KICAgICAgICA8cG9seWdvbiBpZD0i55+p5b2i5aSH5Lu9LTIiIGZpbGw9IiNGRkZGRkYiIGZpbGwtcnVsZT0ibm9uemVybyIgcG9pbnRzPSIxOCAxOCAyMiAxOCAyMiAyMiAxOCAyMiI+PC9wb2x5Z29uPgogICAgICAgIDxwb2x5Z29uIGlkPSLnn6nlvaLlpIfku70tNCIgZmlsbD0iI0ZGRkZGRiIgZmlsbC1ydWxlPSJub256ZXJvIiBwb2ludHM9IjEzIDE4IDE3IDE4IDE3IDIyIDEzIDIyIj48L3BvbHlnb24+CiAgICA8L2c+Cjwvc3ZnPg==);
  background-repeat: no-repeat;
}

.gc-spread-pasteColumnWidth {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMjRweCIgaGVpZ2h0PSIyNHB4IiB2aWV3Qm94PSIwIDAgMjQgMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+UGFzdGUgQ29sdW1uIFdpZHRo5aSH5Lu9PC90aXRsZT4KICAgIDxnIGlkPSJQYXN0ZS1Db2x1bW4tV2lkdGjlpIfku70iIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwYXRoIGQ9Ik02LDIgTDYsNCBDNiw1LjEwNDU2OTUgNi44OTU0MzA1LDYgOCw2IEwxNiw2IEMxNy4xMDQ1Njk1LDYgMTgsNS4xMDQ1Njk1IDE4LDQgTDE4LDIgTDIwLDIgQzIwLjU1MjI4NDcsMiAyMSwyLjQ0NzcxNTI1IDIxLDMgTDIxLDExIEwxMSwxMSBMMTEsMjEgTDQsMjEgQzMuNDQ3NzE1MjUsMjEgMywyMC41NTIyODQ3IDMsMjAgTDMsMyBDMywyLjQ0NzcxNTI1IDMuNDQ3NzE1MjUsMiA0LDIgTDYsMiBaIiBpZD0iUGF0aCIgZmlsbD0iIzc1QkVGNCIgZmlsbC1ydWxlPSJub256ZXJvIj48L3BhdGg+CiAgICAgICAgPHBvbHlnb24gaWQ9IkNvbWJpbmVkLVNoYXBlIiBmaWxsPSIjRkZGRkZGIiBmaWxsLXJ1bGU9Im5vbnplcm8iIHBvaW50cz0iMTggMTEgMTEgMTEgMTEgMTggNiAxOCA2IDQgMTggNCI+PC9wb2x5Z29uPgogICAgICAgIDxwYXRoIGQ9Ik0xMCwyIEwxMCwwLjgzMzMzMzMzMyBDMTAsMC4zNzMwOTYwNDIgMTAuMjk4NDc2OCwwIDEwLjY2NjY2NjcsMCBMMTMuMzMzMzMzMywwIEMxMy43MDE1MjMyLDAgMTQsMC4zNzMwOTYwNDIgMTQsMC44MzMzMzMzMzMgTDE0LDIgTDE2LjMzMzMzMzMsMiBDMTYuNzAxNTIzMiwyIDE3LDIuMzczMDk2MDQgMTcsMi44MzMzMzMzMyBMMTcsNC4xNjY2NjY2NyBDMTcsNC42MjY5MDM5NiAxNi43MDE1MjMyLDUgMTYuMzMzMzMzMyw1IEw3LjY2NjY2NjY3LDUgQzcuMjk4NDc2ODMsNSA3LDQuNjI2OTAzOTYgNyw0LjE2NjY2NjY3IEw3LDIuODMzMzMzMzMgQzcsMi4zNzMwOTYwNCA3LjI5ODQ3NjgzLDIgNy42NjY2NjY2NywyIEwxMCwyIFogTTExLDEgTDExLDMgTDEzLDMgTDEzLDEgTDExLDEgWiIgaWQ9IlNoYXBlIiBmaWxsPSIjNjY2NjY2IiBmaWxsLXJ1bGU9Im5vbnplcm8iPjwvcGF0aD4KICAgICAgICA8cG9seWdvbiBpZD0i5b2i54q257uT5ZCILXBhdGjlpIfku70iIGZpbGw9IiM2NjY2NjYiIGZpbGwtcnVsZT0ibm9uemVybyIgcG9pbnRzPSIyMyAxNSAyMyAyMyAxMiAyMyAxMiAxNSI+PC9wb2x5Z29uPgogICAgICAgIDxwb2x5Z29uIGlkPSLnn6nlvaLlpIfku70tNCIgZmlsbD0iI0ZGRkZGRiIgZmlsbC1ydWxlPSJub256ZXJvIiBwb2ludHM9IjEzIDE2IDIyIDE2IDIyIDIzIDEzIDIzIj48L3BvbHlnb24+CiAgICAgICAgPHJlY3QgaWQ9IuefqeW9ouWkh+S7vS0zIiBmaWxsPSIjMzY3RkM5IiB4PSIxMiIgeT0iMTIiIHdpZHRoPSIxMSIgaGVpZ2h0PSIxIj48L3JlY3Q+CiAgICAgICAgPHJlY3QgaWQ9IuefqeW9ouWkh+S7vS01IiBmaWxsPSIjMzY3RkM5IiB4PSIxMiIgeT0iMTEiIHdpZHRoPSIxIiBoZWlnaHQ9IjMiPjwvcmVjdD4KICAgICAgICA8cmVjdCBpZD0i55+p5b2i5aSH5Lu9LTYiIGZpbGw9IiMzNjdGQzkiIHg9IjIyIiB5PSIxMSIgd2lkdGg9IjEiIGhlaWdodD0iMyI+PC9yZWN0PgogICAgPC9nPgo8L3N2Zz4=);
  background-repeat: no-repeat;
}

.gc-spread-pasteTranspose {
  background-image: url(data:image/svg+xml;base64,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);
  background-repeat: no-repeat;
}

.gc-spread-pasteValueAndNumberFormats {
  background-image: url(data:image/svg+xml;base64,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);
  background-repeat: no-repeat;
}

.gc-spread-valueAndSourceFormats {
  background-image: url(data:image/svg+xml;base64,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);
  background-repeat: no-repeat;
}

.gc-spread-pasteLink {
  background-image: url(data:image/svg+xml;base64,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);
  background-repeat: no-repeat;
}

.gc-spread-insertComment {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAOCAYAAAAmL5yKAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTM4IDc5LjE1OTgyNCwgMjAxNi8wOS8xNC0wMTowOTowMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTcgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjA2MDkwNTRFNDJCNDExRTc5MkIxQTM0NTZCOUEwNEFBIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjA2MDkwNTRGNDJCNDExRTc5MkIxQTM0NTZCOUEwNEFBIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6MDYwOTA1NEM0MkI0MTFFNzkyQjFBMzQ1NkI5QTA0QUEiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6MDYwOTA1NEQ0MkI0MTFFNzkyQjFBMzQ1NkI5QTA0QUEiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz60jjNKAAAB2UlEQVR42oxSPUscURQ9b7MK+Q+CoD9hq2DnL0iVyiZItEi1wQ0Ykd0mlUhASKGB/IA0qawkayVEZEu7ZINESUbcVXHn433nvvtmdjE2DhzOm5l3zr33cOF3G965bjvg/zMe80yE39x97rYfoxfhohDooLcINOhLj9DoYphc4uLsN/JcMrKMOCuICWmBNC/wZuOLEONWV3oT270Gk335Ad4qeKeYnZPEkt93dvYxGN6g7l9tdR50sLJFF5/AG0MzGhJoeK/pHNgwS0UoDGqxYovb5srMLbroQA5R4CuTyI5MtVKQmgzEp1YnffGOLj1lL++m4Ja3Ufu8FkWMWDWcAwdDKTUMdVETqz1xezUg92kSfoSzoWsHs/SehQEuZDAexXInSpEJoR6qGhPbZNybOYwwEaM0DKy0JthooLThmSv3yqiqHNu2+NHPkFxeI0mGmJ+bwd/kNIaoVQzL21jB2xCULsObdPXz1x8sPJvF9+NTLD2fhhl3oKoRyqDKsyurO3636PfP8Xb9BJvNOdTon7GlgaZ5jrqHyPKi3LgcablxvIX0Pc8ULCW82ZxnsaORNYUtquU7+Praj+4k7shgNJIsHqUSaarIRCErFIpco5CKFyjkFsL/J8AAobbYzZKR99AAAAAASUVORK5CYII=);
  background-repeat: no-repeat;
}

.gc-spread-sortAscend {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAPCAYAAADUFP50AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTM4IDc5LjE1OTgyNCwgMjAxNi8wOS8xNC0wMTowOTowMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTcgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjMxOTlCRUM2NDJCNDExRTc5RTE1RTkxNEMyMTAwMjYzIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjMxOTlCRUM3NDJCNDExRTc5RTE1RTkxNEMyMTAwMjYzIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6MzE5OUJFQzQ0MkI0MTFFNzlFMTVFOTE0QzIxMDAyNjMiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6MzE5OUJFQzU0MkI0MTFFNzlFMTVFOTE0QzIxMDAyNjMiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz4BcqYtAAABGElEQVR42qyRsW7CMBCG/7No5wYJHgCpKgtT+gjZvMLUPWysmC4VA5S5G9lZSFam8gj0Abr0AZqBrhVSrmerkUANwUNPsnw++ff/+Q74ja7O4judcVevQ5yJh9ETl7kqE6lEYP5gqAE8wgl7ehUw817EiXvAV/jN1zETpYp4S0B4q7OOlxBEA0leBXPnjswXcZW8HonL9n3TJ7sE2ZAHrrJOso/LgohiIYhsh/FfcTyORv7o3BbHF5iRtJ8xrEWVS8FVgWZrbilhZL1dEjlHYqQ3C+xzg1A4xkWBex9s64Ivg+CgsGOCac+Q1v3tZI4HwlKQ0yqRjdXLlP6cbXPyCdZl0SJ/Tk6bVeVMIqya11CaldT98UeAAQC1jWR89hnvdgAAAABJRU5ErkJggg==);
  background-repeat: no-repeat;
}

.gc-spread-sortDescend {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAPCAYAAADUFP50AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTM4IDc5LjE1OTgyNCwgMjAxNi8wOS8xNC0wMTowOTowMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTcgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjQzRDlFNTlBNDJCNDExRTc5M0U1RTRCOTU1NkNDNDRGIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjQzRDlFNTlCNDJCNDExRTc5M0U1RTRCOTU1NkNDNDRGIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6NDNEOUU1OTg0MkI0MTFFNzkzRTVFNEI5NTU2Q0M0NEYiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NDNEOUU1OTk0MkI0MTFFNzkzRTVFNEI5NTU2Q0M0NEYiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz7esVRmAAABKUlEQVR42pRSsU7DMBB9NmEnSK2YQVAWJvoJnZq1ZWGnG2sCS9WleGZr9y44a6b+AtlBiHwAHcqO5ONsNVVSBdU8KdbdKe/u3bPF6hExAIUKiDBvP2GEHdzej2nxPBE2lvxTeGhw3JrCFhL+8ibSLgJB0EcK61WCawJiY9CFB2RLIf9OEELihWeOThQKH2Jgjx+BGUvW7Sk0PCE35oD3svvBSv56qJv110RlbeEGVBY53W/Oxs1/IyiDyyi945EzAdN9y27yvTtuLx3o8c0XBDn0MsceV9EiJKI1k+eugQfcfhd9HZMQ+QEMk+WrAc4+skFRfWrNUoUYcrC0JJcS1eSW77OWn0dprxOl23uz0zt9vWySV50smOSS92zgujLpkxWcVmtN+BVgAGcAaQc1+ebbAAAAAElFTkSuQmCC);
  background-repeat: no-repeat;
}

.gc-spread-editComment {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTM4IDc5LjE1OTgyNCwgMjAxNi8wOS8xNC0wMTowOTowMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTcgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjgxRDBDRTYzNDJCNDExRTc4OEM4QkU2MjcxNTlCOTFGIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjgxRDBDRTY0NDJCNDExRTc4OEM4QkU2MjcxNTlCOTFGIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6ODFEMENFNjE0MkI0MTFFNzg4QzhCRTYyNzE1OUI5MUYiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6ODFEMENFNjI0MkI0MTFFNzg4QzhCRTYyNzE1OUI5MUYiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz54ygF9AAACB0lEQVR42pSSP2gUURDGvz1DwNIyggiXRjshWEiukq3UQgs767VIFQ2CRUAQBMukkVsR4xUWphC8UkgagwlGBCEI6p6g8ZYTchHu9s/778zbVREtzLFz77Hsb76ZbwY44K/dbjsXRY7vjYOCYRji1tQUEkrw33DyBCQYotPpoNVq4S69C4a7K1RCAOcspNJQUkMIhaIUKIoqjo1uo3k5AfpLQLqMq3GEOI6DYPh1xR05eqpK7xw9Bs4qOimMxOdu+Ae4ukpVTF7C3vA79xz8Ks3B0p/x4az+J3j+2j0IqSBKjYY1tiYtPRXkwWdn/wLPXW/DWm5NQlCLDcNwXW7AijA49OgxmrPbeLE0/Vtx4T5A7cBVnmhSb2htvFkMW1ep9tMUeP8arcORBy8sPKh94G81JJtKMaG19tm62yfwrreHrTcpvqz1EXVjzMyQ4p2HsEaQapWYT6kUBVX64e2ya548TRmld9fZ6rSmrO/Cq35MRhh828dgMKTYx8bLHTR4rlyKM1VmZxSZUvX2U4nh5FOK2TPHsbm1gysXJ6FJeYLr5w/Bc60hvlsPKu8Dj67X28WNm6+wON+kEWloQ7Ci+jfW1pEXJfKcNiovkOV0z0q/Xfy+yCUMjXFxftqDlgxW2lYb8vzpnBuPBEYEj8fCg+NMIMskJZDIS4myUCiF9MvBa8xG/xBgAKOSrgO6at9SAAAAAElFTkSuQmCC);
  background-repeat: no-repeat;
}

.gc-spread-Totals {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAa0lEQVQ4T2NkoBAwUqifAdmABQwMDPFEGghSmwhSi2xAAQMDwwUkAwyQ+MhskBIQfwK6AQ4MDAwHkAxA5uOUQ3YBxQYMAy+MsDAIYGBg2ICekBIYGBjuE5kSFRkYGEDJGSUpE5kNUJVRnBsBMEouEYfPNZcAAAAASUVORK5CYII=);
  background-repeat: no-repeat;
}

.gc-spread-editCellType {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAj9JREFUOBFjrOxa+Z/hPwMYgBiMQAgXALIZwJJAmpHhNxMTo3dbSdhuiGooWdyyBKodRRgrx7zxVKJZw+npyJJMv379RubjZFs2nnRn+P9/FlDBVWRFTL9//UHmY2VbNZwy+PefYTVQsv9Ug+kUZEVMv39DXOA56Tb7////QZ5GATYNZ+SAVmwDBsI2g+/nl6JIAjlwL7x7926dRePpLmQFDg3nBX4z/N0OFLstJCQY//3HLxAbBQBdAPECEyPDJGCIFwADKQekAuSibwy/N4DYbOwcAdvzVH/++vlLEsRHBiywQDxeb74TGMppjP//zQYG2NN3796HARWqMbOwWh6p1HsP0vQLGl4N//8zSa84WP3586cIYCAiYuFkvdn8/4xMzX//M64FusaHGRjvx2qMHsJsBLr2acPMzVwK6w6vtzdVqv3x7dtxpl/QQIQpOlVv2ggMydlAL4UcrzM9DxMH0eJCPF5qEkI7PW00/bbtO3Pr+LUbeQxOgVVEJaS2KeuE12w/dfzNm7f/l63d/Swxu9scZCgLKBBtvEvBhjAyApPxf2ByBtIgAGIzMTIdk9TSKFZRlOpxMFWyOHPhxodDR6/XzJ9aehKsiBAx5eKbzA1XXz7bdufN3z2Hz3zJLJlYSUgPXL7syFP1KWdfvnnx9ff/r7///qtfchgjHbDAVaMxGg88mff2y8/Yyx9+sDx6/4MhREP4xS8JuWI0ZQw4DXj87tthFiYGi19//sm++/Xv96rLr2p7PJWuoRsAABsE/6rSSX85AAAAAElFTkSuQmCC);
  background-repeat: no-repeat;
}

.gc-spread-editCellDropdowns {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAa5JREFUOBFjnLpk9/+///4y/P37l+Hfv38Mf4EYRP8D8kFskBxYHCrfXprAyIAEWEAKcmPdkYRwM8u75mNIMoFsJhaAXIIOmLAJoiuC8f/+/QNjwmmSDMBmGQuyF7Zs2cKwefNmuOkghq+vL4OPjw9Y7O9fbF74jxAEKQRpgAFkzSCxf8AARwcoLgBJwmxDZ4P4MC80/P/PJL3iYPXnz58igNGIcAFIEQggGwIRgZA8PFx/G2Zu5lJYd3i5lamS55otxxaxgBIMkeChqY5G1t9/LDudLdRslm88fPX4tRt5WF2AxcAn56/e92dm4ZzhbKpssevguecXL95N3jyr4RsLyF/lnfMY/v3/D0zOf4D+BNGg5AtJytLiwp/klJWKuRhYpjqZq1icuXDjw6Gj12vmTy09icUiTKEpF99kbrj68tm2O2/+7jl85ktmycRKTFU4RMqOPFWfcvblmxdff///+vvvv/olh7ejK2VBF4DxGw88mff2y8/Yyx9+sDx6/4MhREP4xS8JuWKYPIzGacDjd98OszAxWPz680/23a9/v1ddflXb46l0DaYRRgMA06fsoLnNar4AAAAASUVORK5CYII=);
  background-repeat: no-repeat;
}

.gc-spread-deleteComment {
  background-image: url(data:image/png;base64,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);
  background-repeat: no-repeat;
}

.gc-spread-tableInsertColumnsLeft {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAPJJREFUOBFjYKAQMML022fu+e/qqw/m7t58keHgdBe4HEwNNpoFWfDjlz8M/5EFiGCjGHBq/1UitKAqIehMkNeQtYRa/gZz////z5Ab78XIhCz5qlkBRTFMzjPAgAGEQSA71gOMf//5A+ajeAEsgoX4/RfT3F+/f5FgwO9/GMb++U2CC379wTQA5gJwGKD7HZ3/5y8DAwiDQFX3fDD+9RsSmOAwEKt9wAjTBKJBfIhyCInsgrbSRLBgSdssMA2PBZgmGI1swO/f/xlAGBn8xhaI2DSDNGGLhd+kBOJvPIFIVDp4cP423PXxxW1gNiglUgUAAHOkbVTsKQH5AAAAAElFTkSuQmCC);
  background-repeat: no-repeat;
}

.gc-spread-tableInsertColumnsRight {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAPVJREFUOBFjYKAQMJKi3z5zz39XX32wlt2bLzIcnO7CyEKKASC1H7/8YfiPpIlkA07tv4qknYEB7AWQ05BFQy1/g7n///9nyI33wutNJphGzwADBhAGgexYDzD+/ecPTBpOv2pWQLEM7oXff1HEwRp+/f4F14iLgTDg9z8MNX9+Y7oAXRHcgF9/MA0gxgXwMPjzl4EBhEGgqns+GP/6DQlMkBi632F8rC5oK00E6WEoaZsFpkGEWO0DRpgmEA3ig8ThLvj9+z8DCCOD32iBCNMEo0Fq4S7AFgu/sQQismZUA8gMRLgLHpy/DXd9fHEbhA1MiTQHAK/6bFU/afyPAAAAAElFTkSuQmCC);
  background-repeat: no-repeat;
}

.gc-spread-tableInsertRowsAbove {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAPpJREFUOBFjYKAQMIL022fu+Q8z5+B0F7AYjA+iXzUr/BerfYAhDpJjAhGuvvoMZo7aICZWANIMMgSbJAtYECiFLItLMTaXgA3YveUiiuHYnItNM4omfBxcLsKnh35yGFEzZdH2/79+/2b49esXw+8/fxh+/4bQv0D07z8MEBooD1SzuL8GQz/DZKAB6KCicy660P+4olZwxIHTAYqH/yNHKESGkRHTIgaoOgwZ5FSJYjAWDijVQhISmqRngAHQ//8Zfv8F4j//gP79B+eD2H+A4g/O3wbrwvQCUBjiLFDy/A9E/xnAXgAKgsRBbGRPYnXBtg0X0NxEQy4A0oaWOlQyq2QAAAAASUVORK5CYII=);
  background-repeat: no-repeat;
}

.gc-spread-tableDeleteColumns {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAXBJREFUOBFjYKAQME5dvON/dqwHI7o59pl7/iOLHZzugqEmtrDlPxOyInS2u78Bg7u/Prowgg+0guX/fxSLEJJA1q/f/4Akbvn/QDkWBkZGhokLtvz/8+cvw5+/fxj+Aumq7DCwc38CDQBKw0F23cT/f/8C1f0BqgPRQMySg8X/MB0gFzAimTC1KR/JOIgqFphibPSvP0Dn/wd5AzfAbwCaC7AZgzMWmJiYLH7/ZTj+8xd+F2D4CWTLh4Kgk79//3nwgUk4J/VvdBArC+u++W/rv8ssPfAE3RVYXQCMiRl/fv+x4Pj89OSCT21/Z7ysnfP1209/dM0gPlYXgCSeJ3qK/vz18wbQIP6/v/8Zq68/chEkjg6wuuBtriffz98/1v398+cqEC/7/eubI7pGGB+rAZ/e/1zz59dvUd5/TF5qem4J3758V73opscN04RMYzXg7+8/HH9+/8sSW33gC2NDwz9uMabCl0/fqiJrpBobAEm1oy4uYR9PAAAAAElFTkSuQmCC);
  background-repeat: no-repeat;
}

.gc-spread-tableDeleteRows {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAVBJREFUOBFjZACCyYu2//8PYoAAkJUX78UI4ZBB9s/bDDeLGO1M6Ir+/P2LLoSXj2HAX0oN+PP3D14byZL8UBB08nW238pP2YHCMAOeRDvIgNjg0LbP3IMz4JgYmHzWME8U+/Xrd8OfX8AQ+vOn7e/vP7F/fv1epb7++FS4Ae5+Bgw///xj+PXrH8Pvv/8Zfv0Gsv/8Z/j9+9/7u+fvxi3/1nXy56+fN/78/sP/9/c/Y/X1Ry6CXMACIkDgPxCC0gDETRAxEP8/4//zWUzrTv78/WPd3z9/rwJd8OD3rx+OQBWoBuzaBOZDdSIoVmbWAtMPV5cCnSzK95/FRFTP7dvp/SsnX3TT49bfdekrQiUe1p1wu0M3Aq2cYEquhmqz7dKWNoDxUeic+kk4AxRFIZSDkZD+/CEtHWAYQHlK/ENaXgBHY1xRKzgWQd76+480AwAVyp7Zznx5LAAAAABJRU5ErkJggg==);
  background-repeat: no-repeat;
}

.gc-spread-tableInsertRowsBelow {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAPlJREFUOBFjYKAQMKLrn7Jo+/9fv38z/Pr1i+H3nz8Mv39DaJDYb5A4iP/7D5he3F+DoZ9hMtAAdFDRORdd6H9cUet/kOVM6C5g+A8WRxFmZMS0CKYOQ8Y+cw+mCSjGITgHp7swsiC4CJZngAHQ//8Zfv8F4j//gP79B+eD2H+A4g/O3wZrwPQCUBjiLKBDgN4Bep4B7AWgIEgcxEZ2IlYXbNtwAeGcQc8Cexc55EEhi+7qV80K/8VqH2CIg9SBA9HVV5/BzFEbXR+cD9IMMgQugMSABCIowJEEcSnG5hKwAbu3XETSzsCAzbnYNKNowsfB5SJ8eoiWAwD2VZY6VOVQLwAAAABJRU5ErkJggg==);
  background-repeat: no-repeat;
}

.gc-ui-contextmenu-disable .gc-spread-tableInsertColumnsLeft {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAIdJREFUOBHNUkEOgCAMQ+P74K3wBf7DF9AulgCKQPTgkqXbaJesQamvwlobQwiSqEf3rqPEFm/LH7z3eTtULz3W0znGmFJ/R669iWeQ+9qDnyzgPTS07jkHOuckOZMT4CZFwIu7ZB+otZbkKHlAEZGEHqYFIM6KoSkWYDAbxVduifMvDhM/jR15Eks6nBv2awAAAABJRU5ErkJggg==);
  background-repeat: no-repeat;
}

.gc-ui-contextmenu-disable .gc-spread-tableInsertColumnsRight {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAIdJREFUOBHNUkEOgCAMm8b3wVvhC/yHL6g1dhkEDogxNlnYGO02QORLhBD2nPNl8FF7nW1gGxVIKRWUBRHbKTJ34L2/zrRyugcBojWnHjydutj0HfxIIMYosB7q2RnrCM45gfWA1yAJK19HBXpEu08SV+SGBECw5EcCIFnoV7YXWH9XS3jdPwD670s6Akl6lAAAAABJRU5ErkJggg==);
  background-repeat: no-repeat;
}

.gc-ui-contextmenu-disable .gc-spread-tableInsertRowsAbove {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAIVJREFUOBG9UYkJwCAMTEvXcgZnNSu4jytYrqD4JIaWtoGA3l3OJBK9ESGEXFLyAyfhFUspZeRKqHFHdWkOmhi4935rpHQZxBhbjEYRSKm4K1pdtI5WNf9x3ULw7J12sSvxF5xz5ghl8bupNARiB8XdqJVp7GAMDYPDNyMw89SehE2iJ8AJ2XZh/l3z/VsAAAAASUVORK5CYII=);
  background-repeat: no-repeat;
}

.gc-ui-contextmenu-disable .gc-spread-tableDeleteColumns {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAP9JREFUOBFjYKAQMG7cuPG/v78/I7o5IHFkMVxqmJAVobNtbW0ZQBgfwGsAPo0wORYQgxjnYlMHEmPB5jeQBDaATS3FXqCdASwsLBZAbxzH5hVkMYz4B0lu2rTpJJB6ICAgkPPhw4cgRkbGfczMzN+9vLyeIGsGsbF6AahhBlDOAqj5JJD999+/f3P+/v3rj64ZxMfqApDEtm3bRP/8+XPj/////ExMTMa+vr4XQeLoAJwO0AWBmvmAmtcBNV8FuuABkHYEqsFqAFYvADWvAWoS5ePj8wLanADUrLpz505udItAfKwGADVzAG3OcnR0/AKk/ykrKxcCDVXFZgDFYgBvWFxcHU+rmAAAAABJRU5ErkJggg==);
  background-repeat: no-repeat;
}

.gc-ui-contextmenu-disable .gc-spread-tableDeleteRows {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAPNJREFUOBFjZACCjRs3/gfRMODv788IY5NMoxtGyAAmQgoIyQ+8AYRcCJbftGnTSSBeuWfPHmGYhm3btsmA2ODQxhdwzMzMPv///xcD4gag+r+MjIxt//79i2ViYlrl6+s7lQVkCgjY2tpCGGgkUMPiw4cPx7GwsJj8+fPnBlDzDKBmY6DmiyClcAPQ9CFzzwM1nwRqXgd0xVWggQ+AtCNQAdgAgl4Aatb7+/dvL1CTHB8fn4mDg8O3zZs3T2ZnZy9zd3f/imwTTjYwjA4BA9EJpuDq1atsW7duNYDxUWh8AYqiEMoZ+IREsQvA0Uiqv5HDAgCeaV8TkwuKRgAAAABJRU5ErkJggg==);
  background-repeat: no-repeat;
}

.gc-ui-contextmenu-disable .gc-spread-tableInsertRowsBelow {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAINJREFUOBHNkt0JwCAMhG3pWs7grLqC+7iC5QR9OC9VKIUKgvlyOeOPcy/HwfUxxsrMikMIU72DAQ+Lwfi03Hf5pYQppQkrBpE08N5PBgxyzg19c4Tuzrv+OMY796naRE7xwUopFfNJaOXkM1picP6+zYBvnUVoVRWPI6wWVkeruq38DTljYf5lkhHeAAAAAElFTkSuQmCC);
  background-repeat: no-repeat;
}

/*-----contextmenu end-----*/
/*-----chart start-----*/
.gcdv-control {
  outline: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.gc-filter-dialog-color-title {
  background-color: rgb(235, 235, 235);
  color: rgb(106, 106, 106);
}

.gcdv-state-disabled {
  opacity: 0.5;
  cursor: default;
  pointer-events: none;
}

.gcdv-tooltip {
  position: absolute;
  z-index: 1000;
  top: 0;
  left: 0;
  pointer-events: none;
  max-width: 400px;
  padding: 6px;
  background-color: #ffffe5;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  box-sizing: border-box;
}

.gcdv-popup {
  background-color: #fff;
  box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  z-index: 1500;
  margin: 2px 0;
}

.gcdv-popup-backdrop {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1500;
  background-color: rgba(0, 0, 0, 0.5);
}

/*-----custom chart start-----*/
.gcdv-tooltip {
  border-radius: 0;
  box-shadow: 3px 3px 3px #8e8e8e;
}

/*-----custom chart end-----*/
/*-----chart end-----*/
/* status bar start */
.gc-statusbar {
  width: 100%;
  height: 100%;
  background: #217346;
  font-size: 8pt;
  color: #eef5f1;
  font-family: "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
  position: relative;
  user-select: none;
  cursor: default;
  overflow: hidden;
}

.gc-statusbar-menu-host {
  position: relative;
}

.gc-statusbar-statusitem-container {
  width: auto;
  height: 100%;
}

.gc-statusbar-zoom-panel {
  width: 30px;
  height: 100%;
  cursor: default;
  font-size: 8pt;
  color: #eef5f1;
  display: inline-block;
}

.gc-statusbar-slider-btn-container {
  display: inline-block;
  vertical-align: middle;
  margin-right: 3px;
}

.gc-statusbar-slider-btn-container:hover {
  background-color: #1d5838;
}

.gc-statusbar-slider-btn-container:active {
  background-color: #0f331f;
}

.gc-statusbar-slider-btn {
  width: 16px;
  height: 25px;
  background-image: url(data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMTYiIGhlaWdodD0iMTYiPjxkZWZzPjxzdHlsZS8+PC9kZWZzPjxwYXRoIGQ9Ik04NjMuNzQ1IDU0NGgtNzAwLjMyYy0xNy42NjYgMC0zMi4wMDItMTQuMzM1LTMyLjAwMi0zMnMxNC4zMzYtMzIgMzIuMDAxLTMyaDcwMC4zMmMxNy42OTYgMCAzMiAxNC4zMzUgMzIgMzJzLTE0LjMwNCAzMi0zMiAzMnoiIGZpbGw9IiNlZWY1ZjEiLz48L3N2Zz4=);
  background-size: 16px 16px;
  background-repeat: no-repeat;
  background-position: center;
}

.gc-statusbar-slider-add-btn-container {
  display: inline-block;
  vertical-align: top;
}

.gc-statusbar-slider-add-btn-container:hover {
  background-color: #1d5838;
}

.gc-statusbar-slider-add-btn-container:active {
  background-color: #0f331f;
}

.gc-statusbar-slider-add-btn {
  width: 16px;
  height: 25px;
  background-image: url(data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMTYiIGhlaWdodD0iMTYiPjxkZWZzPjxzdHlsZS8+PC9kZWZzPjxwYXRoIGQ9Ik04NjMuMzI4IDQ4MS4zNGwtMzE3LjM0NC4xVjE2Mi44MThjMC0xNy42NjUtMTQuMzM2LTMyLjAwMS0zMi0zMi4wMDFzLTMyIDE0LjMzNi0zMiAzMnYzMTguNDAxbC0zMjIuMzY4LS4xNzdoLS4wOTdjLTE3LjYzMiAwLTMxLjkzNSAxNC4yNC0zMiAzMS45MDQtLjA5NyAxNy42NjUgMTQuMjA4IDMyLjAzMiAzMS44NzEgMzIuMDk2bDMyMi41OTMuMTc3djMxOS4xNjdjMCAxNy42OTYgMTQuMzM2IDMyLjAwMSAzMS45OTkgMzIuMDAxczMyLTE0LjMwMyAzMi0zMlY1NDUuNDRsMzE3LjA4OC0uMWguMTI4YzE3LjYzMiAwIDMxLjkzNS0xNC4yNCAzMi0zMS45MDVzLTE0LjIzOC0zMi4wMzEtMzEuODctMzIuMDk1eiIgZmlsbD0iI2VlZjVmMSIvPjwvc3ZnPg==);
  background-size: 16px 16px;
  background-repeat: no-repeat;
  background-position: center;
}

.gc-statusbar-slider-back-progress {
  width: 120px;
  height: 1px;
  background: #eef5f1;
  display: inline-block;
  vertical-align: top;
}

.gc-statusbar-slider-back {
  display: inline-block;
}

.gc-statusbar-slider-middle-line {
  width: 1px;
  height: 8px;
  background: #eef5f1;
  display: inline-block;
  position: relative;
  vertical-align: top;
  top: 9px;
}

.gc-statusbar-slider-drag-bar {
  width: 4px;
  height: 10px;
  background: #eef5f1;
  display: inline-block;
  position: relative;
  vertical-align: top;
  top: 8px;
}

.gc-statusbar-progress-item-item-container {
  height: 100%;
  display: flex;
  align-items: center;
}

.gc-statusbar-progress-item-wrapper {
  height: 50%;
  background-color: #217346;
  border: 1px solid #eef5f1;
  box-sizing: border-box;
}

.gc-statusbar-progress-item-bar {
  width: 0;
  height: 100%;
  background-color: #eef5f1;
}

.gc-statusbar-progress-item-status {
  margin-left: 4px;
}

.gc-statusbar-progress-item-sheet-name {
  margin-left: 4px;
  overflow: visible;
  height: 100%;
}

.gc-statusbar-contextmenu-check {
  width: 6px;
  height: 10px;
  margin: 0 0 0 5px;
  border-style: solid;
  border-color: #222222;
  border-width: 0 2px 2px 0;
  transform: rotateZ(45deg);
  content: "";
}

.gc-statusbar-contextmenu-check-container {
  width: 12px;
  height: 12px;
  display: inline-block;
  padding-top: 1px;
}

.gc-statusbar-contextmenu-content {
  display: inline-block;
  color: #444444;
  margin: 0 5px 0 15px;
}

.gc-statusbar-contextmenu-status {
  float: right;
  color: #444444;
  vertical-align: middle;
  margin-left: auto;
}

input.gc-checkbox-cell-type-input[type=checkbox]:before {
  position: static;
  display: inline-block;
  border: none;
  content: "";
  background: none;
}
input.gc-checkbox-cell-type-input[type=checkbox]:after {
  position: static;
  display: inline-block;
  border: none;
  content: "";
  background-image: none;
  background-repeat: no-repeat;
}

.gc-chart-unsuppoprtedChart-fartherDiv {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgb(210, 210, 210);
  height: 100%;
  width: 100%;
}

.gc-chart-unsuppoprtedChart-childDiv {
  text-align: center;
}

.gc-sjs-dialog-button {
  padding: 5px 20px 5px 20px;
  border: 1px solid #b4b4b4;
  display: inline-block;
  background-color: #fdfdfd;
  font-size: 12px;
  line-height: 14px;
  min-width: 40px;
  text-align: center;
}

.gc-sjs-dialog-button.disable {
  color: #838383;
  pointer-events: none;
  touch-action: none;
}

.gc-sjs-dialog-button:hover:not(.disable) {
  border: 1px solid #3296c8;
  background-color: rgb(191, 216, 231);
}

.gc-sjs-paste-special-dialog-container {
  background-color: #f0f0f0;
  border: 1px solid #707070;
  box-shadow: 2px 2px 10px #707070;
  font-family: "Segoe UI", Helvetica, Verdana, sans-serif;
  font-size: 12px;
}
.gc-sjs-paste-special-dialog-container label, .gc-sjs-paste-special-dialog-container span {
  user-select: none;
}
.gc-sjs-paste-special-dialog-container label {
  margin-left: 3px;
  font-size: 12px;
}
.gc-sjs-paste-special-dialog-container .gc-sjs-horzation-container {
  display: flex;
  flex-direction: row;
}
.gc-sjs-paste-special-dialog-container .gc-sjs-column-container {
  min-width: 150px;
  margin: 5px 20px 5px 5px;
}
.gc-sjs-paste-special-dialog-container .gc-sjs-vertical-container {
  display: flex;
  flex-direction: column;
}
.gc-sjs-paste-special-dialog-container .gc-sjs-paste-special-dialog-title {
  display: flex;
  justify-content: space-between;
  background-color: white;
  align-items: center;
  height: 30px;
}
.gc-sjs-paste-special-dialog-container .gc-sjs-paste-special-dialog-title-text {
  font-size: 14px;
  margin-left: 5px;
}
.gc-sjs-paste-special-dialog-container .gc-sjs-paste-special-dialog-content {
  padding: 7px 7px 0 7px;
}
.gc-sjs-paste-special-dialog-container .gc-sjs-paste-special-dialog-footer {
  padding: 7px;
  justify-content: space-between;
}

.gc-sjs-separator {
  flex: 1;
  height: 1px;
  background-color: #dcdcdc;
  align-self: center;
  margin-left: 10px;
}

/* status bar end */
div[gcUIElement=gcSpread] .gc-month-picker-container {
  font-size: 12px;
  display: block;
  background-color: #fff;
  border: solid 1px #a7abb0;
  font-family: "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
  overflow-y: auto;
  width: 320px;
  outline: none;
}
div[gcUIElement=gcSpread] .gc-month-picker-container .gc-month-picker-year-container {
  display: inline-block;
  line-height: 126px;
  vertical-align: bottom;
}
div[gcUIElement=gcSpread] .gc-month-picker-container .gc-month-picker-month-container {
  display: inline-block;
}
div[gcUIElement=gcSpread] .gc-month-picker-container .gc-month-picker-separator {
  height: 1px;
  border-top: 1px solid lightgray;
  pointer-events: none;
  width: 100%;
}
div[gcUIElement=gcSpread] .gc-month-picker-container .gc-month-picker-year-item {
  font-size: 18px;
  color: #838383;
  width: 60px;
  text-align: center;
}
div[gcUIElement=gcSpread] .gc-month-picker-container .gc-month-picker-month-item,
div[gcUIElement=gcSpread] .gc-month-picker-container .gc-month-picker-qtr-item {
  height: 30px;
  width: 60px;
  display: inline-block;
  text-align: center;
  line-height: 30px;
}
div[gcUIElement=gcSpread] .gc-month-picker-container .gc-month-picker-month-item.selected, div[gcUIElement=gcSpread] .gc-month-picker-container .gc-month-picker-month-item.selected:hover,
div[gcUIElement=gcSpread] .gc-month-picker-container .gc-month-picker-qtr-item.selected,
div[gcUIElement=gcSpread] .gc-month-picker-container .gc-month-picker-qtr-item.selected:hover {
  background: #3498db;
  color: #fff;
}
div[gcUIElement=gcSpread] .gc-month-picker-container .gc-month-picker-month-item {
  cursor: pointer;
}
div[gcUIElement=gcSpread] .gc-month-picker-container .gc-month-picker-month-item:hover {
  background: #e2f2ff;
}
div[gcUIElement=gcSpread] .gc-month-picker-container .gc-month-picker-qtr-item {
  color: #a3a3a3;
}

div[gcUIElement=gcSpread] .gc-calendar-container {
  border: solid 1px #a7abb0;
  background-color: #fff;
  outline: none;
  font-family: "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
  font-size: 12px;
  line-height: 1;
  color: #222;
  -webkit-tap-highlight-color: transparent;
  border-collapse: separate;
  border-spacing: 2px;
}
div[gcUIElement=gcSpread] .gc-calendar-container td {
  padding: 0px;
}
div[gcUIElement=gcSpread] .gc-calendar-container input,
div[gcUIElement=gcSpread] .gc-calendar-container textarea {
  outline: none;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-date-part-container {
  position: static !important;
  transform: none !important;
  overflow: hidden;
  max-width: 30rem;
  height: auto;
  outline: none;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-date-part-container .gc-header-container .gc-header-title {
  width: 75px;
  height: 22px;
  display: inline-block;
  line-height: 22px;
  text-align: center;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-date-part-container .gc-year-page .gc-content-container,
div[gcUIElement=gcSpread] .gc-calendar-container .gc-date-part-container .gc-month-page .gc-content-container {
  margin-top: 2px;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-date-part-container .gc-year-page .gc-content-container td,
div[gcUIElement=gcSpread] .gc-calendar-container .gc-date-part-container .gc-month-page .gc-content-container td {
  padding: 19px 7px;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-date-part-container .gc-year-page .gc-content-container td .gc-cl-day,
div[gcUIElement=gcSpread] .gc-calendar-container .gc-date-part-container .gc-month-page .gc-content-container td .gc-cl-day {
  width: 40px;
  height: 40px;
  line-height: 40px;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-date-part-container .gc-year-page .gc-content-container td .gc-japan-cl-day,
div[gcUIElement=gcSpread] .gc-calendar-container .gc-date-part-container .gc-month-page .gc-content-container td .gc-japan-cl-day {
  width: 54px !important;
  height: 54px !important;
  padding-top: 14px !important;
  line-height: 16px !important;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-date-part-container .gc-day-page .gc-header-container .gc-year-select-div {
  display: inline-flex;
  padding: 0px 5px;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-date-part-container .gc-day-page .gc-header-container .gc-year-select-div .gc-cl-select-container {
  margin-left: 0px;
  width: 68px;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-date-part-container .gc-day-page .gc-header-container .gc-year-select-div .gc-cl-select-container .gc-cl-select {
  height: 22px;
  min-width: 68px !important;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-date-part-container .gc-day-page .gc-today-container tr {
  height: 20px;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-date-part-container .gc-day-page .gc-today-container i {
  padding-bottom: 0;
  color: #3498db;
  text-decoration: none;
  cursor: pointer;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-date-part-container .gc-cl-button {
  cursor: pointer;
  border: 0px;
  outline: none;
  box-shadow: none;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-date-part-container .gc-cl-button .gc-left-arrow {
  position: absolute;
  top: 5px;
  left: 7px;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-right: 8px solid #3498db;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-date-part-container .gc-cl-button .gc-right-arrow {
  position: absolute;
  top: 5px;
  left: 7px;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 8px solid #3498db;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-date-part-container .gc-cl-disabled-text .gc-cl-day {
  cursor: pointer;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-date-part-container .gc-is-focused {
  background: #e2f2ff;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-date-time-vertical-partline {
  width: 1px;
  height: auto;
  margin: -1px 3px -3px;
  background: #d8dde6;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-time-part-container {
  position: static !important;
  transform: none !important;
  overflow: hidden;
  width: 75px;
  max-height: 265px;
  overflow-y: auto;
  outline: none;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-time-part-container .gc-list-control {
  border: none;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-time-part-container .gc-time-picker-li {
  text-align: center !important;
  cursor: pointer;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-date-time-horizontal-partline {
  width: 100%;
  height: 1px;
  margin: 0 0px;
  background: #d8dde6;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-ok-button-container {
  position: static !important;
  transform: none !important;
  overflow: hidden;
  padding: 1px;
  border: none !important;
  background: #fff;
  text-align: center;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-ok-button-container button {
  width: 115px;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-content-td-date {
  display: inline-flex;
  background: #fff;
  padding: 1px 3px 3px 3px;
}
div[gcUIElement=gcSpread] .gc-calendar-container * {
  box-sizing: border-box;
}
div[gcUIElement=gcSpread] .gc-calendar-container *:before {
  box-sizing: border-box;
}
div[gcUIElement=gcSpread] .gc-calendar-container *:after {
  box-sizing: border-box;
}
div[gcUIElement=gcSpread] .gc-calendar-container table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 2px;
}
div[gcUIElement=gcSpread] .gc-calendar-container table td {
  text-align: left;
}
div[gcUIElement=gcSpread] .gc-calendar-container table th {
  text-align: left;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-datepicker {
  padding: 0;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-datepicker td {
  padding: 0px;
  text-align: center;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-datepicker td:hover:not(.gc-cl-disabled-text) > .gc-cl-day {
  background: #e2f2ff;
  cursor: pointer;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-datepicker td > .gc-cl-day {
  width: 30px;
  height: 28px;
  display: block;
  position: relative;
  line-height: 28px;
  margin: 0;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-datepicker td.gc-cl-is-selected:not(.gc-cl-disabled-text) > .gc-cl-day {
  background: #3498db;
  color: #fff;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-datepicker th {
  text-align: center;
  padding: 3px 0px;
  font-weight: 400;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-datepicker th > .gc-cl-week {
  width: 30px;
  height: 28px;
  display: block;
  position: relative;
  line-height: 28px;
  margin: 0;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-dateRangepicker {
  padding: 0;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-dateRangepicker td {
  padding: 0px;
  text-align: center;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-dateRangepicker td > .gc-cl-day {
  width: 30px;
  height: 28px;
  display: block;
  position: relative;
  line-height: 28px;
  margin: 0;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-dateRangepicker td.gc-cl-is-selected:not(.gc-cl-disabled-text) > .gc-cl-day {
  background: #3498db;
  color: #fff;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-dateRangepicker th {
  text-align: center;
  padding: 3px 0px;
  font-weight: 400;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-dateRangepicker th > .gc-cl-week {
  width: 30px;
  height: 28px;
  display: block;
  position: relative;
  line-height: 28px;
  margin: 0;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-datepicker-filter {
  padding: 0;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-datepicker-filter-month {
  padding: 0 0.25rem 0 0;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-dropdown {
  position: absolute;
  z-index: 7000;
  left: 50%;
  float: left;
  margin-top: 0.125rem;
  background: #fff;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-grid {
  display: flex;
  align-items: center;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-grid-align-spread {
  justify-content: space-between;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-grow {
  flex-grow: 1;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-button {
  position: relative;
  display: inline-block;
  padding: 0;
  background: transparent;
  border: 1px solid transparent;
  line-height: 22px;
  text-decoration: none;
  color: #fff;
  -webkit-appearance: none;
  white-space: normal;
  user-select: none;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-button-brand {
  text-align: center;
  vertical-align: middle;
  color: #fff;
  background-color: #3498db;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-button-icon-container {
  line-height: 1;
  vertical-align: middle;
  width: 22px;
  height: 22px;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-arrow-disable {
  opacity: 0.5;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-align-middle {
  vertical-align: middle;
  align-self: center;
  cursor: pointer;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-shrink-none {
  flex-shrink: 0;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-select {
  background-color: #fff;
  border: 1px solid #d8dde6;
  border-radius: 0.25rem;
  width: 100%;
  transition: border 0.1s linear, background-color 0.1s linear;
  height: 1.75rem;
  cursor: pointer;
  font-family: "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
  font-size: 12px;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-select > option {
  cursor: pointer;
  font-family: "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
  font-size: 12px;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-select-container {
  position: relative;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-select-container:after {
  position: absolute;
  content: "";
  display: block;
  right: 6px;
  width: 0;
  height: 0;
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
  bottom: 10px;
  border-left-width: 4px;
  border-right-width: 4px;
  pointer-events: none;
  border-top: 5px solid #3498db;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-select-container .gc-cl-select {
  overflow: hidden;
  -moz-appearance: none;
  -webkit-appearance: none;
  padding: 0 10%;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-select-container .gc-cl-select::-ms-expand {
  display: none;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-disabled-text {
  color: #d8dde6;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-show-inline-block {
  display: inline-block;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-cl-p-bottom-x-small {
  padding-bottom: 0.5rem;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-date-container {
  z-index: 9999;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-date-time-container {
  z-index: 9999;
}
div[gcUIElement=gcSpread] .gc-calendar-container .gc-date-range-container {
  border: none;
}
div[gcUIElement=gcSpread] .gc-builtIn-date-Range-child-div {
  margin: 5px 2px;
  height: 22px;
  text-align: center;
  line-height: 22px;
}
div[gcUIElement=gcSpread] .gc-builtIn-date-Range-child-div:not(.gc-builtIn-date-range-childDiv-select):hover {
  background-color: #e2f2ff;
}
div[gcUIElement=gcSpread] .gc-builtIn-date-Range-child-div.gc-cl-disabled-text:hover {
  background-color: #fff;
}
div[gcUIElement=gcSpread] .gc-builtIn-DateRangeChildDiv {
  margin: 5px 2px;
  height: 22px;
  text-align: center;
  line-height: 22px;
}
div[gcUIElement=gcSpread] .gc-builtIn-DateRangeChildDiv:hover {
  background-color: #e2f2ff;
  color: #222;
}
div[gcUIElement=gcSpread] .gc-builtIn-date-range-childDiv-select {
  background-color: #3498db;
  color: #fff;
}
div[gcUIElement=gcSpread] .gc-builtIn-date-ranges-div {
  float: left;
  height: 253px;
  width: 80px;
  text-align: center;
  border-top: none;
  border-bottom: none;
  border-right: solid 1px #a7abb0;
  border-left: none;
}
div[gcUIElement=gcSpread] .gc-builtIn-date-ranges-div:hover:not(.gc-cl-disabled-text) > .gc-cl-day {
  background: #e2f2ff;
  cursor: pointer;
}
div[gcUIElement=gcSpread] .gc-date-ranges-result-div {
  height: 30px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  white-space: nowrap;
  text-align: center;
  border: solid 1px #a7abb0;
  background-color: #fff;
}
div[gcUIElement=gcSpread] .gc-date-ranges-result-div > * {
  display: inline-block;
  vertical-align: middle;
}
div[gcUIElement=gcSpread] .gc-date-ranges-result-div .gc-arrow-disable {
  opacity: 0.5;
}
div[gcUIElement=gcSpread] .gc-date-ranges-result-buttonDiv {
  position: static !important;
  transform: none !important;
  overflow: hidden;
  padding: 1px;
  border: none !important;
  background: #fff;
  text-align: center;
  margin: 0 8px;
}
div[gcUIElement=gcSpread] .gc-date-ranges-result-buttonDiv button {
  width: 80px;
}
div[gcUIElement=gcSpread] .gc-date-ranges-result-button {
  position: relative;
  display: inline-block;
  padding: 0;
  background: transparent;
  border: 1px solid transparent;
  line-height: 22px;
  text-decoration: none;
  -webkit-appearance: none;
  white-space: nowrap;
  user-select: none;
  text-align: center;
  vertical-align: middle;
  color: #fff;
  background-color: #3498db;
}
div[gcUIElement=gcSpread] .gc-date-ranges-result-input {
  margin-right: 10px;
  display: inline-block;
  width: 60%;
  height: 22px;
  font-size: 12px;
  white-space: normal;
  line-height: 20px;
}
div[gcUIElement=gcSpread] .gc-date-ranges-calendar {
  display: inline-block;
}
div[gcUIElement=gcSpread] .gc-range-calendar-container {
  display: flex;
  border-bottom: none;
}

div[gcUIElement=gcSpread] .gc-calculator-container {
  font-family: "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
  font-size: 15px;
}
div[gcUIElement=gcSpread] .gc-calculator-container sub,
div[gcUIElement=gcSpread] .gc-calculator-container sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
div[gcUIElement=gcSpread] .gc-calculator-container sup {
  top: -0.25em;
}
div[gcUIElement=gcSpread] .gc-calculator-container sub {
  bottom: -0.25em;
  font-size: 90%;
}
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator {
  width: 250px;
  border: 1px solid gray;
  box-shadow: 0 0 15px 2px rgba(255, 255, 255, 0.2);
  background: #f2f2f2;
}
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-screen {
  height: 50px;
}
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-screen input {
  width: 100%;
  height: 100%;
  border: 0;
  box-sizing: border-box;
  background: transparent;
  text-align: right;
}
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-screen .gc-main-screen {
  height: 100%;
}
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-screen .gc-main-screen .gc-main {
  font-size: 2rem;
  color: #000;
  font-weight: 600;
  padding: 10px 18px 0 10px;
}
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-sub-menu .gc-row,
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-main-menu .gc-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-sub-menu .gc-row .button,
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-main-menu .gc-row .button {
  width: calc(25% - 2px);
  line-height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  margin: 1px;
}
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-sub-menu .gc-row .button:hover,
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-main-menu .gc-row .button:hover {
  background: #cfcfcf;
  cursor: pointer;
}
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-sub-menu .gc-row .button:active,
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-main-menu .gc-row .button:active {
  background: #b8b8b8;
}
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-sub-menu .gc-row .button.disabled,
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-main-menu .gc-row .button.disabled {
  pointer-events: none;
  opacity: 0.3;
}
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-sub-menu .gc-row .number,
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-main-menu .gc-row .number {
  font-size: 18px;
  font-weight: 600;
}
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-sub-menu .gc-row .gc-operation:hover,
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-main-menu .gc-row .gc-operation:hover {
  background: #3498db;
}
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-sub-menu .gc-row .gc-operation:active,
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-main-menu .gc-row .gc-operation:active {
  background: rgba(52, 152, 219, 0.79);
}
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-sub-menu .gc-row .gc-number,
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-main-menu .gc-row .gc-number {
  font-size: 1.2rem;
}
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-sub-menu .gc-row .icon,
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-main-menu .gc-row .icon {
  width: 16px;
  height: 16px;
}
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-sub-menu .gc-row .image:active img,
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-main-menu .gc-row .image:active img {
  width: 24px;
  height: 24px;
}
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-sub-menu .gc-row .dot,
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-main-menu .gc-row .dot {
  font-size: 1.5rem;
}
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-sub-menu .gc-row .icon-group1,
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-main-menu .gc-row .icon-group1 {
  background: #e6e6e6;
}
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-sub-menu .gc-row .icon-group2,
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-main-menu .gc-row .icon-group2 {
  background: #fdfdfd;
}
div[gcUIElement=gcSpread] .gc-calculator-container .gc-calculator .gc-sub-menu .button {
  line-height: 3rem;
}

div[gcUIElement=gcSpread] .gc-list-control {
  user-select: none;
  border: 1px solid #a7abb0;
  background-color: #fff;
  font-family: "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
  font-size: 12px;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-alignment-vertical {
  display: flex;
  flex-direction: column;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-alignment-horizontal {
  display: flex;
  flex-direction: row;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-item-wrap {
  flex-wrap: wrap;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container {
  margin: 0;
  padding: 0;
  border: 0;
  line-height: 1.3;
  text-decoration: none;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container .gc-list-control-group .gc-list-control-group-text {
  display: block;
  background: #f0f2f5;
  font-weight: bold;
  color: #444;
  padding: 3px 4px;
  cursor: default;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container .gc-list-control-group .gc-list-control-group-text.hide-children {
  display: none;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container .gc-list-control-item {
  position: relative;
  cursor: pointer;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container .gc-list-control-item.gc-list-control-hassubitem {
  display: flex;
  align-items: center;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container .gc-list-control-item.gc-list-control-selected-item {
  background: #3498db;
  color: #fff;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container .gc-list-control-item.gc-list-control-selected-item:hover {
  background: #3498db;
  color: #fff;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container .gc-list-control-item.gc-list-control-noitem {
  pointer-events: none;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container .gc-list-control-item .gc-list-control-listseparator {
  height: 1px;
  border-top: 1px solid lightgray;
  margin: 0 0 0 12px;
  pointer-events: none;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container .gc-list-control-item .gc-list-control-subitem {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTQyIDc5LjE2MDkyNCwgMjAxNy8wNy8xMy0wMTowNjozOSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTggKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjRFNUM4M0M4NjcyMzExRTlCM0Y0Q0IyQjZEQTUwQzlCIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjRFNUM4M0M5NjcyMzExRTlCM0Y0Q0IyQjZEQTUwQzlCIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6NEU1QzgzQzY2NzIzMTFFOUIzRjRDQjJCNkRBNTBDOUIiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NEU1QzgzQzc2NzIzMTFFOUIzRjRDQjJCNkRBNTBDOUIiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz7Sk1otAAAAM0lEQVR42mJgGAU4QXl5+X9i1DFRaggTpS5hotQ7RBnQ2dnJSLYB+DRTJRZGARUAQIABAHK7DzeBU6NxAAAAAElFTkSuQmCC);
  width: 16px;
  height: 16px;
  display: block;
  justify-self: flex-end;
  margin-left: auto;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container .gc-list-control-item .gc-list-control-item-text {
  letter-spacing: 0;
  text-align: left;
  padding: 0.4em 1em;
  display: block;
  line-height: normal;
  white-space: pre;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container .gc-list-control-item .gc-list-control-item-text.gc-list-control-item-bigtext {
  line-height: 32px;
  white-space: nowrap;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container .gc-list-control-item .gc-list-control-item-text-ellipsis {
  overflow-x: hidden;
  text-overflow: ellipsis;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container .gc-list-control-item:hover {
  background: #e2f2ff;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container .gc-list-control-item .gc-list-control-item-icon {
  left: 0.5em;
  position: absolute;
  top: 50%;
  margin-top: -8px;
  width: 16px;
  height: 16px;
  overflow: hidden;
  background-repeat: no-repeat;
  background-size: contain;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container .gc-list-control-item .gc-list-control-item-icon.gc-list-control-emptyicon {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTQyIDc5LjE2MDkyNCwgMjAxNy8wNy8xMy0wMTowNjozOSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTggKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkFFNTRFMjUxNjcyMzExRTlBOUQxRTQ3MjgyQjgwNkZGIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkFFNTRFMjUyNjcyMzExRTlBOUQxRTQ3MjgyQjgwNkZGIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6QUU1NEUyNEY2NzIzMTFFOUE5RDFFNDcyODJCODA2RkYiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6QUU1NEUyNTA2NzIzMTFFOUE5RDFFNDcyODJCODA2RkYiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz7Dh39gAAAAH0lEQVR42mL8//8/AyWAiYFCMGrAqAGjBgwWAwACDABjmgMdW2LjxgAAAABJRU5ErkJggg==);
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container .gc-list-control-item .gc-list-control-item-icon + .gc-list-control-item-text {
  padding-left: 25px;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container .gc-list-control-item .gc-list-control-item-icon.gc-list-control-item-bigicon {
  top: 0.5em;
  margin-top: 0;
  width: 32px;
  height: 32px;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container .gc-list-control-item .gc-list-control-item-icon.gc-list-control-item-bigicon + .gc-list-control-item-bigtext {
  padding-left: 42px;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container .gc-list-control-tree .gc-list-control-tree-text {
  display: block;
  font-weight: bold;
  color: #444;
  padding: 3px 4px;
  cursor: pointer;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container .gc-list-control-tree .gc-list-control-tree-content {
  margin-left: 0.4em;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container .gc-list-control-tree .gc-list-control-tree-content .gc-list-control-item .gc-list-control-item-text {
  padding: 3px 4px;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container .gc-list-control-tree .gc-list-control-tree-content .gc-list-control-item .gc-list-control-item-icon + .gc-list-control-item-text {
  padding-left: 25px;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container .gc-list-control-tree .gc-list-control-subitem {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTQyIDc5LjE2MDkyNCwgMjAxNy8wNy8xMy0wMTowNjozOSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTggKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjRFNUM4M0M4NjcyMzExRTlCM0Y0Q0IyQjZEQTUwQzlCIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjRFNUM4M0M5NjcyMzExRTlCM0Y0Q0IyQjZEQTUwQzlCIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6NEU1QzgzQzY2NzIzMTFFOUIzRjRDQjJCNkRBNTBDOUIiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NEU1QzgzQzc2NzIzMTFFOUIzRjRDQjJCNkRBNTBDOUIiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz7Sk1otAAAAM0lEQVR42mJgGAU4QXl5+X9i1DFRaggTpS5hotQ7RBnQ2dnJSLYB+DRTJRZGARUAQIABAHK7DzeBU6NxAAAAAElFTkSuQmCC);
  transform: rotate(90deg);
  width: 16px;
  height: 16px;
  display: block;
  justify-self: flex-end;
  margin-left: auto;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container .gc-list-control-tree.hide-children .gc-list-control-tree-content {
  display: none;
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container .gc-list-control-tree.hide-children .gc-list-control-subitem {
  transform: rotate(0deg);
}
div[gcUIElement=gcSpread] .gc-list-control .gc-list-control-container .gc-list-control-item-ellipsis {
  width: 100%;
  white-space: nowrap;
  overflow-x: hidden;
  text-overflow: ellipsis;
}

.gc-slider {
  font-family: "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: "tnum";
  font-feature-settings: "tnum";
  position: relative;
  height: 12px;
  margin: 0 20px;
  padding: 20px 0;
  cursor: pointer;
  -ms-touch-action: none;
  touch-action: none;
}
.gc-slider.gc-slider-vertical {
  width: 12px;
  height: 100%;
  padding: 0 4px;
}
.gc-slider.gc-slider-vertical .gc-slider-rail {
  width: 4px;
  height: 100%;
}
.gc-slider.gc-slider-vertical .gc-slider-track {
  width: 4px;
}
.gc-slider.gc-slider-vertical .gc-slider-step {
  width: 4px;
  height: 100%;
}
.gc-slider.gc-slider-vertical .gc-slider-step .gc-slider-dot {
  top: auto;
  left: 0px;
  margin-bottom: -4px;
  box-sizing: content-box;
}
.gc-slider.gc-slider-vertical .gc-slider-handle {
  margin-bottom: -7px;
  margin-left: -7px;
}
.gc-slider.gc-slider-vertical .gc-slider-mark {
  top: 0;
  left: 12px;
  width: 18px;
  height: 100%;
}
.gc-slider.gc-slider-vertical .gc-slider-mark .gc-slider-mark-text {
  left: 4px;
  white-space: nowrap;
}
.gc-slider.gc-slider-with-marks {
  padding-bottom: 32px;
}
.gc-slider.gc-slider-disabled {
  cursor: not-allowed;
}
.gc-slider.gc-slider-disabled .gc-slider-track {
  background-color: rgba(0, 0, 0, 0.25);
}
.gc-slider.gc-slider-disabled .gc-slider-handle {
  background-color: #fff;
  border-color: rgba(0, 0, 0, 0.25);
  -webkit-box-shadow: none;
  box-shadow: none;
  cursor: not-allowed;
}
.gc-slider.gc-slider-disabled .gc-slider-step .gc-slider-dot {
  background-color: #fff;
  border-color: rgba(0, 0, 0, 0.25);
  -webkit-box-shadow: none;
  box-shadow: none;
  cursor: not-allowed;
  cursor: not-allowed;
}
.gc-slider.gc-slider-disabled .gc-slider-mark .gc-slider-mark-text {
  cursor: not-allowed;
}
.gc-slider .gc-slider-rail {
  position: absolute;
  width: 100%;
  height: 4px;
  background-color: #e8e8e8;
  border-radius: 2px;
  -webkit-transition: background-color 0.3s;
  transition: background-color 0.3s;
}
.gc-slider .gc-slider-track {
  position: absolute;
  height: 4px;
  background-color: #91d5ff;
  border-radius: 4px;
  -webkit-transition: background-color 0.3s ease;
  transition: background-color 0.3s ease;
}
.gc-slider .gc-slider-step {
  position: absolute;
  width: 100%;
  height: 4px;
  background: transparent;
}
.gc-slider .gc-slider-step .gc-slider-dot {
  position: absolute;
  top: -4px;
  width: 8px;
  height: 8px;
  margin-left: -4px;
  background-color: #fff;
  border: 2px solid #e8e8e8;
  border-radius: 50%;
  cursor: pointer;
}
.gc-slider .gc-slider-step .gc-slider-dot:first-child {
  margin-left: -4px;
}
.gc-slider .gc-slider-step .gc-slider-dot-active {
  border-color: #91d5ff;
}
.gc-slider .gc-slider-handle {
  position: absolute;
  width: 14px;
  height: 14px;
  margin-top: -7px;
  margin-left: -7px;
  background-color: #fff;
  border: solid 2px #91d5ff;
  border-radius: 50%;
  -webkit-box-shadow: 0;
  box-shadow: 0;
  box-sizing: content-box;
  cursor: pointer;
  z-index: 2;
  -webkit-transition: border-color 0.3s, -webkit-box-shadow 0.6s, -webkit-transform 0.3s cubic-bezier(0.18, 0.89, 0.32, 1.28);
  transition: border-color 0.3s, -webkit-box-shadow 0.6s, -webkit-transform 0.3s cubic-bezier(0.18, 0.89, 0.32, 1.28);
  transition: border-color 0.3s, box-shadow 0.6s, transform 0.3s cubic-bezier(0.18, 0.89, 0.32, 1.28);
  transition: border-color 0.3s, box-shadow 0.6s, transform 0.3s cubic-bezier(0.18, 0.89, 0.32, 1.28), -webkit-box-shadow 0.6s, -webkit-transform 0.3s cubic-bezier(0.18, 0.89, 0.32, 1.28);
}
.gc-slider .gc-slider-handle:focus {
  border-color: #3498db;
  outline: none;
  -webkit-box-shadow: 0 0 0 5px rgba(24, 144, 255, 0.2);
  box-shadow: 0 0 0 5px rgba(24, 144, 255, 0.2);
}
.gc-slider .gc-slider-mark {
  position: absolute;
  top: 30px;
  left: 0;
  width: 100%;
  font-size: 14px;
}
.gc-slider .gc-slider-mark .gc-slider-mark-text {
  position: absolute;
  display: inline-block;
  color: rgba(0, 0, 0, 0.45);
  text-align: center;
  cursor: pointer;
}
.gc-slider .gc-slider-mark .gc-slider-mark-text-active {
  color: rgba(0, 0, 0, 0.65);
}
.gc-slider .gc-slider-tooltip {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: "tnum";
  font-feature-settings: "tnum";
  position: absolute;
  z-index: 1060;
  display: block;
  max-width: 250px;
  visibility: visible;
}
.gc-slider .gc-slider-tooltip.gc-slider-tooltip-placement-top {
  padding-bottom: 8px;
}
.gc-slider .gc-slider-tooltip .gc-slider-tooltip-arrow {
  left: 50%;
  margin-left: -5px;
  position: absolute;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
  bottom: 3px;
  border-width: 5px 5px 0;
  border-top-color: rgba(0, 0, 0, 0.75);
}
.gc-slider .gc-slider-tooltip .gc-slider-tooltip-inner {
  min-width: 10px;
  min-height: 12px;
  padding: 6px 8px;
  color: #fff;
  text-align: left;
  text-decoration: none;
  word-wrap: break-word;
  background-color: rgba(0, 0, 0, 0.75);
  border-radius: 4px;
  -webkit-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.gc-slider-container {
  background-color: white;
  box-sizing: content-box;
}
.gc-slider-container.gc-slider-vertical {
  padding: 20px 0;
  padding-right: 10px;
}

div[gcUIElement=gcSpread] .gc-color-picker-outer-div {
  width: 100%;
  height: auto;
  overflow: hidden;
  background-color: white;
  font-family: "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
  font-size: 12px;
  box-sizing: border-box;
}
div[gcUIElement=gcSpread] .gc-color-picker-outer-div .gc-list-control-item {
  line-height: 0.5;
}
div[gcUIElement=gcSpread] .gc-color-picker-outer-div .gc-list-control-item:hover {
  background-color: transparent !important;
}
div[gcUIElement=gcSpread] .gc-color-picker-outer-div .gc-color-picker-group-row {
  display: inline-block;
  margin: 3px 0;
}
div[gcUIElement=gcSpread] .gc-color-picker-outer-div .gc-color-picker-group-row .gc-color-picker-color-cell {
  width: 12px;
  height: 12px;
  border: 1.5px solid transparent;
  display: inline-block;
  margin: 0 2px;
  box-sizing: content-box;
}
div[gcUIElement=gcSpread] .gc-color-picker-outer-div .gc-color-picker-group-row .gc-color-picker-color-cell:hover {
  border: 1.5px solid #f5ab5d;
}
div[gcUIElement=gcSpread] .gc-color-picker-outer-div .gc-color-picker-group-row .gc-color-picker-color-cell.selected {
  border: 1.5px solid #f58447;
}

div[gcUIElement=gcSpread] .gc-time-picker-outer-div {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: white;
  font-size: 12px;
  font-family: "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
}
div[gcUIElement=gcSpread] .gc-time-picker-outer-div .gc-list-control-item {
  line-height: 0.5;
}
div[gcUIElement=gcSpread] .gc-time-picker-outer-div .gc-list-control-item:hover {
  background-color: transparent !important;
}
div[gcUIElement=gcSpread] .gc-time-picker-outer-div .gc-list-control {
  height: 100%;
}
div[gcUIElement=gcSpread] .gc-time-picker-outer-div .gc-list-control .gc-list-control-container {
  height: 100%;
}
div[gcUIElement=gcSpread] .gc-time-picker-outer-div .gc-list-control .gc-list-control-container .gc-list-control-item {
  height: 100%;
}
div[gcUIElement=gcSpread] .gc-time-picker-outer-div .gc-time-picker-container {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}
div[gcUIElement=gcSpread] .gc-time-picker-outer-div .gc-time-picker-container .gc-time-picker-ul {
  margin: 0;
  padding: 0;
  width: 100%;
  list-style: none;
}
div[gcUIElement=gcSpread] .gc-time-picker-outer-div .gc-time-picker-container .gc-time-picker-ul .gc-time-picker-li {
  width: 100%;
  height: 24px;
  line-height: 24px;
  padding: 0 12px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  cursor: pointer;
}
div[gcUIElement=gcSpread] .gc-time-picker-outer-div .gc-time-picker-container .gc-time-picker-ul .gc-time-picker-li:hover {
  background-color: #e2f2ff !important;
}
div[gcUIElement=gcSpread] .gc-time-picker-outer-div .gc-time-picker-container .gc-time-picker-ul .gc-time-picker-li.selected {
  color: #fff;
  background-color: #3498db !important;
}

div[gcUIElement=gcSpread] .gc-base-spread-div {
  width: 100%;
  height: 100%;
  border: solid;
}

.gc-sjs-file-preview-dialog-container {
  background-color: #f0f0f0;
  border: 1px solid #707070;
  box-shadow: 2px 2px 10px #707070;
  font-family: "Segoe UI", Helvetica, Verdana, sans-serif;
  font-size: 12px;
  width: 600px;
  display: flex;
  flex-direction: column;
  user-select: none;
}
.gc-sjs-file-preview-dialog-container .gc-sjs-file-preview-dialog-title {
  display: flex;
  justify-content: space-between;
  background-color: white;
  align-items: center;
  flex: 0 0 30px;
}
.gc-sjs-file-preview-dialog-container .gc-sjs-file-preview-dialog-title .gc-sjs-file-preview-dialog-title-text {
  font-size: 14px;
  margin-left: 5px;
  overflow: hidden;
  text-wrap: nowrap;
  flex: 1;
}
.gc-sjs-file-preview-dialog-container .gc-sjs-file-preview-dialog-title .sjs-cancel-icon {
  flex: 0 0 50px;
}
.gc-sjs-file-preview-dialog-container .gc-sjs-file-preview-dialog-content {
  padding: 7px;
  flex: 1;
  overflow: auto;
}

.gc-panel-all {
  width: 100%;
  height: 100%;
  min-width: 300px;
  font-size: 12px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  position: relative;
  overflow: hidden;
  background-color: white;
  box-sizing: border-box;
}
.gc-panel-all.gc-panel-show-right .gc-panel-left {
  transform: translateX(-100%);
  transition: transform 0.5s ease;
}
.gc-panel-all.gc-panel-show-right .gc-panel-right {
  transform: translateX(0);
  transition: transform 0.5s ease;
}

.gc-panel-left {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: transform 0.5s ease;
  overflow: hidden;
}

.gc-panel-right {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  display: flex;
  overflow: hidden;
  /* 初始状态下将右侧面板平移出视口 */
  transform: translateX(100%);
  transition: transform 0.5s ease;
}

.gc-panel-base {
  flex: 1 1 0;
  display: flex;
  min-height: 0;
  transition: height 0.5s ease;
}

.gc-panel-addon {
  height: 0;
  overflow: hidden;
  transition: height 0.5s ease;
}

.gc-panel {
  position: relative;
  padding: 15px 5px 5px 15px;
  background-color: white;
  box-sizing: border-box;
  width: 100%;
  min-width: 300px;
  height: 100%;
  overflow-x: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.gc-panel .gc-panel-all-field-item,
.gc-panel .gc-panel-area-item {
  list-style-type: none;
  cursor: move;
}
.gc-panel .gc-panel-header {
  height: 27px;
}
.gc-panel .gc-panel-header h2 {
  margin: 0;
  color: #2c7a50;
  font-weight: 600;
  font-size: 20px;
}
.gc-panel .gc-panel-header p {
  margin: 0;
  color: black;
  font-size: 14px;
}

.gc-panel-checkbox {
  appearance: none;
  outline: none;
  display: inline-block;
  line-height: 22px;
  width: 14px;
  height: 14px;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTRweCIgaGVpZ2h0PSIxNHB4IiB2aWV3Qm94PSIwIDAgMTQgMTQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+Y2hlY2tfZGFyazwvdGl0bGU+CiAgICA8ZyBpZD0iY2hlY2tfZGFyayIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPHBhdGggZD0iTTEzLDEgTDEzLDEzIEwxLDEzIEwxLDEgTDEzLDEgWiBNMTIsMiBMMiwyIEwyLDEyIEwxMiwxMiBMMTIsMiBaIiBpZD0i5b2i54q257uT5ZCIIiBmaWxsPSIjNjk2OTY5Ij48L3BhdGg+CiAgICA8L2c+Cjwvc3ZnPg==);
  cursor: default;
}
.gc-panel-checkbox:checked {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTRweCIgaGVpZ2h0PSIxNHB4IiB2aWV3Qm94PSIwIDAgMTQgMTQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+Y2hlY2tfZGFyazwvdGl0bGU+CiAgICA8ZyBpZD0iY2hlY2tfZGFyayIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPHBvbHlnb24gaWQ9IlBhdGgtNCIgZmlsbD0iIzY5Njk2OSIgZmlsbC1ydWxlPSJub256ZXJvIiBwb2ludHM9IjExLjA2MDIyNjkgMyAxMiAzLjc1MDMxMDI5IDUuODIxNDMxODEgMTEgMiA3LjE0MDcwNzQzIDIuOTM5NzczMTQgNi4zOTAzOTcxMyA1LjgyMTQzMTgxIDkuMzAwNjA1OCI+PC9wb2x5Z29uPgogICAgICAgIDxwYXRoIGQ9Ik0xMywxIEwxMywxMyBMMSwxMyBMMSwxIEwxMywxIFogTTEyLDIgTDIsMiBMMiwxMiBMMTIsMTIgTDEyLDIgWiIgaWQ9IuW9oueKtue7k+WQiCIgZmlsbD0iIzY5Njk2OSI+PC9wYXRoPgogICAgPC9nPgo8L3N2Zz4=);
}

.gc-panel-collapse {
  appearance: none;
  outline: none;
  margin: 2px 3px 0px 4px;
  display: inline-block;
  line-height: 22px;
  width: 14px;
  height: 14px;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+dHJpYW5nbGVfbGlnaHQ8L3RpdGxlPgogICAgPGcgaWQ9InRyaWFuZ2xlX2xpZ2h0IiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8cG9seWdvbiBpZD0i6Lev5b6ELTQiIGZpbGw9IiM2OTY5NjkiIHBvaW50cz0iNCA2IDEyIDYgOCAxMCI+PC9wb2x5Z29uPgogICAgPC9nPgo8L3N2Zz4=);
}

.gc-panel-button {
  border: none;
  background-color: transparent;
  cursor: pointer;
  float: right;
  margin-right: 2px;
  outline: none;
  font-size: 14px;
}

.gc-panel-button:focus {
  outline: none;
}

/*for IE*/
.gc-panel-button::-moz-focus-inner {
  border-color: transparent;
}

/*for mozilla*/
.gc-panel-views {
  width: 100%;
  color: black;
}
.gc-panel-views > div > input {
  color: black;
}

.gc-panel-viewList {
  position: absolute;
  bottom: 10px;
  background-color: #fff;
  border: 1px solid #CBCBCB;
  border-radius: 5px;
  font-size: 14px;
  min-height: 21px;
  max-height: 210px;
  overflow-y: scroll;
}

.gc-panel-viewList > ul {
  width: calc(100% - 35px);
  padding-left: 15px;
  margin-top: 1px;
  margin-bottom: 1px;
}

.gc-panel-viewList > ul > li {
  margin-top: 1px;
  display: inline-block;
  width: 100%;
  line-height: 14px;
}

.gc-panel-viewList > ul > li > span:hover {
  background-color: rgb(159, 213, 183);
  cursor: default;
}

.gc-panel-button-delete {
  padding: 0;
}

.gc-panel-withoutViewList {
  height: 100%;
}

.gc-panel-withoutViewList[containViewList=true] {
  height: calc(100% - 30px);
}

.gc-panel-stack {
  width: 100%;
}
.gc-panel-stack .gc-panel-fields-section {
  width: 100%;
  height: calc(52% - 60px);
  color: black;
}
.gc-panel-stack .gc-panel-fields-section[containArea=false] {
  height: calc(100% - 30px);
}
.gc-panel-stack > span {
  color: black;
}
.gc-panel-stack .gc-panel-areas-section {
  width: 100%;
  height: 48%;
  color: black;
}
.gc-panel-stack .gc-panel-areas-section > span:first-child {
  display: inline-block;
  margin-bottom: 5px;
}
.gc-panel-stack .gc-panel-areas-section[containFields=false] {
  width: 100%;
  height: calc(100% - 40px);
}
.gc-panel-stack .gc-panel-areas-section span {
  white-space: normal;
}
.gc-panel-stack .gc-panel-field-area {
  height: calc(100% - 40px);
}
.gc-panel-stack .gc-panel-field-area > section:nth-of-type(1) {
  border-right: 1px solid #ababab;
  border-bottom: 1px solid #ababab;
}
.gc-panel-stack .gc-panel-field-area > section:nth-of-type(2) {
  border-bottom: 1px solid #ababab;
}
.gc-panel-stack .gc-panel-field-area > section:nth-of-type(2) .gc-panel-area-item-title,
.gc-panel-stack .gc-panel-field-area > section:nth-of-type(2) .gc-panel-area-item-content {
  margin-left: 3%;
}
.gc-panel-stack .gc-panel-field-area > section:nth-of-type(3) {
  border-right: 1px solid #ababab;
}
.gc-panel-stack .gc-panel-field-area > section:nth-of-type(4) .gc-panel-area-item-title,
.gc-panel-stack .gc-panel-field-area > section:nth-of-type(4) .gc-panel-area-item-content {
  margin-left: 3%;
}
.gc-panel-stack .gc-panel-area-section {
  width: 50%;
  height: 50%;
  box-sizing: border-box;
}
.gc-panel-stack .gc-panel-area-section-Filters {
  border-bottom-color: #CBCBCB;
  border-right-color: #CBCBCB;
}
.gc-panel-stack .gc-panel-area-section-Columns {
  border-bottom-color: #CBCBCB;
}
.gc-panel-stack .gc-panel-area-section-Rows {
  border-right-color: #CBCBCB;
}
.gc-panel-stack .gc-panel-defer-layout-update {
  width: 100%;
  height: 20px;
  line-height: 20px;
  display: inline-block;
  margin: 5px 0;
}
.gc-panel-stack .gc-panel-field-area-text {
  display: inline-block;
  height: 20px;
  line-height: 20px;
  margin: 5px 0;
}
.gc-panel-stack .gc-panel-area-section:nth-child(odd) {
  float: left;
}
.gc-panel-stack .gc-panel-area-section:nth-child(even) {
  float: right;
}

.gc-panel-flow {
  width: 100%;
}
.gc-panel-flow .gc-panel-fields-section {
  width: 49%;
  height: calc(100% - 50px);
  color: black;
  float: left;
  overflow-y: scroll;
}
.gc-panel-flow .gc-panel-fields-section[containArea=false] {
  width: 100%;
  height: calc(100% - 30px);
}
.gc-panel-flow .gc-panel-fields-section span {
  white-space: nowrap;
  text-overflow: ellipsis;
}
.gc-panel-flow > span {
  color: black;
}
.gc-panel-flow .gc-panel-areas-section {
  width: 49%;
  height: calc(100% - 50px);
  float: right;
  overflow-y: scroll;
  color: black;
}
.gc-panel-flow .gc-panel-areas-section[containFields=false] {
  width: 100%;
  height: calc(100% - 40px);
}
.gc-panel-flow .gc-panel-field-area {
  height: calc(100% - 40px);
}
.gc-panel-flow .gc-panel-area-section {
  width: 100%;
  height: 25%;
}
.gc-panel-flow .gc-panel-area-section-Filters {
  border-bottom-color: #CBCBCB;
  border-right-color: #CBCBCB;
}
.gc-panel-flow .gc-panel-area-section-Columns {
  border-bottom-color: #CBCBCB;
}
.gc-panel-flow .gc-panel-area-section-Rows {
  border-right-color: #CBCBCB;
}
.gc-panel-flow .gc-panel-defer-layout-update {
  width: 100%;
  height: 20px;
  line-height: 20px;
  display: inline-block;
  margin: 5px 0;
}
.gc-panel-flow .gc-panel-field-area-text {
  display: inline-block;
  height: 20px;
  line-height: 20px;
  margin: 5px 0;
}

.gc-panel-all-field {
  background-color: #FDFDFD;
  height: calc(100% - 30px);
  overflow-y: auto;
  border-bottom: 1px solid #CBCBCB;
  border-top: 1px solid #CBCBCB;
  box-sizing: border-box;
}
.gc-panel-all-field ul {
  margin: 0;
  padding: 0;
}
.gc-panel-all-field .gc-panel-all-field-item {
  height: 22px;
  line-height: 19px;
  font-size: 12px;
  color: black;
}
.gc-panel-all-field .gc-panel-all-field-item:hover {
  background-color: rgb(159, 213, 183);
}
.gc-panel-all-field .gc-panel-area-item-icon {
  width: 20px;
  height: 13px;
  display: inline-block;
  margin-top: 3px;
  float: right;
  cursor: auto;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+dHJpYW5nbGVfbGlnaHQ8L3RpdGxlPgogICAgPGcgaWQ9InRyaWFuZ2xlX2xpZ2h0IiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8cG9seWdvbiBpZD0i6Lev5b6ELTQiIGZpbGw9IiM2OTY5NjkiIHBvaW50cz0iNCA2IDEyIDYgOCAxMCI+PC9wb2x5Z29uPgogICAgPC9nPgo8L3N2Zz4=);
}

.gc-panel-all-field-checked {
  font-weight: 600;
}

.gc-panel-all-field-unchecked,
.gc-panel-all-field-checked {
  vertical-align: top;
  display: inline-block;
  width: calc(100% - 45px);
  overflow: hidden;
}

.gc-panel-all-field-item-filtered .gc-panel-all-field-unchecked,
.gc-panel-all-field-item-filtered .gc-panel-all-field-checked {
  width: calc(100% - 64px);
}

.gc-panel-area-item-title {
  height: 26px;
  line-height: 26px;
  font-size: 12px;
  color: black;
  width: 97%;
}

.gc-panel-area-item {
  height: 23px;
  line-height: 23px;
  text-indent: 3px;
  box-sizing: border-box;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
  white-space: nowrap;
  margin-bottom: 1px;
  font-size: 12px;
  border: 1px solid #CBCBCB;
  outline: none;
}
.gc-panel-area-item:hover {
  border: 1px solid #217346;
}
.gc-panel-area-item .gc-panel-area-item-label {
  display: inline-block;
  width: 75%;
  height: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  color: black;
}
.gc-panel-area-item .gc-panel-area-item-icon {
  width: 20px;
  height: 13px;
  display: inline-block;
  margin-top: 3px;
  float: right;
  cursor: default;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+dHJpYW5nbGVfbGlnaHQ8L3RpdGxlPgogICAgPGcgaWQ9InRyaWFuZ2xlX2xpZ2h0IiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8cG9seWdvbiBpZD0i6Lev5b6ELTQiIGZpbGw9IiM2OTY5NjkiIHBvaW50cz0iNCA2IDEyIDYgOCAxMCI+PC9wb2x5Z29uPgogICAgPC9nPgo8L3N2Zz4=);
}

.gc-panel-area-item-content {
  margin: 0;
  padding: 1px;
  height: calc(100% - 30px);
  width: 97%;
  background-color: #FDFDFD;
  overflow-y: auto;
  box-sizing: border-box;
}

.gc-panel-defer-layout-left-div {
  float: left;
  width: calc(100% - 60px);
}
.gc-panel-defer-layout-left-div label {
  display: inline-block;
  max-width: calc(100% - 20px);
  color: black;
  overflow-x: hidden;
  text-overflow: ellipsis;
  vertical-align: middle;
}
.gc-panel-defer-layout-left-div input {
  vertical-align: middle;
}

.gc-panel-defer-layout-right-button {
  float: right;
  border: 1px solid #CBCBCB;
  background-color: white;
  color: black;
  width: 60px;
}
.gc-panel-defer-layout-right-button:disabled {
  color: #c6bfbe;
}

.gc-panel-bottomBorderStyle {
  border-bottom: 2px solid green;
}

.gc-panel-topBorderStyle {
  border-top: 2px solid green;
}

.gc-panel-all-field-filter {
  width: 18px;
  height: 13px;
  margin-top: 3px;
  display: inline-block;
  float: right;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+ZmlsdGVyX2xpZ2h0PC90aXRsZT4KICAgIDxnIGlkPSJmaWx0ZXJfbGlnaHQiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwYXRoIGQ9Ik0xMi45MzI0NzkyLDMgQzEzLjIwODYyMTYsMyAxMy40MzI0NzkyLDMuMjIzODU3NjMgMTMuNDMyNDc5MiwzLjUgQzEzLjQzMjQ3OTIsMy42MTY5NjgyNSAxMy4zOTE0NzExLDMuNzMwMjM0NyAxMy4zMTY1ODk4LDMuODIwMDkyMiBMOS4yLDguNzYgTDkuMiwxMi44IEw2LjgsMTQgTDYuOCw4Ljc2IEwyLjY4MzQxMDE3LDMuODIwMDkyMiBDMi41MDY2MjgxMywzLjYwNzk1Mzc1IDIuNTM1MjkwMTYsMy4yOTI2NzE0IDIuNzQ3NDI4NjEsMy4xMTU4ODkzNiBDMi44MzcyODYxMSwzLjA0MTAwODExIDIuOTUwNTUyNTUsMyAzLjA2NzUyMDgxLDMgTDEyLjkzMjQ3OTIsMyBaIiBpZD0iQ29tYmluZWQtU2hhcGUiIGZpbGw9IiM2OTY5NjkiPjwvcGF0aD4KICAgIDwvZz4KPC9zdmc+);
  background-repeat: no-repeat;
}

.gc-panel-area-item-Filters {
  width: 15px;
  height: 14px;
  display: inline-block;
  background-repeat: no-repeat;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+ZmlsdGVyX2xpZ2h0PC90aXRsZT4KICAgIDxnIGlkPSJmaWx0ZXJfbGlnaHQiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwYXRoIGQ9Ik0xMi45MzI0NzkyLDMgQzEzLjIwODYyMTYsMyAxMy40MzI0NzkyLDMuMjIzODU3NjMgMTMuNDMyNDc5MiwzLjUgQzEzLjQzMjQ3OTIsMy42MTY5NjgyNSAxMy4zOTE0NzExLDMuNzMwMjM0NyAxMy4zMTY1ODk4LDMuODIwMDkyMiBMOS4yLDguNzYgTDkuMiwxMi44IEw2LjgsMTQgTDYuOCw4Ljc2IEwyLjY4MzQxMDE3LDMuODIwMDkyMiBDMi41MDY2MjgxMywzLjYwNzk1Mzc1IDIuNTM1MjkwMTYsMy4yOTI2NzE0IDIuNzQ3NDI4NjEsMy4xMTU4ODkzNiBDMi44MzcyODYxMSwzLjA0MTAwODExIDIuOTUwNTUyNTUsMyAzLjA2NzUyMDgxLDMgTDEyLjkzMjQ3OTIsMyBaIiBpZD0iQ29tYmluZWQtU2hhcGUiIGZpbGw9IiM2OTY5NjkiPjwvcGF0aD4KICAgIDwvZz4KPC9zdmc+);
}

.gc-panel-area-item-Rows {
  width: 15px;
  height: 14px;
  display: inline-block;
  background-repeat: no-repeat;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+Y2F0ZWdvcnlfbGlnaHQ8L3RpdGxlPgogICAgPGcgaWQ9ImNhdGVnb3J5X2xpZ2h0IiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8cGF0aCBkPSJNMTMsMTIgTDEzLDEzIEwzLDEzIEwzLDEyIEwxMywxMiBaIE0xMywxMCBMMTMsMTEgTDMsMTEgTDMsMTAgTDEzLDEwIFogTTEzLDggTDEzLDkgTDMsOSBMMyw4IEwxMyw4IFogTTEzLDQgTDEzLDcgTDMsNyBMMyw0IEwxMyw0IFoiIGlkPSLlvaLnirbnu5PlkIgiIGZpbGw9IiM2OTY5NjkiPjwvcGF0aD4KICAgIDwvZz4KPC9zdmc+);
}

.gc-panel-area-item-Columns {
  width: 15px;
  height: 14px;
  display: inline-block;
  background-repeat: no-repeat;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+c2VyaWVzX2xpZ2h0PC90aXRsZT4KICAgIDxnIGlkPSJzZXJpZXNfbGlnaHQiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwYXRoIGQ9Ik02LDMgTDYsMTMgTDMsMTMgTDMsMyBMNiwzIFogTTgsMyBMOCwxMyBMNywxMyBMNywzIEw4LDMgWiBNMTAsMyBMMTAsMTMgTDksMTMgTDksMyBMMTAsMyBaIE0xMiwzIEwxMiwxMyBMMTEsMTMgTDExLDMgTDEyLDMgWiIgaWQ9IuW9oueKtue7k+WQiCIgZmlsbD0iIzY5Njk2OSI+PC9wYXRoPgogICAgPC9nPgo8L3N2Zz4=);
}

.gc-panel-area-item-Values {
  width: 15px;
  height: 14px;
  display: inline-block;
  background-repeat: no-repeat;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+dmFsdWVfbGlnaHQ8L3RpdGxlPgogICAgPGcgaWQ9InZhbHVlX2xpZ2h0IiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8cGF0aCBkPSJNMy40MzYyOTYyMiwzIEwxMywzIEwxMywzIEwxMyw2LjEyNSBMMTIuMjMwNzY5Miw2LjEyNSBMMTEuNDYxNTM4NSw0LjAxODQ3MzMxIEw1LjkwMzIzODkzLDQuMDE4NDczMzEgTDguOTY2Nzk2ODgsNy42ODMwMjQwOSBMNS42MjEwNTMwNiwxMS43MjI3Mzc2IEwxMS40NjE1Mzg1LDExLjcyMjczNzYgTDEyLjIzMDc2OTIsOS44NzUgTDEzLDkuODc1IEwxMywxMyBMMy40MzYyOTYyMiwxMyBDMy4zMjU4MzkyNywxMyAzLjIzNjI5NjIyLDEyLjkxMDQ1NjkgMy4yMzYyOTYyMiwxMi44IEMzLjIzNjI5NjIyLDEyLjc1MjA4MzggMy4yNTM0OTkxMiwxMi43MDU3NiAzLjI4NDc3NDQ4LDEyLjY2OTQ1ODIgTDcuMzA3NjkyMzIsOCBMNy4zMDc2OTIzMiw4IEwzLjI4NDc3NDQ4LDMuMzMwNTQxODEgQzMuMjEyNjc4MjMsMy4yNDY4NTg2NiAzLjIyMjA3MTI3LDMuMTIwNTc0NTEgMy4zMDU3NTQ0MSwzLjA0ODQ3ODI2IEMzLjM0MjA1NjE3LDMuMDE3MjAyOSAzLjM4ODM3OTk4LDMgMy40MzYyOTYyMiwzIFoiIGlkPSJTaGFwZSIgZmlsbD0iIzY5Njk2OSIgZmlsbC1ydWxlPSJub256ZXJvIj48L3BhdGg+CiAgICA8L2c+Cjwvc3ZnPg==);
}

.gc-panel-all-field-filter:hover {
  cursor: auto;
}

.gc-panel-views #gc-panel-selected-view {
  background-color: #FDFDFD;
}

.gc-ai-icon {
  width: 16px;
  height: 16px;
  cursor: pointer;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  mask-position: center;
  mask-repeat: no-repeat;
  mask-size: contain;
  transition: transform 0.2s ease;
}
.gc-ai-icon:hover {
  transform: scale(1.1);
}
.gc-ai-icon.gc-ai-ai {
  background-image: url(data:image/svg+xml;charset=utf-8;base64,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);
  cursor: default;
}
.gc-ai-icon.gc-ai-close {
  mask-image: url(data:image/svg+xml;charset=utf-8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+Y2xvc2U8L3RpdGxlPgogICAgPGcgaWQ9ImNsb3NlIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8cG9seWdvbiBpZD0i6Lev5b6EIiBmaWxsPSIjMDAwMDAwIiBmaWxsLXJ1bGU9Im5vbnplcm8iIHBvaW50cz0iMTAgMi44MDU3MTQyOSA5LjE5NDI4NTcxIDIgNiA1LjE5NDI4NTcxIDIuODA1NzE0MjkgMiAyIDIuODA1NzE0MjkgNS4xOTQyODU3MSA2IDIgOS4xOTQyODU3MSAyLjgwNTcxNDI5IDEwIDYgNi44MDU3MTQyOSA5LjE5NDI4NTcxIDEwIDEwIDkuMTk0Mjg1NzEgNi44MDU3MTQyOSA2Ij48L3BvbHlnb24+CiAgICA8L2c+Cjwvc3ZnPg==);
  background-color: black;
  margin-right: 10px;
  width: 12px;
  height: 12px;
}
.gc-ai-icon.gc-ai-reset {
  mask-image: url(data:image/svg+xml;charset=utf-8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+cmVzZXQ8L3RpdGxlPgogICAgPGcgaWQ9InJlc2V0IiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSgyLjAwMDAwMCwgMS4wMDAwMDApIiBmaWxsPSIjMDAwMDAwIiBmaWxsLXJ1bGU9Im5vbnplcm8iIGlkPSLot6/lvoQiPgogICAgICAgICAgICA8cGF0aCBkPSJNMS41NTU1NTU1NSw3Ljc3Nzc3Nzc4IEwwLDcuNzc3Nzc3NzggQzAsMTEuMTUwMjIyMiAyLjg0OSwxNCA2LjIyMjIyMjIyLDE0IEM5LjU5NTQ0NDQzLDE0IDEyLjQ0NDQ0NDQsMTEuMTUwMjIyMiAxMi40NDQ0NDQ0LDcuNzc3Nzc3NzggQzEyLjQ0NDQ0NDQsNC40MDUzMzMzNSA5LjU5NTQ0NDQzLDEuNTU1NTU1NTcgNi4yMjIyMjIyMiwxLjU1NTU1NTU3IEw2LjIyMjIyMjIyLDAgTDMuMTMwNTU1NTUsMi4zMzMzMzMzMyBMNi4yMjIyMjIyMiw0LjY2NjY2NjY3IEw2LjIyMjIyMjIyLDMuMTExMTExMTIgQzguNzUxNTU1NTUsMy4xMTExMTExMiAxMC44ODg4ODg5LDUuMjQ4NDQ0NDUgMTAuODg4ODg4OSw3Ljc3Nzc3Nzc4IEMxMC44ODg4ODg5LDEwLjMwNzExMTEgOC43NTE1NTU1NSwxMi40NDQ0NDQ1IDYuMjIyMjIyMjIsMTIuNDQ0NDQ0NSBDMy42OTI4ODg4OCwxMi40NDQ0NDQ1IDEuNTU1NTU1NTUsMTAuMzA3MTExMSAxLjU1NTU1NTU1LDcuNzc3Nzc3NzggWiI+PC9wYXRoPgogICAgICAgICAgICA8cGF0aCBkPSJNOCw4IEM4LDYuODg3MzMzMzMgNy4xMTQsNiA2LDYgQzQuODg2LDYgNCw2Ljg4NzMzMzMzIDQsOCBDNCw5LjExMjY2NjY3IDQuODg2LDEwIDYsMTAgQzcuMTE0LDEwIDgsOS4xMTI2NjY2NyA4LDggWiI+PC9wYXRoPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+);
  background-color: grey;
  width: 20px;
  height: 20px;
}
.gc-ai-icon.gc-ai-reset.gc-ai-icon-disabled {
  background-color: lightgrey;
  cursor: default;
}
.gc-ai-icon.gc-ai-send {
  mask-image: url(data:image/svg+xml;charset=utf-8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+c2VuZDwvdGl0bGU+CiAgICA8ZyBpZD0ic2VuZCIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPHBhdGggZD0iTTYuNDE5MTgyNjQsNi4xMDE3MTQwNCBMMi41NTcyMDQ1Miw2Ljc0NTcxODkgQzIuNDY2ODM2MzUsNi43NjA4MDA2OSAyLjM5MTM4NDkyLDYuODIyOTYwMTYgMi4zNTkyODU4Myw2LjkwODc3MTA4IEwxLjAyNzE4MDI3LDEwLjQ3NTkyMjEgQzAuOTAwMDIwMDc3LDEwLjgwNDA3NzUgMS4yNDMwNDQ5NSwxMS4xMTY4NTA1IDEuNTU3ODY4OTcsMTAuOTU5NDM4NSBMMTAuNzg3MjM3OCw2LjM0NDc1NDA5IEMxMC45MTc2MjA5LDYuMjc5NjQ5ODUgMTEsNi4xNDY0Mzc0MSAxMSw2LjAwMDcwMzczIEMxMSw1Ljg1NDk3MDA1IDEwLjkxNzYyMDksNS43MjE3NTc2MiAxMC43ODcyMzc4LDUuNjU2NjUzMzggTDEuNTU3ODY4OTcsMS4wNDE5Njg5NiBDMS4yNDMwNDQ5NSwwLjg4NDU1Njk0NyAwLjkwMDAyMDA3NywxLjE5NzMzMDAxIDEuMDI3MTgwMjcsMS41MjU0ODUzMyBMMi4zNTk3OTg1OCw1LjA5MjYzNjM5IEMyLjM5MTc0OTE0LDUuMTc4NjQ2NDEgMi40NjcyMjg0Niw1LjI0MTAyNDM5IDIuNTU3NzE3MjYsNS4yNTYyMDEzMSBMNi40MTk2OTUzNyw1Ljg5OTY5MzQyIEM2LjQ2ODc1NzM4LDUuOTA4Mjg4MjEgNi41MDQ1NDg3MSw1Ljk1MDg5NDU4IDYuNTA0NTQ4NzEsNi4wMDA3MDM3MyBDNi41MDQ1NDg3MSw2LjA1MDUxMjg4IDYuNDY4NzU3MzgsNi4wOTMxMTkyNiA2LjQxOTY5NTM3LDYuMTAxNzE0MDQgTDYuNDE5MTgyNjQsNi4xMDE3MTQwNCBaIiBpZD0i6Lev5b6EIiBmaWxsPSIjMDAwMDAwIiBmaWxsLXJ1bGU9Im5vbnplcm8iPjwvcGF0aD4KICAgIDwvZz4KPC9zdmc+);
  background-color: white;
  width: 14px;
  height: 14px;
}
.gc-ai-icon.gc-ai-stop {
  mask-image: url(data:image/svg+xml;charset=utf-8;base64,PHN2ZyB0PSIxNzQ0MDk3NDg5MTMwIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjE2NTciIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIj48cGF0aCBkPSJNODUuMzMzMzMzIDg1LjMzMzMzM201My4zMzMzMzQgMGw3NDYuNjY2NjY2IDBxNTMuMzMzMzMzIDAgNTMuMzMzMzM0IDUzLjMzMzMzNGwwIDc0Ni42NjY2NjZxMCA1My4zMzMzMzMtNTMuMzMzMzM0IDUzLjMzMzMzNGwtNzQ2LjY2NjY2NiAwcS01My4zMzMzMzMgMC01My4zMzMzMzQtNTMuMzMzMzM0bDAtNzQ2LjY2NjY2NnEwLTUzLjMzMzMzMyA1My4zMzMzMzQtNTMuMzMzMzM0WiIgZmlsbD0iIzVDNUM2NiIgcC1pZD0iMTY1OCI+PC9wYXRoPjwvc3ZnPg==);
  background-color: white;
  width: 10px;
  height: 10px;
}
.gc-ai-icon.gc-ai-generate {
  background-image: url(data:image/svg+xml;charset=utf-8;base64,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);
}
.gc-ai-icon.gc-ai-analyze {
  background-image: url(data:image/svg+xml;charset=utf-8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjVweCIgaGVpZ2h0PSIyNXB4IiB2aWV3Qm94PSIwIDAgMjUgMjUiIHZlcnNpb249IjEuMSI+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9ImxpbmVhcjAiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIiB4MT0iMC40MzI2NDQiIHkxPSIwLjEyOTk3MiIgeDI9IjAuNjYyMjQyIiB5Mj0iMSIgZ3JhZGllbnRUcmFuc2Zvcm09Im1hdHJpeCgxMi43ODkwNjIsMCwwLDIwLjU0Njg3NSw2LjI1LDIuMDgyMDMxKSI+CjxzdG9wIG9mZnNldD0iMCIgc3R5bGU9InN0b3AtY29sb3I6cmdiKDcxLjM3MjU0OSUsMTQuOTAxOTYxJSwxMDAlKTtzdG9wLW9wYWNpdHk6MTsiLz4KPHN0b3Agb2Zmc2V0PSIxIiBzdHlsZT0ic3RvcC1jb2xvcjpyZ2IoMTQuNTA5ODA0JSwxMy43MjU0OSUsMTAwJSk7c3RvcC1vcGFjaXR5OjE7Ii8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPGcgaWQ9InN1cmZhY2UxIj4KPHBhdGggc3R5bGU9IiBzdHJva2U6bm9uZTtmaWxsLXJ1bGU6ZXZlbm9kZDtmaWxsOnVybCgjbGluZWFyMCk7IiBkPSJNIDYuMjUgMjIuNjI4OTA2IEwgMTMuMTY3OTY5IDIyLjYyODkwNiBMIDE5LjAzOTA2MiAyLjA4MjAzMSBMIDEyLjEyMTA5NCAyLjA4MjAzMSBaIE0gNi4yNSAyMi42Mjg5MDYgIi8+CjwvZz4KPC9zdmc+Cg==);
}
.gc-ai-icon.gc-ai-refresh {
  mask-image: url(data:image/svg+xml;charset=utf-8;base64,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);
  background-color: black;
}
.gc-ai-icon.gc-ai-refresh.gc-ai-loading {
  animation: spin 1s linear infinite;
}
.gc-ai-icon.gc-ai-arrow-up {
  mask-image: url(data:image/svg+xml;charset=utf-8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iOHB4IiBoZWlnaHQ9IjhweCIgdmlld0JveD0iMCAwIDggOCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDx0aXRsZT5hcnJvdy11cDwvdGl0bGU+CiAgICA8ZyBpZD0iYXJyb3ctdXAiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwb2x5Z29uIGlkPSLot6/lvoTlpIfku70tNSIgZmlsbD0iIzAwMDAwMCIgcG9pbnRzPSIwIDYgOCA2IDQgMiI+PC9wb2x5Z29uPgogICAgPC9nPgo8L3N2Zz4=);
  background-color: black;
  width: 8px;
  height: 8px;
}
.gc-ai-icon.gc-ai-back {
  mask-image: url(data:image/svg+xml;charset=utf-8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+YmFjazwvdGl0bGU+CiAgICA8ZyBpZD0iYmFjayIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9ImZvcm11bGFFZGl0b3JQYW5lbEJhY2siIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAuMDAwMDAwLCAyLjAwMDAwMCkiIGZpbGw9IiMwMDgwMDAiIGZpbGwtcnVsZT0ibm9uemVybyI+CiAgICAgICAgICAgIDxwb2x5Z29uIGlkPSLlvaLnirbnu5PlkIgiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDguMDAwMDAwLCA1LjcxNDI4Nikgc2NhbGUoLTEsIDEpIHRyYW5zbGF0ZSgtOC4wMDAwMDAsIC01LjcxNDI4NikgIiBwb2ludHM9IjEwLjE4NTAwOTEgMCAxNiA1LjcxNDI4NTcxIDEwLjE4NTAwOTEgMTEuNDI4NTcxNCA4LjcwNTg4MjM0IDExLjQyODU3MTQgMTQuMDczNjEzNCA2LjIyNzU0NDkxIDAgNi4yMjc1NDQ5MSAwIDUuMjAxMDI2NTEgMTQuMDczNjEzNCA1LjIwMTAyNjUxIDguNzA1ODgyMzQgMCI+PC9wb2x5Z29uPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+);
  background-color: #2c7a50;
  width: 20px;
  height: 20px;
}
.gc-ai-icon.gc-ai-copy {
  mask-image: url(data:image/svg+xml;charset=utf-8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+Y29weTwvdGl0bGU+CiAgICA8ZyBpZD0iY29weSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPHBhdGggZD0iTTEwLjc3NzU4ODYsNC41IEwxNC4yNSw4LjA2Nzk2OTQxIEwxNC4yNSwxNSBMNS41LDE1IEw1LjUsNC41IEwxMC43Nzc1ODg2LDQuNSBaIE05Ljg3NSw1LjM3NSBMNi4zNzUsNS4zNzUgTDYuMzc1LDE0LjEyNSBMMTMuMzc1LDE0LjEyNSBMMTMuMzc1LDguODc1IEw5Ljg3NSw4Ljg3NSBMOS44NzUsNS4zNzUgWiBNNy4yNzc1ODg1NiwxIEw5LjgzMjI4NTM2LDMuNjI1IEw4LjYxMTMwNjY3LDMuNjI1IEw3LjI1LDIuMjI2MjMxMjcgTDcuMjUsMy42MjUgTDYuMzc1LDMuNjI1IEw2LjM3NSwxLjg3NSBMMi44NzUsMS44NzUgTDIuODc1LDEwLjYyNSBMNC42MjUsMTAuNjI1IEw0LjYyNSwxMS41IEwyLDExLjUgTDIsMSBMNy4yNzc1ODg1NiwxIFogTTEyLjUsMTAuNjI1IEwxMi41LDExLjUgTDcuMjUsMTEuNSBMNy4yNSwxMC42MjUgTDEyLjUsMTAuNjI1IFogTTksOCBMOSw4Ljg3NSBMNy4yNSw4Ljg3NSBMNy4yNSw4IEw5LDggWiBNNC42MjUsNy4xMjUgTDQuNjI1LDggTDMuNzUsOCBMMy43NSw3LjEyNSBMNC42MjUsNy4xMjUgWiBNMTAuNzUsNS43MjYyMzEyNyBMMTAuNzUsOCBMMTIuOTYyODcyMyw4IEwxMC43NSw1LjcyNjIzMTI3IFogTTQuNjI1LDQuNSBMNC42MjUsNS4zNzUgTDMuNzUsNS4zNzUgTDMuNzUsNC41IEw0LjYyNSw0LjUgWiIgaWQ9IuW9oueKtue7k+WQiCIgZmlsbD0iIzAwMDAwMCIgZmlsbC1ydWxlPSJub256ZXJvIj48L3BhdGg+CiAgICA8L2c+Cjwvc3ZnPg==);
  background-color: grey;
  width: 17px;
  height: 17px;
}
.gc-ai-icon.gc-ai-copied {
  mask-image: url(data:image/svg+xml;charset=utf-8;base64,PHN2ZyB0PSIxNzQzMDY1MDA2MTU0IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjE0NTEiIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIj48cGF0aCBkPSJNMzgwLjM0Mjg1NyA4MDEuNjQ1NzE0YTUzLjM5NDI4NiA1My4zOTQyODYgMCAwIDEtMzYuNTcxNDI4LTE2LjA5MTQyOGwtMjE4LjMzMTQyOS0yMTcuMjM0Mjg2YTU1LjU4ODU3MSA1NS41ODg1NzEgMCAwIDEgMC03Ny4xNjU3MTQgNTQuMTI1NzE0IDU0LjEyNTcxNCAwIDAgMSA3Ni44IDBsMTc4LjEwMjg1NyAxNzkuMkw4MzUuMjkxNDI5IDI3Mi4wOTE0MjlhNTMuMzk0Mjg2IDUzLjM5NDI4NiAwIDAgMSA3Ni40MzQyODUgMCA1NC4xMjU3MTQgNTQuMTI1NzE0IDAgMCAxIDAgNzYuOEw0MTguNzQyODU3IDc4NS41NTQyODZhNTQuNDkxNDI5IDU0LjQ5MTQyOSAwIDAgMS0zOC40IDE2LjA5MTQyOHoiIHAtaWQ9IjE0NTIiPjwvcGF0aD48L3N2Zz4=);
  background-color: grey;
  width: 17px;
  height: 17px;
  cursor: default;
}
.gc-ai-icon.gc-ai-explain {
  background-image: url(data:image/svg+xml;base64,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);
}

.gc-ai-icon-disabled:hover {
  transform: scale(1);
}
.gc-ai-icon-disabled .gc-ai-icon:hover {
  transform: scale(1);
}

/* 定义旋转动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.gc-ai-icon-background {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #2c7a50;
}
.gc-ai-icon-background.gc-ai-icon-disabled {
  background-color: lightgrey;
  cursor: default;
}
.gc-ai-icon-background.gc-ai-icon-disabled .gc-ai-icon {
  background-color: white;
  cursor: default;
}

.gc-ai-text-container {
  position: relative;
  min-height: 64px;
  border: 1px solid #ddd;
  padding: 5px;
  box-sizing: border-box;
  font-size: 12px;
  cursor: text;
  background-color: #FDFDFD;
  color: black;
  width: 100%;
}

.gc-ai-text {
  user-select: none;
  outline: none;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 48px;
  max-height: 120px;
  white-space: normal;
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
}
.gc-ai-text::-webkit-scrollbar {
  width: 4px;
  height: 6px;
  cursor: pointer;
}
.gc-ai-text::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: rgb(203, 203, 203);
  cursor: pointer;
}
.gc-ai-text::-webkit-scrollbar-thumb:hover {
  background-color: rgb(153, 153, 153);
}

.gc-ai-text:empty::before {
  content: attr(placeholder);
  color: grey;
}

.gc-ai-text-container-icons {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 5px;
  padding: 5px 10px;
}

.gc-ai-separator {
  width: 1px;
  height: 24px;
  background: #ddd;
  margin: 0 7px;
}

.gc-ai-loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(241, 241, 241, 0.5);
  pointer-events: auto;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.gc-ai-loading-mask .gc-ai-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #fff;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.gc-ai-error-overlay {
  position: fixed;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  z-index: 9999;
}

.gc-ai-error-dialog {
  position: absolute;
  background-color: rgb(255, 255, 255);
  padding: 12px;
  border-radius: 8px;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 4px 8px;
  max-width: 400px;
  overflow-wrap: break-word;
  text-align: center;
  font-size: 14.6667px;
  font-family: Consolas, Menlo, Monaco, "Courier New", monospace;
}

.gc-ai-text-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 44px;
  white-space: nowrap;
  overflow: hidden;
}

.gc-ai-text-header-left {
  display: flex;
  align-items: center;
  margin-left: 5px;
  gap: 5px;
}

.gc-ai-text-header-left img {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.gc-ai-text-header-title {
  font-size: 13px;
  font-weight: bold;
  font-family: "PingFangSC-Semibold", "PingFang SC Semibold", "PingFang SC", sans-serif;
}

.gc-ai-text-header-right img {
  width: 20px;
  height: 20px;
  cursor: pointer;
}

.gc-ai-hide {
  display: none;
}

.gc-ai-pt-capsule {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 65px;
  height: 25px;
  border-radius: 25px;
  background-color: white;
  position: absolute;
  top: 16px;
  right: 10px;
  overflow: hidden;
}

.gc-ai-pt-capsule::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 25px;
  padding: 2px;
  /* 边框宽度 */
  background: linear-gradient(to right, #bc34fe, #272dfe);
  mask: linear-gradient(white 0 0) content-box, linear-gradient(white 0 0);
  -webkit-mask: linear-gradient(white 0 0) content-box, linear-gradient(white 0 0);
  -webkit-mask-composite: destination-out;
  mask-composite: exclude;
}

.gc-ai-pt-button {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: sans-serif;
  font-size: 18px;
  font-weight: bold;
  color: #333;
  cursor: pointer;
  z-index: 1;
  /* 确保按钮在伪元素之上 */
  position: relative;
  /* 为倾斜分隔线定位 */
}

.gc-ai-pt-button:not(:last-child)::after {
  content: "";
  position: absolute;
  right: -2px;
  top: 1px;
  bottom: 1px;
  width: 1px;
  background-color: #6535fe;
  /* 分隔线颜色 */
  transform: skewX(-15deg);
  /* 倾斜分隔线 */
  transform-origin: center;
  /* 确保倾斜以中心为基准 */
}

.gc-ai-pt-gen {
  padding: 5px 10px;
  color: black;
}

.gc-ai-pt-gen-spinner-line {
  width: 100%;
  height: 1px;
  background: #CBCBCB;
}

.gc-ai-pt-sug-container {
  position: relative;
  margin-top: 7px;
  border: 1px solid #ddd;
  padding: 5px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  user-select: none;
  background-color: #FDFDFD;
  border-radius: 5px;
  color: black;
}

.gc-ai-pt-sug-dropdown {
  display: none;
  position: absolute;
  bottom: 35px;
  width: 96%;
  left: 2%;
  border: 1px solid #ddd;
  background: #fff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  z-index: 9999;
  border-radius: 5px;
  background-color: #FDFDFD;
}

.gc-ai-pt-sug-dropdown-header {
  display: flex;
  justify-content: flex-end;
  padding: 5px;
}

.gc-ai-pt-sug-dropdown-header img {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.gc-ai-pt-sug-item-container {
  color: black;
}

.gc-ai-pt-sug-item {
  padding: 6px 3px;
  border: 1px solid #ababab;
  cursor: pointer;
  transition: background 0.3s;
  margin: 6px 4px;
  border-radius: 5px;
  background-color: #FDFDFD;
}

.gc-ai-pt-sug-item:hover {
  background: white;
}

.gc-ai-pt-ana {
  width: 100%;
  margin: 10px;
  display: flex;
  flex-direction: column;
  color: black;
}
.gc-ai-pt-ana .gc-ai-text-container {
  color: black;
  min-height: 110px;
}
.gc-ai-pt-ana .gc-ai-text-container .gc-ai-text-container-icons {
  margin-top: 4px;
  padding: 0px 10px;
}
.gc-ai-pt-ana .gc-ai-text-container .gc-ai-text {
  min-height: 75px;
  max-height: 75px;
}

.gc-ai-pt-analyze-container {
  display: flex;
  flex-grow: 1;
  flex-direction: column;
  position: relative;
  border: 1px solid #ddd;
  padding: 5px;
  box-sizing: border-box;
  font-size: 12px;
  cursor: text;
  background-color: #FDFDFD;
  color: black;
  border-radius: 10px;
  margin-top: 10px;
  overflow-y: auto;
}
.gc-ai-pt-analyze-container .gc-ai-text {
  min-height: 200px;
  max-height: none;
  height: calc(100% - 40px);
  user-select: auto;
}
.gc-ai-pt-analyze-container .gc-ai-icon-background {
  width: 20px;
  height: 20px;
  background-color: #FDFDFD;
  border: 1px solid grey;
}

.gc-ai-pt-analyze-buttons {
  margin: 10px 5px;
  width: calc(100% - 18px);
}

.gc-ai-formula-editor-container {
  height: 100%;
  transition: height 0.3s ease;
  position: relative;
}

.gc-ai-open-button {
  cursor: pointer;
  width: 45px;
  height: 45px;
  position: absolute;
  bottom: 32px;
  right: 32px;
  z-index: 1000;
  background-image: url(data:image/svg+xml;base64,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);
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  display: none;
}

.gc-ai-assistant-area {
  height: 0;
  position: relative;
  overflow: hidden;
  padding: 2px;
  bottom: 0px;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-top: 1px solid rgb(203, 203, 203);
  transition: height 0.3s ease;
  background-clip: content-box;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
}
.gc-ai-assistant-area .gc-ai-text-container {
  background-clip: content-box;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  color: inherit;
  font-size: inherit;
  background-color: inherit;
}
.gc-ai-assistant-area .gc-ai-text {
  height: 100px;
  min-height: 100px;
  max-height: 100px;
}

.gc-ai-resize-handle {
  cursor: grab;
  width: 12px;
  height: 6px;
  flex-shrink: 0;
  background-image: url(data:image/svg+xml;base64,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);
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.gc-ai-formula-generator-container {
  width: 100%;
  height: 100%;
}
.gc-ai-formula-generator-container .gc-ai-formula-generator-button-container {
  width: 20px;
  height: 20px;
}
.gc-ai-formula-generator-container .gc-ai-refresh {
  width: 20px;
  height: 20px;
  background-color: rgb(44, 122, 80);
  position: absolute;
}
.gc-ai-formula-generator-container .gc-ai-icon-background {
  width: 20px;
  height: 20px;
  position: absolute;
  background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSI+CjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIwIiBoZWlnaHQ9IjAiIGZpbGw9InJnYmEoMjQ5LCAyNDksIDI0OSwgMSkiIC8+CjxwYXRoIGQ9Ik0wIDEwQzAgMTUuNTIyOCA0LjQ3NzE1IDIwIDEwIDIwQzE1LjUyMjggMjAgMjAgMTUuNTIyOCAyMCAxMEMyMCA0LjQ3NzE1IDE1LjUyMjggMCAxMCAwQzQuNDc3MTUgMCAwIDQuNDc3MTUgMCAxMFoiICAgZmlsbD0iIzJDN0E1MCIgPgo8L3BhdGg+CjxwYXRoIGQ9Ik0xMC40MTkyIDEwLjEwMUw2LjU1NzIgMTAuNzQ0OUM2LjQ2Njg0IDEwLjc2IDYuMzkxMzggMTAuODIyMSA2LjM1OTI5IDEwLjkwNzlMNS4wMjcxOCAxNC40NzQ2QzQuOTAwMDIgMTQuODAyNyA1LjI0MzA0IDE1LjExNTQgNS41NTc4NyAxNC45NThMMTQuNzg3MiAxMC4zNDRDMTQuOTE3NiAxMC4yNzg5IDE1IDEwLjE0NTcgMTUgMTBDMTUgOS44NTQyOSAxNC45MTc2IDkuNzIxMDkgMTQuNzg3MiA5LjY1Nkw1LjU1Nzg3IDUuMDQxOTZDNS4yNDMwNCA0Ljg4NDU3IDQuOTAwMDIgNS4xOTczIDUuMDI3MTggNS41MjU0MUw2LjM1OTggOS4wOTIwNkM2LjM5MTc1IDkuMTc4MDYgNi40NjcyMyA5LjI0MDQzIDYuNTU3NzIgOS4yNTU2TDEwLjQxOTcgOS44OTlDMTAuNDY4OCA5LjkwNzYgMTAuNTA0NSA5Ljk1MDIgMTAuNTA0NSAxMEMxMC41MDQ1IDEwLjA0OTggMTAuNDY4OCAxMC4wOTI0IDEwLjQxOTcgMTAuMTAxTDEwLjQxOTIgMTAuMTAxWiIgICBmaWxsPSIjRkZGRkZGIiA+CjwvcGF0aD4KPC9zdmc+Cg==);
}
.gc-ai-formula-generator-container .gc-ai-icon-background,
.gc-ai-formula-generator-container .gc-ai-refresh {
  transition: opacity 0.3s ease, transform 0.3s ease;
  opacity: 0;
  transform: scale(0.9);
  pointer-events: none;
}
.gc-ai-formula-generator-container .gc-ai-icon-background.show,
.gc-ai-formula-generator-container .gc-ai-refresh.show {
  opacity: 1;
  transform: scale(1);
  pointer-events: auto;
}
.gc-ai-formula-generator-container .gc-ai-icon-background.show:hover,
.gc-ai-formula-generator-container .gc-ai-refresh.show:hover {
  transform: scale(1.1);
}

.gc-ai-formula-explain-container {
  width: 100%;
  height: 100%;
}

.gc-ai-formula-explain-content {
  padding: 4px;
  height: calc(100% - 56px);
  max-height: calc(100% - 56px);
  width: calc(100% - 36px);
  margin: auto;
  overflow-y: scroll;
}
.gc-ai-formula-explain-content::-webkit-scrollbar {
  width: 4px;
  height: 6px;
  cursor: pointer;
}
.gc-ai-formula-explain-content::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: rgb(203, 203, 203);
  cursor: pointer;
}
.gc-ai-formula-explain-content::-webkit-scrollbar-thumb:hover {
  background-color: rgb(153, 153, 153);
}

.gc-ai-formula-explain-button-container {
  position: absolute;
  bottom: 24px;
  right: 24px;
  border: none;
  cursor: pointer;
  z-index: 1000;
  width: 30px;
  height: 30px;
}
.gc-ai-formula-explain-button-container .gc-ai-formula-explain-button {
  width: 30px;
  height: 30px;
  position: absolute;
  background-image: url(data:image/svg+xml;base64,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);
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}
.gc-ai-formula-explain-button-container .gc-ai-refresh {
  width: 30px;
  height: 30px;
  background-color: rgb(44, 122, 80);
  position: absolute;
}
.gc-ai-formula-explain-button-container .gc-ai-formula-explain-button,
.gc-ai-formula-explain-button-container .gc-ai-refresh {
  transition: opacity 0.3s ease, transform 0.3s ease;
  opacity: 0;
  transform: scale(0.9);
  pointer-events: none;
}
.gc-ai-formula-explain-button-container .gc-ai-formula-explain-button.show,
.gc-ai-formula-explain-button-container .gc-ai-refresh.show {
  opacity: 1;
  transform: scale(1);
  pointer-events: auto;
}
.gc-ai-formula-explain-button-container .gc-ai-formula-explain-button.show:hover,
.gc-ai-formula-explain-button-container .gc-ai-refresh.show:hover {
  transform: scale(1.1);
}

/* Base Style */
/* Tools */
/* Component */
/* Layout */
.gc-drop-down-list {
  box-shadow: rgba(0, 0, 0, 0.4) 1px 2px 5px;
  background: #fcfdfd;
  -ms-overflow-style: none;
  scrollbar-width: none;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
}

.gc-drop-down-list::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}

.gc-drop-down-list-span {
  display: block;
  margin: 1px 0px 0px 30px;
  border-bottom: 1px solid #e1dfdd;
}

.gc-drop-down-list-block {
  background: #fcfdfd;
  color: #444444;
}

.gc-drop-down-list-item {
  height: 21px;
  margin-bottom: 1px;
  border: 1px solid rgba(0, 0, 0, 0);
  white-space: nowrap;
  line-height: 21px;
}

.gc-drop-down-list-icon {
  width: 16px;
  height: 16px;
  display: inline-block;
  margin: 3px 0px 0px 3px;
}

.gc-drop-down-list-link {
  padding: 0 15px 0 0;
  display: inline-block;
  letter-spacing: 0.2px;
  font-size: 12px;
  vertical-align: top;
  margin-left: 3px;
  white-space: nowrap;
}

.gc-drop-down-list-link-hover:hover {
  background-image: none;
  background-color: #d3f0e0;
}

.selectable-item:focus {
  background-image: none;
  background-color: #d3f0e0;
  outline: none;
}

.filter-dialog-container .selectable-item:focus,
.filter-value-dialog-container .selectable-item:focus,
.filter-top-ten-container .selectable-item:focus,
.filter-date-dialog-container .selectable-item:focus,
.gc-pivot-sort-dialog-container .selectable-item:focus {
  background-color: transparent;
  outline: 1px solid rgb(82, 146, 247);
}

.gc-pivot-filter-selectable-item {
  background-image: none;
  background-color: #d3f0e0;
}

.gc-drop-down-list-item-noHover,
.gc-Filter-list-noHover {
  color: #b1b1b1;
}

.icon-Move-Report {
  background-repeat: round;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+ZmlsdGVyX2xpZ2h0PC90aXRsZT4KICAgIDxnIGlkPSJmaWx0ZXJfbGlnaHQiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwYXRoIGQ9Ik0xMi45MzI0NzkyLDMgQzEzLjIwODYyMTYsMyAxMy40MzI0NzkyLDMuMjIzODU3NjMgMTMuNDMyNDc5MiwzLjUgQzEzLjQzMjQ3OTIsMy42MTY5NjgyNSAxMy4zOTE0NzExLDMuNzMwMjM0NyAxMy4zMTY1ODk4LDMuODIwMDkyMiBMOS4yLDguNzYgTDkuMiwxMi44IEw2LjgsMTQgTDYuOCw4Ljc2IEwyLjY4MzQxMDE3LDMuODIwMDkyMiBDMi41MDY2MjgxMywzLjYwNzk1Mzc1IDIuNTM1MjkwMTYsMy4yOTI2NzE0IDIuNzQ3NDI4NjEsMy4xMTU4ODkzNiBDMi44MzcyODYxMSwzLjA0MTAwODExIDIuOTUwNTUyNTUsMyAzLjA2NzUyMDgxLDMgTDEyLjkzMjQ3OTIsMyBaIiBpZD0iQ29tYmluZWQtU2hhcGUiIGZpbGw9IiM2OTY5NjkiPjwvcGF0aD4KICAgIDwvZz4KPC9zdmc+);
}

.icon-Move-Row {
  background-repeat: round;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+c2VyaWVzX2xpZ2h0PC90aXRsZT4KICAgIDxnIGlkPSJzZXJpZXNfbGlnaHQiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwYXRoIGQ9Ik02LDMgTDYsMTMgTDMsMTMgTDMsMyBMNiwzIFogTTgsMyBMOCwxMyBMNywxMyBMNywzIEw4LDMgWiBNMTAsMyBMMTAsMTMgTDksMTMgTDksMyBMMTAsMyBaIE0xMiwzIEwxMiwxMyBMMTEsMTMgTDExLDMgTDEyLDMgWiIgaWQ9IuW9oueKtue7k+WQiCIgZmlsbD0iIzY5Njk2OSI+PC9wYXRoPgogICAgPC9nPgo8L3N2Zz4=);
}

.icon-Move-Col {
  background-repeat: round;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+Y2F0ZWdvcnlfbGlnaHQ8L3RpdGxlPgogICAgPGcgaWQ9ImNhdGVnb3J5X2xpZ2h0IiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8cGF0aCBkPSJNMTMsMTIgTDEzLDEzIEwzLDEzIEwzLDEyIEwxMywxMiBaIE0xMywxMCBMMTMsMTEgTDMsMTEgTDMsMTAgTDEzLDEwIFogTTEzLDggTDEzLDkgTDMsOSBMMyw4IEwxMyw4IFogTTEzLDQgTDEzLDcgTDMsNyBMMyw0IEwxMyw0IFoiIGlkPSLlvaLnirbnu5PlkIgiIGZpbGw9IiM2OTY5NjkiPjwvcGF0aD4KICAgIDwvZz4KPC9zdmc+);
}

.icon-Move-Value {
  background-repeat: round;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+dmFsdWVfbGlnaHQ8L3RpdGxlPgogICAgPGcgaWQ9InZhbHVlX2xpZ2h0IiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8cGF0aCBkPSJNMy40MzYyOTYyMiwzIEwxMywzIEwxMywzIEwxMyw2LjEyNSBMMTIuMjMwNzY5Miw2LjEyNSBMMTEuNDYxNTM4NSw0LjAxODQ3MzMxIEw1LjkwMzIzODkzLDQuMDE4NDczMzEgTDguOTY2Nzk2ODgsNy42ODMwMjQwOSBMNS42MjEwNTMwNiwxMS43MjI3Mzc2IEwxMS40NjE1Mzg1LDExLjcyMjczNzYgTDEyLjIzMDc2OTIsOS44NzUgTDEzLDkuODc1IEwxMywxMyBMMy40MzYyOTYyMiwxMyBDMy4zMjU4MzkyNywxMyAzLjIzNjI5NjIyLDEyLjkxMDQ1NjkgMy4yMzYyOTYyMiwxMi44IEMzLjIzNjI5NjIyLDEyLjc1MjA4MzggMy4yNTM0OTkxMiwxMi43MDU3NiAzLjI4NDc3NDQ4LDEyLjY2OTQ1ODIgTDcuMzA3NjkyMzIsOCBMNy4zMDc2OTIzMiw4IEwzLjI4NDc3NDQ4LDMuMzMwNTQxODEgQzMuMjEyNjc4MjMsMy4yNDY4NTg2NiAzLjIyMjA3MTI3LDMuMTIwNTc0NTEgMy4zMDU3NTQ0MSwzLjA0ODQ3ODI2IEMzLjM0MjA1NjE3LDMuMDE3MjAyOSAzLjM4ODM3OTk4LDMgMy40MzYyOTYyMiwzIFoiIGlkPSJTaGFwZSIgZmlsbD0iIzY5Njk2OSIgZmlsbC1ydWxlPSJub256ZXJvIj48L3BhdGg+CiAgICA8L2c+Cjwvc3ZnPg==);
}

.icon-Move-Remove {
  background-repeat: round;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+ZGVsZXRlX2xpZ2h0PC90aXRsZT4KICAgIDxnIGlkPSJkZWxldGVfbGlnaHQiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwYXRoIGQ9Ik0xMy45NzczODAzLDQuNzAzMzM2NjcgQzEzLjY0MDA5NzcsNS41NzA0MTE3NCAxMS44MTkxMTgsNi40MDUwODg0OSAxMC4xNDc4ODgyLDcuNjQ2NjYzNjYgQzEwLjk3OTcwNzMsOC45NzA1MzM0NCAxMS44OTk3MzYsMTAuNzQ2MzgxMyAxMy40Mjg4OTUxLDE0IEMxMS41MTE0ODE1LDExLjc1NjU3NzQgMTAuMTAzNDIzMywxMC4zOTkzNTg3IDguNjgxNjMwNyw4LjkwMzc0MzYyIEM3LjE2ODczOTI5LDEwLjI4NzI0NzIgNi40NTA3MjAzMywxMS4wMjA5NzgxIDUuOTIzMjY4MjIsMTIuMDAxNTM2NyBDNS4zMjMwNzAzOSwxMy4xMTczMzMgNC45NzgwMjkxNiwxNC4zNzA5NjggMy44NDI1NDcyMiwxMy44MjcxMDggQzMuMDg1NTU5MjcsMTMuNDYzMzQyIDIuNDAzNDAyNSwxMi4xMDI0OTkzIDMuODQyNTQ3MjIsMTAuNjkxNTY0MiBDNS4yODE2OTE5NSw5LjI4MDYyOTA3IDYuNDA0MTU5NzYsOC4yMjYzMDM5NSA3LjIxMTAzNTE4LDcuNTI5NzgxNDcgQzYuMTExMzQyMDcsNi42Mzc2NjAyMSA1LjEyNDQzOCw2LjAyMzEwMjcxIDMuODQyNTQ3MjIsNS41NjUxMTUzMiBDMi41NjA2NTY0NCw1LjEwNzEyNzk0IDMuMTk2NjQzMTcsMy43MzU4ODA3NyAzLjUyNzQxODcxLDMuMzE0ODY2MzMgQzMuODYwMzYzMjcsMi44OTUwNDQ1NiA0LjU0NjM5NDE5LDIuODk1MDQ0NTYgNS4zNjQxMTQ3MSwzLjMxNDg2NjMzIEM2Ljk2NDg1MTQyLDQuMzMyMjE4NTEgOC4yMDAxMDgyNyw1LjMwNjYzNDM4IDkuMDczMTM4NzksNi4yMzgxMTM5MiBDMTAuOTUzNjc5MSw1LjI0MzQyMjU3IDE0LjMwNzA3MTQsMy44NTQxNTE3MyAxMy45NzczODAzLDQuNzAzMzM2NjcgWiIgaWQ9IkZpbGwtOSIgZmlsbD0iIzY5Njk2OSI+PC9wYXRoPgogICAgPC9nPgo8L3N2Zz4=);
}

.icon-Move-Setting {
  background-repeat: round;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+aW5mb3JfbGlnaHQ8L3RpdGxlPgogICAgPGcgaWQ9ImluZm9yX2xpZ2h0IiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8ZyBpZD0i57yW57uELTMiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDEuMDAwMDAwLCAxLjAwMDAwMCkiPgogICAgICAgICAgICA8cGF0aCBkPSJNMTMsMCBMMTMuMDAxMTk5NSw2LjAwMDUzNTA2IEMxMi42OTMyOTQ2LDUuNzY5MTE0NzEgMTIuMzU3OCw1LjU3MjQyMjkzIDEyLjAwMDUzMjQsNS40MTYyNzYzNCBMMTIsMSBMMSwxIEwxLDEyIEw1LjQxNjI3NjM0LDEyLjAwMDUzMjQgQzUuNTcyNDIyOTMsMTIuMzU3OCA1Ljc2OTExNDcxLDEyLjY5MzI5NDYgNi4wMDA1MzUwNiwxMy4wMDExOTk1IEwwLDEzIEwwLDAgTDEzLDAgWiIgaWQ9IuW9oueKtue7k+WQiCIgZmlsbD0iIzY5Njk2OSI+PC9wYXRoPgogICAgICAgICAgICA8cmVjdCBpZD0i55+p5b2iIiBmaWxsPSIjQjNCM0IzIiB4PSIyIiB5PSIyIiB3aWR0aD0iMyIgaGVpZ2h0PSIzIj48L3JlY3Q+CiAgICAgICAgICAgIDxyZWN0IGlkPSLnn6nlvaLlpIfku70tOSIgZmlsbD0iI0IzQjNCMyIgeD0iMiIgeT0iNiIgd2lkdGg9IjMiIGhlaWdodD0iNSI+PC9yZWN0PgogICAgICAgICAgICA8cmVjdCBpZD0i55+p5b2i5aSH5Lu9LTgiIGZpbGw9IiNCM0IzQjMiIHg9IjYiIHk9IjIiIHdpZHRoPSI1IiBoZWlnaHQ9IjMiPjwvcmVjdD4KICAgICAgICAgICAgPHBhdGggZD0iTTEwLDYgQzEyLjIwOTEzOSw2IDE0LDcuNzkwODYxIDE0LDEwIEMxNCwxMi4yMDkxMzkgMTIuMjA5MTM5LDE0IDEwLDE0IEM3Ljc5MDg2MSwxNCA2LDEyLjIwOTEzOSA2LDEwIEM2LDcuNzkwODYxIDcuNzkwODYxLDYgMTAsNiBaIE0xMC42MjIyMjIyLDguOTMzMzMzMzMgTDkuMiw4LjkzMzMzMzMzIEw5LjIsMTMuMiBMMTAuNjIyMjIyMiwxMy4yIEwxMC42MjIyMjIyLDguOTMzMzMzMzMgWiBNOS45MTExMTExMSw2LjggQzkuNTE4Mzc1MjksNi44IDkuMiw3LjExODM3NTI5IDkuMiw3LjUxMTExMTExIEM5LjIsNy45MDM4NDY5MyA5LjUxODM3NTI5LDguMjIyMjIyMjIgOS45MTExMTExMSw4LjIyMjIyMjIyIEMxMC4zMDM4NDY5LDguMjIyMjIyMjIgMTAuNjIyMjIyMiw3LjkwMzg0NjkzIDEwLjYyMjIyMjIsNy41MTExMTExMSBDMTAuNjIyMjIyMiw3LjExODM3NTI5IDEwLjMwMzg0NjksNi44IDkuOTExMTExMTEsNi44IFoiIGlkPSLlvaLnirbnu5PlkIgiIGZpbGw9IiMzNjdGQzkiPjwvcGF0aD4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==);
}

.icon-Be-Used {
  background-repeat: round;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjExR/NCNwAAAGxJREFUOE+ljsENgDAMAzsaQ3QMxP4/wAhXwTKhEY9TlZOdtK1b/4WVFaysYGUFKxMWdY/hA5T3+x0+BjJYJmOJBoF+87UMYhAwzFBaBnFwYZ1j/kKFltIycHLqMrHyhEvSMrCygpUVrJyntwPdKU02VXQw7gAAAABJRU5ErkJggg==);
}

.icon-clear-filter {
  background-repeat: round;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAgRJREFUOBFjYKAQMIL0nzx5cs6/f/+SCZnFyMh4kpeX105bW/sXTC0TiCEmJpbz58+fc0DMgAe/YWJiCkXWDNILNkBRUfEHUGPw379/3/3+/RtsCIhGwv+A8pFmZmaPQZqQAdgAkICjo+MDoAExQPwPpBFIgzHIRUDv1QLl9yBrhLHBYQDjgOjt27fXAzU0AP0LFgbSmzw8PAKA9H9kdTA2hgH///9n2gIEQEO+mpzfvp2FmfWgeO3kuzAN6DTcCzAJoE0g/zZoXj+8h+Pti7lMLx5deJHpmwKTB9H/09JY74XamoHYGAaABIFh8PemhAb//3//1wBN4/r769fsRwmu0x8luUrdjXRov/v++sM/f/5a4DOA4RMrZ+dOq7C1QC8Z/P/7/8K/P38z/nz/+fjfn98+QDf4qq0/NgmvAaCYAHrlpziDzI0/f//e+AuMjb9//jH9+8dQrrL6yFmQZhDA6gWQBDAaG+Pu79/+8OudjX9//4kAeqcLKLbm7+9fHf8bGuD64AyQJhj4+fPnpaTHj5se3Xm08d/fv57AGJmgsupwubqIbtSP778enNm/IhymFqsB6enpvx/fO+IL9ILbn39/L6oIaJaBNDDOmvVb8Ldg2I9vv2yAYQNOAlgNACn+zfD/+r+//+4x/mMoAGkEiYGA4oEDP1iZmCp3a4mpQEQoJAFYGTPePuR04gAAAABJRU5ErkJggg==);
}

.gc-Filter-list-select-block {
  width: 100%;
  padding-bottom: 5px;
  background-color: rgb(240, 240, 240);
}

.gc-Filter-list {
  font-family: "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
  width: 239px;
  border: 1px solid #c8c6c4;
  background: #fcfdfd;
  color: #222222;
  position: absolute;
  flex-direction: column;
}

.gc-Filter-list.gc-Filter-list-resizable {
  overflow: hidden;
  resize: both;
}

.gc-Filter-list-item {
  padding: 4px 5px 4px 5px;
  font-weight: 500;
  font-size: 12px;
  color: #222222;
}
.gc-Filter-list-item select {
  font-size: 12px;
}

.gc-Filter-list-sort-item {
  padding-left: 3px;
  border: 1px solid rgba(0, 0, 0, 0);
  display: flex;
}

.gc-Filter-list-sort-item.gc-Filter-list-button-container {
  display: flex;
  justify-content: flex-end;
}

.gc-Filter-list-link {
  display: inline-block;
  width: 182px;
  height: 23px;
  font-size: 9pt;
  vertical-align: top;
  margin: 4px 0px 0px 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-grow: 1;
}

.gc-Filter-list-link-arrow {
  display: inline-block;
  font-size: 10px;
  margin-top: 7px;
  height: 13px;
  width: 13px;
  vertical-align: top;
  background-repeat: round;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAAXNSR0IArs4c6QAAAGBJREFUGBljYCAHzJo1a8L///+ZsOlFEQQqygcqXr1q1SpOdMUoCqGSQe/fv987c+ZMEWTF2BSC5C0ZGRkXEaPwONAZccgKWZA5UPY6QUHBmLCwsO9Y5CBC+HyNUxPZEgCPEyCkHm49GwAAAABJRU5ErkJggg==);
}

.gc-Filter-list-icon {
  width: 18px;
  height: 18px;
  display: inline-block;
  margin: 3px 0px 0px 3px;
}

.gc-Filter-list-icon-selected {
  background-repeat: round;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMThweCIgaGVpZ2h0PSIxOHB4IiB2aWV3Qm94PSIwIDAgMTggMTgiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+Y2hlY2tfbGlnaHQ8L3RpdGxlPgogICAgPGcgaWQ9ImNoZWNrX2xpZ2h0IiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8cG9seWdvbiBpZD0iUGF0aC00IiBmaWxsPSIjMzY3RkM5IiBmaWxsLXJ1bGU9Im5vbnplcm8iIHBvaW50cz0iMTQuMDYwMjI2OSA1IDE1IDUuNzUwMzEwMjkgNy44MjE0MzE4MSAxMyA0IDkuMTQwNzA3NDMgNC45Mzk3NzMxNCA4LjM5MDM5NzEzIDcuODIxNDMxODEgMTEuMzAwNjA1OCI+PC9wb2x5Z29uPgogICAgICAgIDxwYXRoIGQ9Ik0xNywxIEwxNywxNyBMMSwxNyBMMSwxIEwxNywxIFogTTE2LDIgTDIsMiBMMiwxNiBMMTYsMTYgTDE2LDIgWiIgaWQ9IuW9oueKtue7k+WQiCIgZmlsbD0iIzY5Njk2OSI+PC9wYXRoPgogICAgPC9nPgo8L3N2Zz4=);
}

.item-hover:hover {
  background-image: none;
  background-color: #d3f0e0;
}

.item-noHover {
  color: #b1b1b1;
}

.clear-filter-icon {
  background-repeat: round;
  background-image: url(data:image/svg+xml;base64,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);
}

.clear-filter-icon-default {
  background-repeat: round;
  background-image: url(data:image/svg+xml;base64,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);
}

.gc-Filter-list-span {
  display: block;
  border-bottom: solid 1px #e1dfdd;
  margin-left: 32px;
}

.filter-search {
  margin: 5px 5px 0px 30px;
  height: 25px;
  width: 210px;
}

.filter-search-input {
  outline: none;
  padding-left: 7px;
}

.filter-select-all-container {
  height: 20px;
  margin: 4px 0px 2px 24px;
}

.filter-select-all-link {
  height: 16px;
  cursor: pointer;
  margin-right: 10px;
  font-size: 12px;
  float: left;
}

.filter-select-all-link:hover {
  text-decoration: underline;
}

.filter-select-icon {
  height: 15px;
  width: 15px;
  margin-right: 4px;
  float: left;
}

.filter-check-all-icon {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjExR/NCNwAAAGxJREFUOE+ljsENgDAMAzsaQ3QMxP4/wAhXwTKhEY9TlZOdtK1b/4WVFaysYGUFKxMWdY/hA5T3+x0+BjJYJmOJBoF+87UMYhAwzFBaBnFwYZ1j/kKFltIycHLqMrHyhEvSMrCygpUVrJyntwPdKU02VXQw7gAAAABJRU5ErkJggg==);
  background-repeat: round;
}

.filter-uncheck-all-icon {
  background-repeat: round;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjExR/NCNwAAAIJJREFUOE+lkssNgDAMQzsCw3UMxEocGKWDQSLVUj5GJeLwhPyI0x7a9qP/gsoKVFagskIUm3ALp3GKZvX63/q0QIcAlqAMXMcFIQ6z7DouTGLptawkMVmeDJi8BFsGQ0jzUcRyvEla4oLAhvVrveu4IOAdxJOwZPkOylBZgcrv9PYAV9tkcyJlS4sAAAAASUVORK5CYII=);
}

.filter-list-box {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  min-height: 277px;
}

.filter-list-box-host {
  height: 170px;
  width: 100%;
  clear: both;
  flex: 1;
  display: flex;
}

.filter-list-box-icon {
  display: inline-block;
  float: left;
  height: 100%;
  width: 25px;
}

.filter-list-box-container {
  display: inline-block;
  height: 100%;
  flex: 1;
  width: calc(100% - 40px);
  margin-right: 15px;
  border: 1px solid rgb(200, 200, 200);
}

.filter-confirm {
  display: inline-block;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  cursor: default;
  height: 25px;
  line-height: 23px;
  width: 81px;
  margin-left: 10px;
  text-align: center;
  font-size: 12px;
  font-weight: normal;
  color: rgb(0, 0, 0);
  border: 1px solid rgb(180, 180, 180);
  background-color: rgb(239, 239, 239);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.filter-confirm:hover {
  border: 1px solid rgb(50, 150, 200);
  background-color: rgba(191, 216, 231, 0.9);
  line-height: 23px;
}

.filter-dialog-confirm {
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  height: 24px;
  line-height: 25px;
  width: 70px;
  margin: 8px 20px 7px -15px;
  text-align: center;
  font-size: 12px;
  float: right;
  font-weight: normal;
  color: #b1b1b1;
  border: 1px solid rgb(180, 180, 180);
  background-color: rgb(239, 239, 239);
}

.filter-confirm-enable {
  color: rgb(0, 0, 0);
  box-sizing: border-box;
  line-height: 24px;
  border: 2px solid #0078d7;
}

.filter-confirm-enable:hover {
  border: 1px solid rgb(50, 150, 200);
  background-color: rgba(191, 216, 231, 0.9);
  line-height: 24px;
}

.ok-class {
  line-height: 22px;
}

.cancel-class {
  border: 1px solid #b4b4b4;
}

.filter-bottom-line {
  display: block;
  background-color: #D4D4D4;
  margin-bottom: 10px;
  margin-top: 9px;
}

.filter-sort-icon-one {
  background-repeat: round;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMThweCIgaGVpZ2h0PSIxOHB4IiB2aWV3Qm94PSIwIDAgMTggMTgiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+c29ydGF6X2xpZ2h0PC90aXRsZT4KICAgIDxnIGlkPSJzb3J0YXpfbGlnaHQiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxnIGlkPSJTb3J0LUEtdG8tWiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMi4wMDAwMDAsIDIuMDAwMDAwKSI+CiAgICAgICAgICAgIDxwb2x5Z29uIGlkPSJTaGFwZSIgZmlsbD0iIzY2NjY2NiIgZmlsbC1ydWxlPSJub256ZXJvIiBwb2ludHM9IjExIDE0Ljk5MzkwMDQgMTQgMTIgMTIgMTIgMTIgLTIuOTEzMTUwMzZlLTE1IDEwIC0yLjkxMzE1MDM2ZS0xNSAxMCAxMiA4IDEyIj48L3BvbHlnb24+CiAgICAgICAgICAgIDxnIGlkPSJHcm91cCI+CiAgICAgICAgICAgICAgICA8cG9seWdvbiBpZD0iWiIgZmlsbD0iIzY2NjY2NiIgcG9pbnRzPSIwLjgwNTYwMjcxNiA5IDYuMjU1NTE3ODMgOSA2LjI1NTUxNzgzIDkuNzgxNTEyNjEgMi4wNTg1NzM4NSAxNC4xNTk2NjM5IDYuNSAxNC4xNTk2NjM5IDYuNSAxNSAwLjUgMTUgMC41IDE0LjIyNjg5MDggNC43MDcxMzA3MyA5Ljg0MDMzNjEzIDAuODA1NjAyNzE2IDkuODQwMzM2MTMiPjwvcG9seWdvbj4KICAgICAgICAgICAgICAgIDxwYXRoIGQ9Ik0yLjgxNDQzMjk5LDAgTDQuMTg1NTY3MDEsMCBMNyw3IEw1LjcxMTM0MDIxLDcgTDUuMDQxMjM3MTEsNS4yNDUwOTgwNCBMMS45NTg3NjI4OSw1LjI0NTA5ODA0IEwxLjI4ODY1OTc5LDcgTDAsNyBMMi44MTQ0MzI5OSwwIFogTTIuMzE5NTg3NjMsNC4zMDM5MjE1NyBMNC42ODA0MTIzNyw0LjMwMzkyMTU3IEwzLjUyNTc3MzIsMS4yMzUyOTQxMiBMMy40ODQ1MzYwOCwxLjIzNTI5NDEyIEwyLjMxOTU4NzYzLDQuMzAzOTIxNTcgWiIgaWQ9IkEiIGZpbGw9IiMzNjdGQzkiPjwvcGF0aD4KICAgICAgICAgICAgPC9nPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+);
}

.filter-sort-icon-two {
  background-repeat: round;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMThweCIgaGVpZ2h0PSIxOHB4IiB2aWV3Qm94PSIwIDAgMTggMTgiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+c29ydHphX2xpZ2h0PC90aXRsZT4KICAgIDxnIGlkPSJzb3J0emFfbGlnaHQiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxnIGlkPSJTb3J0LUEtdG8tWiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMi4wMDAwMDAsIDIuMDAwMDAwKSI+CiAgICAgICAgICAgIDxwb2x5Z29uIGlkPSJTaGFwZSIgZmlsbD0iIzY2NjY2NiIgZmlsbC1ydWxlPSJub256ZXJvIiBwb2ludHM9IjExIDE0Ljk5MzkwMDQgMTQgMTIgMTIgMTIgMTIgLTIuOTEzMTUwMzZlLTE1IDEwIC0yLjkxMzE1MDM2ZS0xNSAxMCAxMiA4IDEyIj48L3BvbHlnb24+CiAgICAgICAgICAgIDxnIGlkPSJHcm91cCI+CiAgICAgICAgICAgICAgICA8cG9seWdvbiBpZD0iWiIgZmlsbD0iIzY2NjY2NiIgcG9pbnRzPSIwLjgwNTYwMjcxNiAwIDYuMjU1NTE3ODMgMCA2LjI1NTUxNzgzIDAuNzgxNTEyNjA1IDIuMDU4NTczODUgNS4xNTk2NjM4NyA2LjUgNS4xNTk2NjM4NyA2LjUgNiAwLjUgNiAwLjUgNS4yMjY4OTA3NiA0LjcwNzEzMDczIDAuODQwMzM2MTM0IDAuODA1NjAyNzE2IDAuODQwMzM2MTM0Ij48L3BvbHlnb24+CiAgICAgICAgICAgICAgICA8cGF0aCBkPSJNMi44MTQ0MzI5OSw4IEw0LjE4NTU2NzAxLDggTDcsMTUgTDUuNzExMzQwMjEsMTUgTDUuMDQxMjM3MTEsMTMuMjQ1MDk4IEwxLjk1ODc2Mjg5LDEzLjI0NTA5OCBMMS4yODg2NTk3OSwxNSBMMCwxNSBMMi44MTQ0MzI5OSw4IFogTTIuMzE5NTg3NjMsMTIuMzAzOTIxNiBMNC42ODA0MTIzNywxMi4zMDM5MjE2IEwzLjUyNTc3MzIsOS4yMzUyOTQxMiBMMy40ODQ1MzYwOCw5LjIzNTI5NDEyIEwyLjMxOTU4NzYzLDEyLjMwMzkyMTYgWiIgaWQ9IkEiIGZpbGw9IiMzNjdGQzkiPjwvcGF0aD4KICAgICAgICAgICAgPC9nPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+);
}

.icon-class {
  background-repeat: round;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMThweCIgaGVpZ2h0PSIxOHB4IiB2aWV3Qm94PSIwIDAgMTggMTgiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+Y2hlY2tfbGlnaHQ8L3RpdGxlPgogICAgPGcgaWQ9ImNoZWNrX2xpZ2h0IiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8cG9seWdvbiBpZD0iUGF0aC00IiBmaWxsPSIjMzY3RkM5IiBmaWxsLXJ1bGU9Im5vbnplcm8iIHBvaW50cz0iMTQuMDYwMjI2OSA1IDE1IDUuNzUwMzEwMjkgNy44MjE0MzE4MSAxMyA0IDkuMTQwNzA3NDMgNC45Mzk3NzMxNCA4LjM5MDM5NzEzIDcuODIxNDMxODEgMTEuMzAwNjA1OCI+PC9wb2x5Z29uPgogICAgICAgIDxwYXRoIGQ9Ik0xNywxIEwxNywxNyBMMSwxNyBMMSwxIEwxNywxIFogTTE2LDIgTDIsMiBMMiwxNiBMMTYsMTYgTDE2LDIgWiIgaWQ9IuW9oueKtue7k+WQiCIgZmlsbD0iIzY5Njk2OSI+PC9wYXRoPgogICAgPC9nPgo8L3N2Zz4=);
}

.icon-class-second-menu {
  background-repeat: round;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAO5JREFUOBFjYBgFRIfAjRs3eI8dO7bz+PHjRsiaWJA5uNhnzpxhffny5dr///+7MjExgfQ4w9QywRggev/+/Rx79+6NRBYDamJ8//79/J8/f7r+/v373q9fv6KQ5VEM+Pbt24rv378v27ZtWwFMEZDdCdQUDdT8+u/fv+4uLi4vYXIgmhGZs3HjRr9///6tBYoxA3EqEPMAXTCBkZHxKzMzs6Ofn99pZPUgNooBIIGVK1eCvLAEqJEB6F9GIP0XSPuFhoZuB8mjAwwDQAoWL16cAtQ4G6o4MS4ubgGUTTw1b968wrlz51YRr2PkqgQA82JlH8nI1QYAAAAASUVORK5CYII=);
}

.filter-dialog-container {
  border: 1px solid #707070;
  box-shadow: 2px 2px 10px #707070;
  width: 446px;
  height: 165px;
}
.filter-dialog-container * {
  box-sizing: border-box;
  font-family: "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
}
.filter-dialog-container *:not(input) {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.show-value-as-dialog-container {
  border: 1px solid #707070;
  box-shadow: 2px 2px 10px #707070;
  width: 560px;
  height: 165px;
}
.show-value-as-dialog-container * {
  box-sizing: border-box;
  font-family: "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
}
.show-value-as-dialog-container *:not(input) {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.show-value-as-dialog-header {
  height: 20%;
  font-size: 12px;
  color: #000000;
  background-color: white;
  padding-left: 10px;
}
.show-value-as-dialog-header > span:first-child {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: inline-block;
  padding-top: 10px;
  height: 100%;
}

.show-value-as-dialog-content {
  font-size: 12px;
  padding: 6px 9px 9px 9px;
  background-color: rgb(240, 240, 240);
  height: 80%;
}
.show-value-as-dialog-content > div:first-child {
  margin-bottom: 2px;
}
.show-value-as-dialog-content > .show-value-as-dialog-content-item {
  margin-bottom: 2px;
}
.show-value-as-dialog-content select {
  padding: 0px;
}

.show-value-as-dialog-content-select {
  width: 82%;
  height: 22px;
  float: right;
  border: 1px solid #abadb3;
  outline: none;
  font-size: 12px;
}

.show-value-as-dialog-content-item {
  margin-top: 10px;
  white-space: nowrap;
}
.show-value-as-dialog-content-item > span:first-child {
  display: inline-block;
}

.show-value-as-dialog-bottom-confirm {
  position: absolute;
  right: 12px;
  bottom: 12px;
}

.date-group-dialog-container {
  border: 1px solid #707070;
  box-shadow: 2px 2px 10px #707070;
  width: 315px;
  height: 330px;
}
.date-group-dialog-container * {
  box-sizing: border-box;
  font-family: "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
}
.date-group-dialog-container *:not(input) {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.number-group-dialog-container {
  border: 1px solid #707070;
  box-shadow: 2px 2px 10px #707070;
  width: 315px;
  height: 248px;
}
.number-group-dialog-container * {
  box-sizing: border-box;
  font-family: "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
}
.number-group-dialog-container *:not(input) {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.date-group-dialog-header {
  height: 10%;
  font-size: 12px;
  color: #000000;
  background-color: white;
  padding-left: 10px;
}
.date-group-dialog-header > .sjs-cancel-icon {
  line-height: 26px;
  border: none;
}

.number-group-dialog-header {
  height: 14%;
  font-size: 12px;
  color: #000000;
  background-color: white;
  padding-left: 10px;
}
.number-group-dialog-header > .sjs-cancel-icon {
  line-height: 28px;
  border: none;
}

.date-group-dialog-header-title,
.number-group-dialog-header-title {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: inline-block;
  padding-top: 10px;
  height: 100%;
}

.date-group-dialog-content-header,
.number-group-dialog-content-header {
  margin: 8px 0px 8px 8px;
  position: relative;
}
.date-group-dialog-content-header > span:first-child,
.number-group-dialog-content-header > span:first-child {
  display: inline-block;
  line-height: 21px;
  position: absolute;
  padding-right: 6px;
  background-color: rgb(240, 240, 240);
}

.date-group-dialog-content {
  font-size: 12px;
  padding: 6px 9px 9px 9px;
  background-color: rgb(240, 240, 240);
  height: 80%;
  height: 90%;
}

.number-group-dialog-content {
  font-size: 12px;
  padding: 6px 9px 9px 9px;
  background-color: rgb(240, 240, 240);
  height: 80%;
  height: 86%;
}

.date-group-dialog-bottom-confirm,
.number-group-dialog-bottom-confirm {
  position: absolute;
  bottom: 12px;
  right: 12px;
}

.date-group--header-horizontal-group-line {
  display: inline-block;
  margin-bottom: 3px;
  width: 100%;
  border-bottom: 1px solid rgb(220, 220, 220);
}

.date-group--header-horizontal-auto-line,
.number-group--header-horizontal-line {
  display: inline-block;
  width: 100%;
  margin-bottom: 3px;
  border-bottom: 1px solid rgb(220, 220, 220);
}

.date-group-dialog-sub-container {
  margin: 8px 0px 8px 8px;
}
.date-group-dialog-sub-container > span:first-child {
  position: absolute;
  display: inline-block;
  padding-right: 4px;
  background-color: rgb(240, 240, 240);
}

.number-group-dialog-sub-container {
  margin: 16px 0px 8px 8px;
}

.date-group-by {
  display: block;
  box-sizing: border-box;
  height: 15px;
  margin: 1px 0px 1px 6px;
}

.number-group-startingAt-value,
.number-group-endingAt-value,
.number-group-by-value {
  float: right;
  width: 62%;
  border: 1px solid #abadb3;
  outline: none;
}

.date-group-startingAt-value,
.date-group-endingAt-value,
.date-group-by-value {
  float: right;
  margin-left: 5px;
  margin-right: 12px;
  outline: none;
  -webkit-appearance: none;
  -webkit-user-select: auto;
  user-select: auto;
}

.date_group_dialog_group-by-subContainer {
  box-sizing: border-box;
  margin-top: 8px;
  width: 273px;
  height: 125px;
  margin-left: 13px;
  border: 1px solid rgba(176, 167, 167, 0.9490196078);
  background-color: white;
}

.date-group-by-selected {
  background-color: rgba(127, 187, 235, 0.7);
}

.label-title-class {
  height: 20%;
  font-size: 12px;
  color: #000000;
  background-color: white;
  padding-left: 10px;
}
.label-title-class > .label-title {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: inline-block;
  padding-top: 10px;
  height: 100%;
  width: calc(100% - 50px);
}

.sjs-cancel-icon {
  height: 100%;
  width: 50px;
  font-size: 26px;
  font-weight: 300;
  display: inline-block;
  float: right;
  text-align: center;
  line-height: 25px;
  border: none;
  cursor: default;
}
.sjs-cancel-icon:hover {
  background-color: rgb(232, 17, 35);
  color: white;
}

.label-filter-body {
  font-size: 12px;
  padding: 6px 9px 9px 9px;
  background-color: rgb(240, 240, 240);
  height: 80%;
  width: auto;
}
.label-filter-body > .confirm-class {
  margin-bottom: 2px;
}
.label-filter-body > div:first-child {
  position: relative;
  margin-bottom: 6px;
}
.label-filter-body > div:last-child {
  position: absolute;
  right: 12px;
  bottom: 12px;
}

.top-ten-content > * {
  display: inline-block;
  white-space: nowrap;
  margin: 15px 2px;
}

.label-show {
  display: inline-block;
  line-height: 21px;
  position: absolute;
  padding-right: 6px;
  background-color: rgb(240, 240, 240);
}

.split-line {
  display: inline-block;
  margin-bottom: 3px;
  width: 100%;
  border-bottom: 1px solid rgb(220, 220, 220);
}

.label-select {
  margin: 0 2%;
  display: inline-block;
  border: 1px solid #abadb3;
  outline: none;
  width: 35%;
  height: 23px;
  text-overflow: ellipsis;
  font-size: 12px;
}

.label-input {
  display: inline-block;
  text-align: left;
  border: 1px solid #abadb3;
  outline: none;
  width: 100%;
  height: 23px;
  font-size: 12px;
}

.prompt-info {
  display: block;
  line-height: 20px;
}

.ok-label {
  border: 2px solid #0078d7;
  line-height: 21px;
}

.input-between-container {
  display: inline-block;
  width: 58%;
  margin-left: 2%;
}

.input-between {
  display: inline-block;
  text-align: left;
  border: 1px solid #abadb3;
  outline: none;
  width: 42%;
  height: 22px;
  font-size: 12px;
}

.and-joiner {
  display: inline-block;
  text-align: center;
  width: 11%;
  height: 20px;
}

.filter-value-dialog-container {
  border: 1px solid #707070;
  box-shadow: 2px 2px 10px #707070;
  width: 560px;
  height: 165px;
}
.filter-value-dialog-container * {
  box-sizing: border-box;
  font-family: "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
}
.filter-value-dialog-container *:not(input) {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.value-title-class {
  height: 20%;
  font-size: 12px;
  color: #000000;
  background-color: white;
  padding-left: 10px;
}
.value-title-class > .label-title {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: inline-block;
  padding-top: 10px;
  height: 100%;
}

.value-filter-body {
  font-size: 12px;
  padding: 6px 9px 9px 9px;
  background-color: rgb(240, 240, 240);
  height: 80%;
}
.value-filter-body > .confirm-class {
  margin-bottom: 2px;
}
.value-filter-body > div:first-child {
  position: relative;
  margin-bottom: 6px;
}
.value-filter-body > div:last-child {
  position: absolute;
  right: 12px;
  bottom: 12px;
}
.value-filter-body .input-between-container {
  display: inline-block;
  width: 52%;
  margin-left: 2%;
}

.split-line-class {
  display: inline-block;
  width: 100%;
  margin-bottom: 3px;
  border-bottom: 1px solid rgb(220, 220, 220);
}

.value-select {
  display: inline-block;
  margin-left: 2%;
  outline: none;
  width: 20%;
  height: 22px;
  margin-top: 2%;
  border: 1px solid #abadb3;
  font-size: 12px;
  text-overflow: ellipsis;
}

.filter-top-ten-container {
  border: 1px solid #707070;
  box-shadow: 2px 2px 10px #707070;
  width: auto;
  height: 165px;
}
.filter-top-ten-container * {
  box-sizing: border-box;
  font-family: "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
}
.filter-top-ten-container *:not(input) {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.top-ten-title-class {
  height: 20%;
  font-size: 12px;
  color: #000000;
  background-color: white;
  padding-left: 10px;
}
.top-ten-title-class > .label-title {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: inline-block;
  padding-top: 10px;
  height: 100%;
}

.top-ten-filter-body {
  font-size: 12px;
  padding: 6px 9px 9px 9px;
  background-color: rgb(240, 240, 240);
  height: 80%;
}
.top-ten-filter-body > div:first-child {
  position: relative;
  margin-bottom: 6px;
}
.top-ten-filter-body > .confirm-class {
  margin-bottom: 2px;
}

.split-line-of-top-ten {
  display: inline-block;
  width: 100%;
  margin-bottom: 3px;
  border-bottom: 1px solid rgb(220, 220, 220);
}

.value-of-top-ten {
  text-align: left;
  outline: none;
  height: 22px;
  border: 1px solid #abadb3;
  font-size: 12px;
}

.top-ten-select {
  border: 1px solid #abadb3;
  outline: none;
  width: 100px;
  height: 20px;
  height: 22px;
  text-overflow: ellipsis;
}

.close-dialog {
  position: absolute;
  bottom: 12px;
  right: 12px;
}

.by-joiner-class {
  text-align: center;
  height: 22px;
}

.filter-date-dialog-container {
  border: 1px solid #707070;
  box-shadow: 2px 2px 10px #707070;
  width: 450px;
  height: 150px;
}
.filter-date-dialog-container * {
  box-sizing: border-box;
  font-family: "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
}
.filter-date-dialog-container *:not(input) {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.date-title-class {
  height: 20%;
  font-size: 12px;
  color: #000000;
  background-color: white;
  padding-left: 10px;
}
.date-title-class > .label-title {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: inline-block;
  padding-top: 10px;
  height: 100%;
}

.date-filter-body {
  font-size: 12px;
  padding: 6px 9px 9px 9px;
  background-color: rgb(240, 240, 240);
  height: 80%;
}
.date-filter-body > .confirm-class {
  margin-bottom: 2px;
}
.date-filter-body .input-between-container {
  display: inline-block;
  width: 64%;
  margin-left: 2%;
}
.date-filter-body > div:first-child {
  margin-bottom: 3px;
  position: relative;
}
.date-filter-body > div:last-child {
  position: absolute;
  right: 12px;
  bottom: 12px;
}

.date-split-line {
  display: inline-block;
  margin-bottom: 3px;
  width: 100%;
  border-bottom: 1px solid rgb(220, 220, 220);
}

.date-select {
  display: inline-block;
  margin: 2% 0 2% 2%;
  border: 1px solid #abadb3;
  outline: none;
  width: 30%;
  height: 22px;
}

.date-and-joiner {
  display: inline-block;
  text-align: right;
  width: 25px;
  height: 20px;
}

.input-of-between-date {
  margin-left: 10px;
  display: inline-block;
  text-align: left;
  border: none;
  outline: none;
  width: 110px;
  height: 20px;
}

.gc-pivot-sort-dialog-container {
  border: 1px solid #707070;
  box-shadow: 2px 2px 10px #707070;
  width: 450px;
  height: 400px;
}
.gc-pivot-sort-dialog-container * {
  box-sizing: border-box;
  font-family: "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
}
.gc-pivot-sort-dialog-container *:not(input) {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.gc-sort-dialog-title {
  height: 36px;
  font-size: 12px;
  color: #000000;
  background-color: white;
  padding-left: 10px;
}
.gc-sort-dialog-title > .label-title {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: inline-block;
  padding-top: 10px;
  height: 100%;
  width: calc(100% - 50px);
  cursor: move;
}

.gc-sort-dialog-body {
  font-size: 12px;
  padding: 6px 9px 9px 9px;
  background-color: rgb(240, 240, 240);
  height: 80%;
  height: 364px;
}
.gc-sort-dialog-body > div:first-child {
  position: relative;
  margin-bottom: 6px;
}
.gc-sort-dialog-body > .confirm-class {
  margin-bottom: 2px;
}

.gc-sort-more-options {
  position: absolute;
  left: 15px;
  width: 125px;
}

.gc-sort-close-dialog {
  position: absolute;
  bottom: 12px;
  right: 12px;
  width: 100%;
  display: flex;
  justify-content: flex-end;
}
.gc-sort-close-dialog .gc-sort-confirm-close-dialog {
  float: right;
}

.gc-sort-summary {
  display: block;
  margin-left: 25px;
  font-size: 11px;
  margin-top: 11px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: pre;
}

.gc-sort-select {
  display: block;
  width: 90%;
  margin-left: 30px;
  margin-bottom: 5px;
  margin-top: 5px;
}

.gc-sort-range-selector {
  background-color: #fff;
  border: 1px solid #a9a9a9;
  height: 20px;
  overflow: hidden;
}

.gc-sort-range-select {
  padding: 1px;
  display: flex;
  border: 1px solid #d3d3d3;
  height: 100%;
  background-color: white;
}
.gc-sort-range-select .gc-sort-range-input {
  padding-left: 2px;
  width: calc(100% - 24px);
  border: none;
  padding-top: 1px;
  padding-bottom: 1px;
  align-self: center;
  font-family: inherit;
  font-size: inherit;
}
.gc-sort-range-select:hover:not(.disable) {
  border: 1px solid gray;
}
.gc-sort-range-select .gc-sort-range-select-button {
  display: inline-block;
  width: 20px;
  height: 20px;
  padding: 1px;
  align-self: center;
  cursor: pointer;
}
.gc-sort-range-select .gc-sort-range-select-button span {
  display: block;
  width: 20px;
  height: 20px;
  background-image: url(data:image/svg+xml;base64,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);
  background-repeat: no-repeat;
  background-position: center center;
}
.gc-sort-range-select.disable {
  background-color: rgb(235, 235, 228);
}
.gc-sort-range-select.disable .gc-sort-range-select-button {
  cursor: default;
}

.gc-sort-disabled {
  opacity: 0.6;
  pointer-events: none;
  cursor: default;
}

.gc-pivot-sort-value-dialog-container {
  border: 1px solid #707070;
  box-shadow: 2px 2px 10px #707070;
  width: 450px;
  height: 300px;
}
.gc-pivot-sort-value-dialog-container * {
  box-sizing: border-box;
  font-family: "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
}
.gc-pivot-sort-value-dialog-container *:not(input) {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.gc-sort-value-dialog-body {
  font-size: 12px;
  padding: 6px 9px 9px 9px;
  background-color: rgb(240, 240, 240);
  height: 80%;
  height: 264px;
}
.gc-sort-value-dialog-body > div:first-child {
  position: relative;
  margin-bottom: 6px;
}
.gc-sort-value-dialog-body > .confirm-class {
  margin-bottom: 2px;
}

.gc-pivot-sort-half-split-area {
  display: inline-block;
  width: 48%;
  margin-left: 2px;
  margin-right: 2px;
}
.gc-pivot-sort-half-split-area > .confirm-class {
  margin-bottom: 2px;
}

.gc-pivot-field-setting-wrapper {
  background-color: transparent;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.gc-pivot-field-setting-wrapper input[type=button] {
  display: inline-block;
  margin: 0px;
}

.gc-sort-range-select-container {
  border: 1px solid #707070;
  box-shadow: 2px 2px 10px #707070;
  width: 450px;
  height: 70px;
}
.gc-sort-range-select-container * {
  box-sizing: border-box;
  font-family: "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
}
.gc-sort-range-select-container *:not(input) {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.gc-sort-range-select-body {
  font-size: 12px;
  padding: 6px 9px 9px 9px;
  background-color: rgb(240, 240, 240);
  height: 80%;
  height: 34px;
}
.gc-sort-range-select-body > .confirm-class {
  margin-bottom: 2px;
}

.gc-pivot-dialog-close:hover {
  background-color: #E81123;
  color: white;
}

.pivot-field-setting-control {
  width: 75px;
  height: 23px;
  line-height: 21px;
  font-size: 12px;
  background-color: rgb(225, 225, 225);
  border: 1px solid rgb(194, 194, 194);
  padding: 0px;
  display: inline-block;
  margin-left: 10px;
}

.pivot-field-setting-control-format {
  width: 100px;
  height: 23px;
  line-height: 21px;
  font-size: 12px;
  background-color: rgb(225, 225, 225);
  border: 1px solid rgb(194, 194, 194);
  padding: 0px;
  display: inline-block;
}

.pivot-field-setting-control:hover {
  border: 1px solid rgb(50, 150, 200);
  background-color: rgba(191, 216, 231, 0.9);
}

.pivot-field-setting-control-format:hover {
  border: 1px solid rgb(50, 150, 200);
  background-color: rgba(191, 216, 231, 0.9);
}

.pivot-field-row-wrap {
  width: 100%;
  margin: 10px 0;
}
.pivot-field-row-wrap > span {
  display: inline-block;
  margin-left: 5px;
  vertical-align: middle;
}
.pivot-field-row-wrap > span > input {
  border: 1px solid #d1d1d1;
  outline: none;
  height: 22px;
  width: 245px;
  box-sizing: border-box;
}

.gc-pivot-field-setting-content {
  padding: 0 12px;
  box-sizing: border-box;
  background-color: rgb(240, 240, 240);
}
.gc-pivot-field-setting-content label {
  display: inline-block;
  padding: 0px;
  vertical-align: middle;
}

.gc-sjs-field-settings-layout-style {
  font-size: 12px;
  font-weight: 700;
}

.gc-sjs-field-settings-layout-line {
  border-bottom: 1px solid #c8c8c8;
  margin: 3px 15px 5px 0px;
}

.gc-sjs-field-settings-layout-showNoData-container {
  background-color: #f0f0f0;
  padding-bottom: 5px;
}
.gc-sjs-field-settings-layout-showNoData-container .gc-sjs-field-settings-layout-showNoData, .gc-sjs-field-settings-layout-showNoData-container .gc-sjs-field-settings-layout-showSubtotalTop {
  margin: 0px;
  appearance: none;
  outline: none;
  display: inline-block;
  width: 13px;
  height: 13px;
  background-color: white;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTRweCIgaGVpZ2h0PSIxNHB4IiB2aWV3Qm94PSIwIDAgMTQgMTQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+Y2hlY2tfZGFyazwvdGl0bGU+CiAgICA8ZyBpZD0iY2hlY2tfZGFyayIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPHBhdGggZD0iTTEzLDEgTDEzLDEzIEwxLDEzIEwxLDEgTDEzLDEgWiBNMTIsMiBMMiwyIEwyLDEyIEwxMiwxMiBMMTIsMiBaIiBpZD0i5b2i54q257uT5ZCIIiBmaWxsPSIjNjk2OTY5Ij48L3BhdGg+CiAgICA8L2c+Cjwvc3ZnPg==);
}
.gc-sjs-field-settings-layout-showNoData-container .gc-sjs-field-settings-layout-showNoData:checked, .gc-sjs-field-settings-layout-showNoData-container .gc-sjs-field-settings-layout-showSubtotalTop:checked {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTRweCIgaGVpZ2h0PSIxNHB4IiB2aWV3Qm94PSIwIDAgMTQgMTQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+Y2hlY2tfZGFyazwvdGl0bGU+CiAgICA8ZyBpZD0iY2hlY2tfZGFyayIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPHBvbHlnb24gaWQ9IlBhdGgtNCIgZmlsbD0iIzY5Njk2OSIgZmlsbC1ydWxlPSJub256ZXJvIiBwb2ludHM9IjExLjA2MDIyNjkgMyAxMiAzLjc1MDMxMDI5IDUuODIxNDMxODEgMTEgMiA3LjE0MDcwNzQzIDIuOTM5NzczMTQgNi4zOTAzOTcxMyA1LjgyMTQzMTgxIDkuMzAwNjA1OCI+PC9wb2x5Z29uPgogICAgICAgIDxwYXRoIGQ9Ik0xMywxIEwxMywxMyBMMSwxMyBMMSwxIEwxMywxIFogTTEyLDIgTDIsMiBMMiwxMiBMMTIsMTIgTDEyLDIgWiIgaWQ9IuW9oueKtue7k+WQiCIgZmlsbD0iIzY5Njk2OSI+PC9wYXRoPgogICAgPC9nPgo8L3N2Zz4=);
}

.pivot-field-summarize-header {
  clear: both;
  width: 100%;
  height: 30px;
}

.pivot-field-summarize-item {
  float: left;
  padding: 0 10px;
  line-height: 30px;
}

.pivot-field-summarize-item.active {
  background-color: white;
}

.pivot-field-summarize-content-header,
.pivot-field-show-value-as-content-header {
  font-weight: 500;
  color: black;
  font-size: 13px;
  padding: 5px 0px;
  margin: 5px 0px;
  border-bottom: 1px solid #ddd;
  width: 320px;
}

.pivot-field-summarize-content-desc {
  height: 40px;
  max-width: 320px;
  margin-bottom: 5px;
}

.pivot-field-summarize-content,
.pivot-field-show-value-as-content {
  background-color: white;
  padding: 10px 10px;
}
.pivot-field-summarize-content input,
.pivot-field-show-value-as-content input {
  outline: none;
  font-size: 12px;
}
.pivot-field-summarize-content select,
.pivot-field-show-value-as-content select {
  outline: none;
  font-size: 12px;
  margin-top: 5px;
  height: 22px;
  margin-bottom: 10px;
  border: 1px solid #d1d1d1;
}

.pivot-field-summarize-content-list {
  overflow-y: auto;
  max-height: 110px;
  padding-left: 0;
  margin: 0;
  border: 1px solid black;
}

.pivot-field-content-item,
.pivot-field-content-base-item-item,
.pivot-field-content-base-field-item,
.pivot-field-content-number-format-item {
  list-style-type: none;
  line-height: 14px;
  height: 18px;
  text-indent: 8px;
  white-space: nowrap;
}

.pivot-field-content-item.active,
.pivot-field-content-base-item-item.active,
.pivot-field-content-base-field-item.active,
.pivot-field-content-number-format-item.active {
  background-color: rgb(0, 120, 215);
  color: white;
  border: 1px dotted #ff8728;
  box-sizing: border-box;
}

.pivot-field-setting-footer {
  position: relative;
  background-color: rgb(240, 240, 240);
  padding: 12px 0px 12px 0;
  clear: both;
  height: 23px;
}

.pivot-field-setting-name-area {
  overflow: hidden;
}

.pivot-field-setting-control-wrap {
  position: absolute;
  right: 12px;
  bottom: 12px;
}

.pivot-field-setting-control-wrap-left {
  margin-left: 12px;
  height: 23px;
}

.gc-panel-view-name {
  float: left;
  width: 85%;
  text-overflow: ellipsis;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  white-space: nowrap;
}

.value-filter::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

.value-filter::-webkit-outer-spin-button {
  -webkit-appearance: none;
}

.pivot-field-content-hide {
  display: none;
}

.pivot-field-show-value-as-content-base-field,
.pivot-field-show-value-as-content-base-item {
  overflow-y: auto;
  padding-left: 0;
  margin: 0;
  border: 1px solid black;
  height: 97px;
}

.pivot-field-show-value-as-content-type-list {
  width: 100%;
  height: 24px;
}

.pivot-field-show-value-as-content-base-field-list {
  display: inline-block;
  width: 49%;
}
.pivot-field-show-value-as-content-base-field-list > div:first-child {
  margin-bottom: 2px;
}

.pivot-field-show-value-as-content-base-item-list {
  display: inline-block;
  width: 49%;
  float: right;
}
.pivot-field-show-value-as-content-base-item-list > div:first-child {
  margin-bottom: 2px;
}

.pivot-field-show-value-as-content-base-field.disable,
.pivot-field-show-value-as-content-base-item.disable {
  color: gray;
}

.pivot-field-number-format-content-list {
  max-height: 143px;
  overflow: auto;
  margin-top: 12px;
  box-sizing: border-box;
  margin-bottom: 0px;
  padding-left: 0px;
  background-color: white;
  border: 1px solid black;
  width: 100%;
}

.pivot-field-number-format-show-value {
  width: 100%;
  height: 22px;
  border: 1px solid #d1d1d1;
  box-sizing: border-box;
}

.pivot-field-number-format-area {
  width: 348px;
}

.pivot-field-number-format-sample-value {
  width: 100%;
  height: 22px;
  box-sizing: border-box;
}

.pivot-field-custom-name {
  font-size: 12px;
}

.gc-pivot-search-box {
  display: flex;
  height: 25px;
  justify-content: center;
  flex-wrap: nowrap;
}
.gc-pivot-search-box .gc-pivot-search-box-input-container {
  display: flex;
  border: 1px solid #c6c6c6;
  width: calc(100% - 40px);
  margin-left: 9px;
  height: 100%;
  flex-wrap: nowrap;
}
.gc-pivot-search-box .gc-pivot-search-box-input-container .gc-pivot-search-box-input {
  display: flex;
  flex-grow: 1;
  border: 0px;
  outline-style: none;
  border-right: 0px;
}
.gc-pivot-search-box .gc-pivot-search-box-input-container .gc-pivot-search-box-icon {
  display: none;
  flex-shrink: 0;
  width: 25px;
  height: 25px;
  border: 0px;
  border-left: 0px;
}

.gc-pivot-search-box-icon-background-clear {
  width: 100%;
  height: 100%;
  margin-top: 4px;
  margin-left: 6px;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjExR/NCNwAAAIJJREFUOE+lkssNgDAMQzsCw3UMxEocGKWDQSLVUj5GJeLwhPyI0x7a9qP/gsoKVFagskIUm3ALp3GKZvX63/q0QIcAlqAMXMcFIQ6z7DouTGLptawkMVmeDJi8BFsGQ0jzUcRyvEla4oLAhvVrveu4IOAdxJOwZPkOylBZgcrv9PYAV9tkcyJlS4sAAAAASUVORK5CYII=);
  background-repeat: no-repeat;
}

.gc-pivot-search-box-icon:hover {
  background-color: rgb(249, 204, 117);
}

.gc-timeline-level-dropdown {
  font-family: "Segoe UI", Calibri, Thonburi, Arial, Verdana, sans-serif, "Mongolian Baiti", "Microsoft Yi Baiti", "Javanese Text";
  width: 120px;
  font-size: 12px;
  border: 1px solid darkgray;
  position: absolute;
  background-color: white;
}

.gc-timeline-level-block {
  height: 25px;
  line-height: 20px;
}

.gc-timeline-level-block:hover {
  background-color: #e9f5ee;
}

.gc-timeline-level-text {
  display: inline-block;
}

.gc-timeline-selected-level {
  display: inline-block;
  width: 35px;
  vertical-align: middle;
  text-align: center;
}

.gcSpread.shape-editor-container {
  font: 14.6667px Calibri, Helvetica Neue, Arial, sans-serif;
  outline: none;
  resize: none;
  box-sizing: content-box;
  position: fixed;
  z-index: -1;
  display: table;
  left: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
}

.gcSpread.shape-editor {
  outline: none;
  resize: none;
  border: none;
  box-sizing: content-box;
  overflow: hidden;
  display: table-cell;
  word-wrap: break-word;
  white-space: pre-wrap;
  text-align: left;
  vertical-align: top;
  float: none;
  -webkit-user-select: text;
  user-select: text;
  max-width: 0px;
}

.gc-defined-column-container {
  --sjs-dc-input-height: 32px;
  --sjs-dc-color: #222222;
  --sjs-dc-color-white: #fff;
  --sjs-dc-background-color: #fcfdfd;
  --sjs-dc-accent: #dbdbdb;
  --sjs-dc-accent-light: rgba(219, 219, 219, 0.5607843137);
  --sjs-dc-accent-heavy: rgba(0, 0, 0, 0.4509803922);
  --sjs-dc-invalid-color: red;
  --sjs-dc-font-size: 13px;
  --sjs-dc-font-family:Segoe UI, Calibri, Thonburi, Arial, Verdana, sans-serif, Mongolian Baiti, Microsoft Yi Baiti, Javanese Text;
  --sjs-dc-hover-background-color:#d3f0e0;
  font-size: var(--sjs-dc-font-size);
  font-family: var(--sjs-dc-font-family);
  color: var(--sjs-dc-color);
  background-color: var(--sjs-dc-background-color);
  --sjs-theme-accent: var(--sjs-dc-accent);
  --sjs-theme-gray-bg: var(--sjs-dc-accent-light);
}
.gc-defined-column-container .gc-defined-column-field-label-validation-container {
  color: var(--sjs-dc-invalid-color);
}
.gc-defined-column-container .gc-defined-column-quieter, .gc-defined-column-container .gc-defined-column-click-quieter, .gc-defined-column-container .gc-defined-column-input-number .gc-defined-column-field-spin-buttons-container div {
  opacity: 0.5;
}
.gc-defined-column-container .gc-defined-column-quieter-light, .gc-defined-column-container .gc-defined-column-input-number .gc-defined-column-field-spin-buttons-container div {
  opacity: 0.75;
}
.gc-defined-column-container .gc-defined-column-unquiet, .gc-defined-column-container .gc-defined-column-click-quieter:hover, .gc-defined-column-container .gc-defined-column-input-number .gc-defined-column-field-spin-buttons-container div:hover {
  opacity: 1;
}
.gc-defined-column-container .gc-defined-column-click, .gc-defined-column-container .gc-defined-column-type-select-container .gc-defined-column-types-container .gc-defined-column-field-type-container, .gc-defined-column-container .gc-defined-column-click-quieter, .gc-defined-column-container .gc-defined-column-input-number .gc-defined-column-field-spin-buttons-container div {
  cursor: pointer;
}
.gc-defined-column-container .gc-defined-column-type-icon-number {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0ibTEzIDQuNjI3LS4yNDYgMS4wNmgtMi42OTNsLS42MzggMi42ODZoMi44NGwtLjI0NiAxLjA0MmgtMi44Nkw4LjI3MyAxM0g2Ljg0OGwuODg0LTMuNTg1SDUuMTE4TDQuMjMzIDEzSDIuODA4bC44ODUtMy41ODVIMWwuMjQ2LTEuMDQyaDIuNzEybC42NC0yLjY4N2gtMi44NmwuMjQ1LTEuMDU5aDIuODZMNS43MTcgMWgxLjQyNmwtLjg3NSAzLjYyN2gyLjYxNEw5Ljc1NyAxaDEuNDI1bC0uODc1IDMuNjI3SDEzWm0tNC4zNjQgMS4wNkg2LjAyMmwtLjYzOSAyLjY4NmgyLjYxNWwuNjM4LTIuNjg3WiIgZmlsbD0iIzY2NiIgZmlsbC1ydWxlPSJub256ZXJvIi8+PC9zdmc+);
}
.gc-defined-column-container .gc-defined-column-type-icon-text {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0ibTUuMTEgOC4xNDguNjYtMS45NTFjLjQ3LTEuMzYuODg2LTIuNjQgMS4zMDMtNC4wNjZoLjA3Yy40MzQgMS40MS44MzQgMi43MDUgMS4zMiA0LjA2NmwuNjYgMS45NUg1LjExWk0xMC43NTcgMTNoMS41MjlMNy45MjUgMUg2LjM0NEwyIDEzaDEuNDQybDEuMzAzLTMuNzg3aDQuNzQzTDEwLjc1NyAxM1oiIGZpbGw9IiM2NjYiIGZpbGwtcnVsZT0ibm9uemVybyIvPjwvc3ZnPg==);
}
.gc-defined-column-container .gc-defined-column-type-icon-formula {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTguODMzIDBjLjQxMSAwIC43NDkuMTA1IDEuMDA4LjMxNi4yNjMuMjE0LjQuNDU3LjQuNzI1YS43NDMuNzQzIDAgMCAxLS4yMTUuNTQzLjc0OC43NDggMCAwIDEtLjU0OC4yMTUuNjk5LjY5OSAwIDAgMS0uNDgxLS4xNjguNTU4LjU1OCAwIDAgMS0uMi0uNDI2YzAtLjA4LjAyMy0uMTY5LjA2Ny0uMjY1bC4xMDItLjE5NGEuNzMuNzMgMCAwIDAgLjAyOS0uMDYxYzAtLjAyNS0uMDA1LS4wMzUtLjAwOS0uMDRDOC45NTMuNjIgOC44OS42MDUgOC43OTYuNjA1Yy0uMjg2IDAtLjUzOC4wODYtLjc2MS4yNjEtLjMxNi4yNDUtLjYwNi42MzYtLjg2NSAxLjE3MmwtLjA2NS4xNDZjLS4xMTkuMjktLjI5Ny44MDItLjUzIDEuNTI4bC0uMDg2LjI3aDEuMTA4bC0uMTk0LjY3My0uMTQxLjEwNi0uOTgtLjAwMS0uODA0IDMuMTA0Yy0uMzM2IDEuMjkxLS42NzMgMi4zMjEtMS4wMTIgMy4wOTJsLS4xMDEuMjIzYy0uNTQxIDEuMTEzLTEuMTIzIDEuODg0LTEuNzUgMi4zMS0uNDg0LjM0LS45NzQuNTEyLTEuNDY4LjUxMi0uMzQgMC0uNjMtLjEtLjg1Ni0uMjk3YS43Mi43MiAwIDAgMS0uMjkxLS41OWMwLS4xOTkuMDgyLS4zNzUuMjQtLjUyYS44MTMuODEzIDAgMCAxIC41NjItLjIxYy4xNyAwIC4zMi4wNTMuNDQyLjE2LjEyNC4xMDkuMTkuMjQ0LjE5LjM5OGEuNTA1LjUwNSAwIDAgMS0uMTM3LjM0bC0uMTIxLjExMi4wMTkuMDA2LjA3NC4wMDZjLjE5MiAwIC4zOTUtLjA2LjYxLS4xODMuMjE2LS4xMi40MTItLjMwNC41ODktLjU1LjE3Ni0uMjQ3LjM0OC0uNjEzLjUxMy0xLjA5MmwuMTQtLjQ4Yy4wOTktLjM1NC4yNC0uODc0LjQxOC0xLjU0OUw0LjggNC43NkgzLjVsLjE0LS42NjEuMTQ0LS4xMTdjLjQwMiAwIC42OC0uMDI3LjgyNi0uMDc4QS45Mi45MiAwIDAgMCA1IDMuNjJjLjEyNi0uMTQ1LjMwMS0uNDM1LjUyMS0uODY0LjI5Ni0uNTkuNTg2LTEuMDU0Ljg3LTEuMzg4LjM4LS40NTQuNzg0LS43OTUgMS4yMTItMS4wMjMuNDMtLjIzLjg0LS4zNDUgMS4yMjktLjM0NVptNC4zMTkgMy44Yy4yNDYgMCAuNDUxLjA2OS42MDguMjEuMTU4LjE0Mi4yNC4zMTkuMjQuNTE5IDAgLjIyNi0uMDQ4LjM5LS4xNjEuNDk1YS44NDYuODQ2IDAgMCAxLS41NzQuMjE5Yy0uMDg1IDAtLjE3Mi0uMDExLS4yNi0uMDMybC0uMzI3LS4xMDFhMS4wMjIgMS4wMjIgMCAwIDAtLjIyOC0uMDVjLS4wODQgMC0uMTk5LjA1NC0uMzM2LjE2Ny0uMjY1LjIxOC0uNTg0LjYyOC0uOTU1IDEuMjMybC0uMDk1LjE1OC42MDQgMi40NzJjLjA3Ny4zMi4xNDQuNTI5LjE5LjYxbC4wMTguMDI3Yy4wNC4wNDcuMDY2LjA2My4wOC4wNjNhLjM3Mi4zNzIgMCAwIDAgLjE4My0uMDdsLjA5Ny0uMDdjLjI1Ny0uMjEuNDg3LS40OTMuNjc2LS44MzRsLjE5Mi0uMDYuMjYyLjEyNS4wNjQuMjA0YTQuODQzIDQuODQzIDAgMCAxLTEuMjQ1IDEuNDg4Yy0uMjk3LjIzMS0uNTU4LjM1LS43OS4zNS0uMzQgMC0uNjEtLjE4My0uODAyLS41MzItLjA4Mi0uMTUtLjIxMy0uNTk4LS40MDMtMS4zNzNsLS4xODUtLjc4NC0uMTE5LjE5NmMtLjU5Ni45NzItMS4wOTcgMS42NC0xLjUwNyAyLjAxbC0uMS4wODZjLS4zMy4yNjItLjY1Ny4zOTctLjk4LjM5N2EuOTg4Ljk4OCAwIDAgMS0uNjUxLS4yNDkuNzEzLjcxMyAwIDAgMS0uMjI1LS41NDcuNzAzLjcwMyAwIDAgMSAuNzE3LS43MTFjLjE3IDAgLjMzOS4wNjUuNTEuMTlsLjEwMi4wODNjLjEwNS4wOTMuMTguMTM2LjIwMS4xMzYuMDIgMCAuMDctLjAyNS4xNDMtLjA4NS4xOTUtLjE1NS40NjItLjQ4Mi44MDEtLjk4MWwuMzM4LS41MTZjLjI4OC0uNDUuNDc4LS43NjYuNTY4LS45bC0uNDkxLTEuODc2Yy0uMTEtLjI5Mi0uMjQ3LS40OTItLjQwNS0uNjAyLS4xNTgtLjExLS40LS4xNjktLjczLS4xNjktLjEyOSAwLS4yNTkuMDAzLS4zOTUuMDFsLS4xNTMtLjE0OHYtLjI2NWwuMTIyLS4xNDUgMi4wMTEtLjM0NS4xMzEuMDQzYy4yNTEuMjY0LjQ0NS41MzMuNTc1Ljc5OS4wNTQuMTAzLjEyMy4yODcuMjEuNTU4bC4xNjYuNTU2LjQ4OC0uNzExYTMuOTYgMy45NiAwIDAgMSAuNDcyLS41MzNsLjE4Ni0uMTY3Yy4yNi0uMjIzLjQ5NS0uMzc1LjY5OC0uNDUyYTEuMDggMS4wOCAwIDAgMSAuNDY0LS4wOTVaIiBmaWxsPSIjNjY2IiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48L3N2Zz4=);
}
.gc-defined-column-container .gc-defined-column-type-icon-checkbox {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0ibTExLjggMi0uOSAxSDJ2OWg5VjcuNTc4bDEtMS4xMTFWMTNIMVYyaDEwLjhabTEuMTM2IDAgLjg5Ny43ODhMNi45ODEgMTAuNCAzLjMzMyA2LjM0OGwuODk3LS43ODggMi43NTEgMy4wNTZMMTIuOTM2IDJaIiBmaWxsPSIjNjY2IiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48L3N2Zz4=);
}
.gc-defined-column-container .gc-defined-column-type-icon-date {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTYgMHYxaDNWMGgxdjFoNHYxMkgxVjFoNFYwaDFabTcgNUgydjdoMTFWNVptLTMgMXYuMzUxTDcuMzkgMTFINi4xOGwyLjQzMi00SDZWNmg0Wk01IDJIMnYyaDExVjJoLTN2MUg5VjJINnYxSDVWMloiIGZpbGw9IiM2NjYiIGZpbGwtcnVsZT0ibm9uemVybyIvPjwvc3ZnPg==);
}
.gc-defined-column-container .gc-defined-column-type-icon-currency {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTcuNSAwYS41LjUgMCAwIDEgLjUuNWwtLjAwMS45OTkuNTAxLjAwMWEzIDMgMCAwIDEgMyAzIC41LjUgMCAxIDEtMSAwIDIgMiAwIDAgMC0yLTJINi4yNWEyIDIgMCAxIDAgMCA0SDlhMyAzIDAgMCAxIDAgNmwtMS4wMDEtLjAwMUw4IDEzLjVhLjUuNSAwIDEgMS0xIDBsLS4wMDEtMS4wMDFMNiAxMi41YTMgMyAwIDAgMS0zLTMgLjUuNSAwIDEgMSAxIDAgMiAyIDAgMCAwIDIgMmgzYTIgMiAwIDEgMCAwLTRINi4yNWEzIDMgMCAxIDEgMC02bC43NDktLjAwMUw3IC41YS41LjUgMCAwIDEgLjUtLjVaIiBmaWxsPSIjNjY2IiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48L3N2Zz4=);
}
.gc-defined-column-container .gc-defined-column-type-icon-percent {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEgNC4xMDFjMC0uOTQ5LjI0LTEuNzAzLjcyLTIuMjYyQzIuMiAxLjI3OSAyLjg0NyAxIDMuNjYgMWMuNzggMCAxLjM5LjI1NyAxLjgyNy43NzIuNDM3LjUxNC42NTYgMS4yMy42NTYgMi4xNSAwIC45MTMtLjI0MSAxLjY1NS0uNzI0IDIuMjI0LS40ODIuNTctMS4xMjMuODU0LTEuOTIyLjg1NC0uNzU3IDAtMS4zNjItLjI2NS0xLjgxNi0uNzk0QzEuMjI3IDUuNjc2IDEgNC45NzUgMSA0LjEwMVptLjg1Ny0uMDY2YzAgLjY3NC4xNTMgMS4xOTQuNDYgMS41Ni4zMDYuMzY1LjcyNC41NDggMS4yNTQuNTQ4LjU0NyAwIC45Ny0uMTkgMS4yNjgtLjU2OC4yOTgtLjM3OS40NDctLjkxMy40NDctMS42MDIgMC0uNjc0LS4xNDctMS4xOTUtLjQ0LTEuNTYzLS4yOTItLjM2OS0uNzAzLS41NTMtMS4yMzQtLjU1My0uNTQgMC0uOTY5LjE5Mi0xLjI4My41NzYtLjMxNS4zODQtLjQ3Mi45MTgtLjQ3MiAxLjYwMlptOS4yNjEtMi44NjJMMy44OTEgMTIuODk1SDIuNzI5TDkuOTU2IDEuMTczaDEuMTYyWm0tMy4yNiA4LjkzOWMwLS45NDUuMjQtMS43LjcyLTIuMjY1QzkuMDU4IDcuMjgyIDkuNzA0IDcgMTAuNTE0IDdjLjc3NiAwIDEuMzg1LjI1NyAxLjgyNS43NzIuNDQuNTE1LjY2MSAxLjI0LjY2MSAyLjE3NiAwIC45MDUtLjI0NCAxLjY0LS43MzIgMi4yMDUtLjQ4Ny41NjUtMS4xMjQuODQ3LTEuOTEuODQ3LS43NjMgMC0xLjM3LS4yNjctMS44MjItLjgwMi0uNDUzLS41MzUtLjY3OS0xLjIzLS42NzktMi4wODZabS44NTYtLjA2OWMwIC42Ni4xNTMgMS4xNzYuNDYgMS41NDUuMzA2LjM3LjcyNC41NTUgMS4yNTUuNTU1LjU0NiAwIC45NjktLjE5MSAxLjI2Ny0uNTc0LjI5OC0uMzgzLjQ0Ny0uOTE4LjQ0Ny0xLjYwNCAwLS42ODItLjE1LTEuMjA0LS40NDctMS41NjUtLjI5OC0uMzYyLS43MDctLjU0My0xLjIyNi0uNTQzLS41NTggMC0uOTkuMTkzLTEuMjk2LjU3OC0uMzA3LjM4NS0uNDYuOTIxLS40NiAxLjYwOFoiIGZpbGw9IiM2NjYiIGZpbGwtcnVsZT0ibm9uemVybyIvPjwvc3ZnPg==);
}
.gc-defined-column-container .gc-defined-column-type-icon-phone {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkuNjE1IDkuNDU3Yy0uMTgxLjE2Ny0uNDQuNDU0LS43NjguODU0bC0uMjg0LjM0NS0uMzg3LS4yMjRjLTEuODQ4LTEuMDc0LTMuMzM4LTIuNjU2LTQuMTgtNC40OWwtLjE1OC0uMzQ0LjI3Ni0uMjU5Yy4zNzctLjM1NC42NTMtLjYzMy44MjMtLjgzMS4wMzEtLjAzNi4wNjEtLjA3Mi4wOS0uMTFhNy40NTEgNy40NTEgMCAwIDEtLjI2OS0yLjE2IDEuMDM1IDEuMDM1IDAgMCAwLS40MjEtLjExbC0yLjE3OS0uMDc2Yy0uMDg2LS4wMDMtLjA5Ny4wMDctLjEuMDkzQTEwLjA2IDEwLjA2IDAgMCAwIDExLjc3IDEyLjU2Yy4wODIuMDAzLjA5Ny0uMDExLjEtLjA5NGwuMDc0LTIuMTJhMi42ODMgMi42ODMgMCAwIDAtLjAwMi0uMzQxYy0uMDE1LS4xODMtLjAwNy0uMTctLjA0LS4xNy0uNzQ0LjAyNy0xLjQ0OC0uMTE3LTIuMjQ3LS40MTVhMi4yNTIgMi4yNTIgMCAwIDAtLjA0LjAzN1ptLTEuMjg2LS4xNjRjLjIyNC0uMjYuNDE0LS40NjIuNTcxLS42MDguMzgyLS4zNTQuNjQ4LS41MDcuOTkzLS4zLjczNi4yODcgMS4zNDcuNDIyIDEuOTY3LjQuNzI1LS4wMzIgMS4wNzMuNDU3IDEuMTMgMS4xMzEuMDEzLjE0Ni4wMTIuMjYuMDA1LjQ2NmwtLjA3NCAyLjEyMWMtLjAyMy42NjUtLjUyNyAxLjEzMS0xLjE4OCAxLjEwOEExMS4xMTEgMTEuMTExIDAgMCAxIDEuMDA3IDIuMTA5QzEuMDMgMS40NDIgMS41MjcuOTc4IDIuMTk1IDFsMi4xNzguMDc2Yy43NDIuMDI2IDEuNDY0LjM5IDEuNDM5IDEuMTE3LS4wMjQuNzA1LjA3IDEuNDEuMjc1IDIuMDg0LjEyNi40MS0uMTEzLjc0LS45NzIgMS41Ni43MDkgMS4zNjggMS44MzUgMi41NyAzLjIxNCAzLjQ1NVoiIGZpbGw9IiM2NjYiIGZpbGwtcnVsZT0ibm9uemVybyIvPjwvc3ZnPg==);
}
.gc-defined-column-container .gc-defined-column-type-icon-email {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEyIDJhMiAyIDAgMCAxIDIgMnY3YTIgMiAwIDAgMS0yIDJIMmEyIDIgMCAwIDEtMi0yVjRhMiAyIDAgMCAxIDItMmgxMFptLjk2NiAxLjc0MUw4LjQxNCA4LjI5M2EyIDIgMCAwIDEtMi44MjggMEwxLjAzNCAzLjc0QzEuMDEyIDMuODI0IDEgMy45MTEgMSA0djdhMSAxIDAgMCAwIDEgMWgxMGExIDEgMCAwIDAgMS0xVjRjMC0uMDktLjAxMi0uMTc2LS4wMzQtLjI1OVpNMTIgM0gyYy0uMDkgMC0uMTc2LjAxMi0uMjU5LjAzNGw0LjU1MiA0LjU1MmExIDEgMCAwIDAgMS40MTQgMGw0LjU1Mi00LjU1MkExLjAwMSAxLjAwMSAwIDAgMCAxMiAzWiIgZmlsbD0iIzY2NiIgZmlsbC1ydWxlPSJub256ZXJvIi8+PC9zdmc+);
}
.gc-defined-column-container .gc-defined-column-type-icon-url {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTYuMDg1IDcuOTE1YS42MzIuNjMyIDAgMCAxIDAgLjkyMi42NTQuNjU0IDAgMCAxLS45MjIgMCAzLjI0OCAzLjI0OCAwIDAgMSAwLTQuNTlMNy40NiAxLjk1YTMuMjQ4IDMuMjQ4IDAgMCAxIDQuNTkgMCAzLjI0OCAzLjI0OCAwIDAgMSAwIDQuNTlsLS45NjguOTY3YTQuNDg0IDQuNDg0IDAgMCAwLS4yNi0xLjU3bC4zMDYtLjMxMmExLjkzNiAxLjkzNiAwIDAgMCAwLTIuNzUzIDEuOTM2IDEuOTM2IDAgMCAwLTIuNzUzIDBMNi4wODUgNS4xNjNhMS45MzYgMS45MzYgMCAwIDAgMCAyLjc1Mm0xLjgzLTIuNzUyYS42NTQuNjU0IDAgMCAxIC45MjIgMCAzLjI0OCAzLjI0OCAwIDAgMSAwIDQuNTlMNi41NCAxMi4wNWEzLjI0OCAzLjI0OCAwIDAgMS00LjU5IDAgMy4yNDggMy4yNDggMCAwIDEgMC00LjU5bC45NjgtLjk2N2E0LjU0NSA0LjU0NSAwIDAgMCAuMjYgMS41NzdsLS4zMDYuMzA1YTEuOTM2IDEuOTM2IDAgMCAwIDAgMi43NTNjLjc2Ljc2NiAxLjk5My43NjYgMi43NTMgMGwyLjI5MS0yLjI5MmExLjkzNiAxLjkzNiAwIDAgMCAwLTIuNzUyLjYzMi42MzIgMCAwIDEgMC0uOTIyWiIgZmlsbD0iIzY2NiIgZmlsbC1ydWxlPSJub256ZXJvIi8+PC9zdmc+);
}
.gc-defined-column-container .gc-defined-column-type-icon-lookup {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEwLjExIDVhMy4xMSAzLjExIDAgMCAxIDMuMTEgMy4xMWMuMDAxLjY5LS4yMyAxLjM2Mi0uNjU3IDEuOTA1bDEuMzIyIDEuMzE4YS4zODkuMzg5IDAgMCAxIDAgLjU1Mi4zODkuMzg5IDAgMCAxLS41NTIgMGwtMS4zMTgtMS4zMjJhMy4wNzkgMy4wNzkgMCAwIDEtMS45MDUuNjU3IDMuMTEgMy4xMSAwIDEgMSAwLTYuMjJaTTYgMTB2MUgwdi0xaDZabTQuMTEtNC4yMjJhMi4zMzMgMi4zMzMgMCAxIDAgMCA0LjY2NSAyLjMzMyAyLjMzMyAwIDAgMCAwLTQuNjY1Wk02IDYuNXYxSDB2LTFoNlpNMTEgM3YxSDBWM2gxMVoiIGZpbGw9IiM2NjYiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvc3ZnPg==);
}
.gc-defined-column-container .gc-defined-column-type-icon-createdtime {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEwLjUgN2EzLjUgMy41IDAgMSAxIDAgNyAzLjUgMy41IDAgMCAxIDAtN1pNNSAwdjFoM1YwaDF2MWg0djUuNzU4YTQuNDg0IDQuNDg0IDAgMCAwLS45OTktLjUwMkwxMiA1SDF2N2w1LjI1Ni4wMDFjLjEyNi4zNTYuMjk2LjY5MS41MDIgMUwwIDEzVjFoNFYwaDFabTYgOGgtMXYySDh2MWgydjJoMXYtMmgydi0xaC0yVjhaTTQgMkgxdjJoMTFWMkg5djFIOFYySDV2MUg0VjJaIiBmaWxsPSIjNjY2IiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48L3N2Zz4=);
}
.gc-defined-column-container .gc-defined-column-type-icon-modifiedtime {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEwLjUgN2MxLjkzNSAwIDMuNSAxLjU2NSAzLjUgMy41UzEyLjQzNSAxNCAxMC41IDE0QTMuNDk3IDMuNDk3IDAgMCAxIDcgMTAuNUM3IDguNTY1IDguNTY1IDcgMTAuNSA3Wk01IDB2MWgzVjBoMXYxaDR2NS43NThhNC40ODQgNC40ODQgMCAwIDAtLjk5OS0uNTAyTDEyIDVIMXY3bDUuMjU2LjAwMWMuMTI2LjM1Ni4yOTYuNjkxLjUwMiAxTDAgMTNWMWg0VjBoMVptNS44NTUgOS4xMDVMOC40IDExLjU2di44MzVoLjgzNUwxMS42OSA5Ljk0bC0uODM1LS44MzVabS43OTgtLjcwNWEuMjA1LjIwNSAwIDAgMC0uMTUzLjA2NWwtLjQwNi40MDUuODMuODMuNDA2LS40MDVhLjIxNC4yMTQgMCAwIDAgMC0uMzE2bC0uNTE0LS41MTRhLjIzNy4yMzcgMCAwIDAtLjE2My0uMDY1Wk00IDJIMXYyaDExVjJIOXYxSDhWMkg1djFINFYyWiIgZmlsbD0iIzY2NiIgZmlsbC1ydWxlPSJub256ZXJvIi8+PC9zdmc+);
}
.gc-defined-column-container .gc-defined-column-type-icon-attachment {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEwLjA0NSAzLjE4MlYxMC41YTIuNTQ1IDIuNTQ1IDAgMSAxLTUuMDkgMFYyLjU0NWExLjU5IDEuNTkgMCAwIDEgMy4xODEgMHY2LjY4MmEuNjM2LjYzNiAwIDAgMS0xLjI3MiAwVjMuMTgyaC0uOTU1djYuMDQ1YTEuNTkgMS41OSAwIDEgMCAzLjE4MiAwVjIuNTQ1YTIuNTQ1IDIuNTQ1IDAgMSAwLTUuMDkxIDBWMTAuNWEzLjUgMy41IDAgMCAwIDcgMFYzLjE4MmgtLjk1NVoiIGZpbGw9IiM2NjYiIGZpbGwtcnVsZT0ibm9uemVybyIvPjwvc3ZnPg==);
}
.gc-defined-column-container .gc-defined-column-type-icon-select {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEgMi41aDh2MUgxdi0xWm05LjA2NC0uMDU3IDEuMjQxIDEuNDY2YS4yNTMuMjUzIDAgMCAwIC4zNjUuMDI1bC4wMjUtLjAyNSAxLjI0MS0xLjQ2NmEuMjc0LjI3NCAwIDAgMC0uMDI0LS4zNzcuMjU0LjI1NCAwIDAgMC0uMTctLjA2NmgtMi40ODNhLjI2My4yNjMgMCAwIDAtLjI1OS4yNjdjMCAuMDY1LjAyMy4xMjguMDY0LjE3NlpNMSA2LjVoOHYxSDF2LTFabTkuMDY0LS4wNTcgMS4yNDEgMS40NjZhLjI1My4yNTMgMCAwIDAgLjM2NS4wMjVsLjAyNS0uMDI1IDEuMjQxLTEuNDY2YS4yNzQuMjc0IDAgMCAwLS4wMjQtLjM3Ny4yNTQuMjU0IDAgMCAwLS4xNy0uMDY2aC0yLjQ4M2EuMjYzLjI2MyAwIDAgMC0uMjU5LjI2N2MwIC4wNjUuMDIzLjEyOC4wNjQuMTc2Wk0xIDEwLjVoOHYxSDF2LTFabTkuMDY0LS4wNTcgMS4yNDEgMS40NjZhLjI1My4yNTMgMCAwIDAgLjM2NS4wMjVsLjAyNS0uMDI1IDEuMjQxLTEuNDY2YS4yNzQuMjc0IDAgMCAwLS4wMjQtLjM3Ny4yNTQuMjU0IDAgMCAwLS4xNy0uMDY2aC0yLjQ4M2EuMjYzLjI2MyAwIDAgMC0uMjU5LjI2N2MwIC4wNjUuMDIzLjEyOC4wNjQuMTc2WiIgZmlsbD0iIzY2NiIgZmlsbC1ydWxlPSJldmVub2RkIi8+PC9zdmc+);
}
.gc-defined-column-container .gc-defined-column-type-icon-barcode {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTIgOXYzaDN2MUgyYTEgMSAwIDAgMS0xLTFWOWgxWm0xMSAwdjNhMSAxIDAgMCAxLTEgMUg5di0xaDNWOWgxWk00IDh2M0gzVjhoMVptNSAwdjNIOFY4aDFabTIgMHYzaC0xVjhoMVpNNyA4djNINVY4aDJabTUuMDUzLTEuMzE2di42MzJIMS45NDd2LS42MzJoMTAuMTA2Wk00IDN2M0gzVjNoMVptNSAwdjNIOFYzaDFabTIgMHYzaC0xVjNoMVpNNyAzdjNINVYzaDJabTUtMmExIDEgMCAwIDEgMSAxdjNoLTFWMkg5VjFoM1pNNSAxdjFIMnYzSDFWMmExIDEgMCAwIDEgMS0xaDNaIiBmaWxsPSIjNjY2IiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48L3N2Zz4=);
}
.gc-defined-column-container .gc-defined-column-type-icon-x {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbD0iIzk5OSIgZmlsbC1ydWxlPSJub256ZXJvIiBkPSJNMTIgMy4wMDcgMTAuOTkzIDIgNyA1Ljk5MyAzLjAwNyAyIDIgMy4wMDcgNS45OTMgNyAyIDEwLjk5MyAzLjAwNyAxMiA3IDguMDA3IDEwLjk5MyAxMiAxMiAxMC45OTMgOC4wMDcgN3oiLz48L3N2Zz4=);
}
.gc-defined-column-container .gc-defined-column-type-icon-chevrondown {
  width: 16px;
  height: 16px;
  background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgLTk2MCA5NjAgOTYwIiB3aWR0aD0iMjAiPjxwYXRoIGQ9Ik00ODAtMzMzIDI0MC01NzNsNTEtNTEgMTg5IDE4OSAxODktMTg5IDUxIDUxLTI0MCAyNDBaIi8+PC9zdmc+);
}
.gc-defined-column-container .gc-defined-column-type-icon-magnifyingglass {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEzLjgyMiAxMy4xMTRjLjIxOS4yMi4yMzguNTU1LjA0My43NS0uMTk2LjE5Ni0uNTMxLjE3Ny0uNzUtLjA0MmwtMy4yMzctMy4yMzZhNS45NzYgNS45NzYgMCAwIDEtMy44NzQgMS40MjIgNi4wMDQgNi4wMDQgMCAxIDEgNi4wMDQtNi4wMDQgNS45NzQgNS45NzQgMCAwIDEtMS40MjIgMy44NzRsMy4yMzYgMy4yMzZabS03LjgxOC0yLjEwN2E1LjAwMyA1LjAwMyAwIDEgMCAwLTEwLjAwNiA1LjAwMyA1LjAwMyAwIDAgMCAwIDEwLjAwNloiIGZpbGw9IiM2NjYiIGZpbGwtcnVsZT0ibm9uemVybyIvPjwvc3ZnPg==);
}
.gc-defined-column-container .gc-defined-column-hidden {
  display: none;
}
.gc-defined-column-container .gc-defined-column-focus {
  border: 1px solid #166ee1;
}
.gc-defined-column-container .gc-defined-column-disabled {
  opacity: 0.6;
  pointer-events: none;
  cursor: default;
}
.gc-defined-column-container .gc-defined-column-disabled input:disabled {
  background-color: inherit;
}
.gc-defined-column-container .gc-defined-column-hide {
  display: none;
}
.gc-defined-column-container input:disabled.gc-defined-column-disabled {
  background-color: inherit;
}
.gc-defined-column-container .gc-defined-column-content-container {
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 0.5rem;
  padding-top: 0.5rem;
}
.gc-defined-column-container .gc-defined-column-field-container {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.gc-defined-column-container .gc-defined-column-field-container.gc-define-column-invalid {
  box-shadow: 0 0 1px rgba(255, 0, 0, 0.32), 0 0 2px rgba(255, 0, 0, 0.08), 0 1px 3px rgba(255, 0, 0, 0.08);
}
.gc-defined-column-container .gc-defined-column-field-input-container-input {
  height: var(--sjs-dc-input-height);
  outline: 0;
  color: #444;
  width: 100%;
  transition: 0.085s all ease-in;
  box-sizing: border-box;
  padding: 0.5rem;
  font-size: var(--sjs-dc-font-size);
  font-family: var(--sjs-dc-font-family);
  line-height: var(--sjs-dc-input-height);
  border: 1px solid #DAE2ED;
}
.gc-defined-column-container .gc-defined-column-field-input-container-input.gc-define-column-invalid {
  border: 1px solid var(--sjs-dc-invalid-color);
}
.gc-defined-column-container .gc-defined-column-field-input-container-input.gc-define-column-invalid:focus {
  border: 1px solid var(--sjs-dc-invalid-color);
}
.gc-defined-column-container .gc-defined-column-field-label-container-label {
  display: block;
  width: 14px;
  height: 14px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  cursor: inherit;
}
.gc-defined-column-container .gc-defined-column-type-select-container {
  width: 100%;
  transition: 0.085s all ease-in;
  box-sizing: border-box;
  margin-top: 0.5rem;
  border: 1px solid #DAE2ED;
}
.gc-defined-column-container .gc-defined-column-type-select-container.gc-define-column-invalid {
  border: 1px solid var(--sjs-dc-invalid-color);
}
.gc-defined-column-container .gc-defined-column-type-select-container .gc-defined-column-field-label-validation-container {
  position: absolute;
}
.gc-defined-column-container .gc-defined-column-type-select-container .gc-defined-column-field-item-container.gc-defined-column-input {
  background-color: #fff;
}
.gc-defined-column-container .gc-defined-column-type-select-container .gc-defined-column-field-container {
  height: var(--sjs-dc-input-height);
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  display: flex;
  align-items: center;
  box-shadow: initial;
  margin: 0;
}
.gc-defined-column-container .gc-defined-column-type-select-container .gc-defined-column-field-container .gc-defined-column-field-input-container {
  width: 100%;
}
.gc-defined-column-container .gc-defined-column-type-select-container .gc-defined-column-field-container .gc-defined-column-field-input-container-input {
  width: 100%;
  border: 0px;
  box-shadow: initial;
}
.gc-defined-column-container .gc-defined-column-type-select-container .gc-defined-column-field-container .gc-defined-column-field-input-container-input[readonly=true] {
  cursor: inherit;
}
.gc-defined-column-container .gc-defined-column-type-select-container .gc-defined-column-types-container {
  overflow: auto;
  max-height: 360px;
}
.gc-defined-column-container .gc-defined-column-type-select-container .gc-defined-column-types-container .gc-defined-column-field-type-container {
  display: flex;
  align-items: center;
  padding: 0.5rem;
}
.gc-defined-column-container .gc-defined-column-type-select-container .gc-defined-column-types-container .gc-defined-column-field-type-container:hover {
  background-image: none;
  background-color: var(--sjs-dc-hover-background-color);
}
.gc-defined-column-container .gc-defined-column-type-select-container .gc-defined-column-types-container .gc-defined-column-field-type-container .gc-defined-column-field-type-icon {
  display: block;
  width: 14px;
  height: 14px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  cursor: inherit;
}
.gc-defined-column-container .gc-defined-column-type-select-container .gc-defined-column-types-container .gc-defined-column-field-type-container .gc-defined-column-field-type-text {
  margin-left: 0.5rem;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--sjs-dc-color);
  fill: var(--sjs-dc-color);
}
.gc-defined-column-container .gc-defined-column-select-container {
  position: relative;
  width: 100%;
  transition: 0.085s all ease-in;
  box-sizing: border-box;
  margin-top: 0.5rem;
}
.gc-defined-column-container .gc-defined-column-select-container .gc-defined-column-field-container {
  height: var(--sjs-dc-input-height);
  padding-left: 0.5rem;
  padding-right: 0.2rem;
  display: flex;
  align-items: center;
  box-shadow: initial;
  margin: 0;
}
.gc-defined-column-container .gc-defined-column-select-container .gc-defined-column-field-container .gc-defined-column-field-input-container {
  width: 100%;
}
.gc-defined-column-container .gc-defined-column-select-container .gc-defined-column-field-container .gc-defined-column-field-input-container-input {
  width: 100%;
  border: 0px;
  box-shadow: initial;
}
.gc-defined-column-container .gc-defined-column-select-container .gc-defined-column-field-container .gc-defined-column-field-input-container-input[readonly=true] {
  cursor: inherit;
}
.gc-defined-column-container .gc-defined-column-select-container .gc-defined-column-field-container .gc-defined-column-field-input-container-input[readonly=true]::placeholder {
  color: var(--sjs-dc-color);
  fill: var(--sjs-dc-color);
}
.gc-defined-column-container .gc-defined-column-select-container .gc-defined-column-select-list-container {
  position: absolute;
  z-index: 1005;
  background-color: var(--sjs-dc-background-color);
  width: 100%;
  top: calc(var(--sjs-dc-input-height) + 4px);
  border: 1px solid #DAE2ED;
  box-sizing: border-box;
  overflow: auto;
  max-height: 235px;
}
.gc-defined-column-container .gc-defined-column-select-container .gc-defined-column-select-list-container .gc-defined-column-select-item-container {
  display: flex;
  cursor: pointer;
  padding: 0.5rem;
}
.gc-defined-column-container .gc-defined-column-select-container .gc-defined-column-select-list-container .gc-defined-column-select-item-container:hover {
  background-image: none;
  background-color: var(--sjs-dc-hover-background-color);
}
.gc-defined-column-container .gc-defined-column-select-container .gc-defined-column-select-list-container .gc-defined-column-select-item-container.gc-defined-column-list-item-selected {
  background-color: var(--sjs-dc-hover-background-color);
}
.gc-defined-column-container .gc-defined-column-select-container .gc-defined-column-select-list-container .gc-defined-column-select-item-container .gc-defined-column-select-item-icon {
  display: block;
  width: 14px;
  height: 14px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  cursor: inherit;
}
.gc-defined-column-container .gc-defined-column-select-container .gc-defined-column-select-list-container .gc-defined-column-select-item-container .gc-defined-column-select-item-text {
  margin-left: 0.5rem;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--sjs-dc-color);
  fill: var(--sjs-dc-color);
}
.gc-defined-column-container .gc-defined-column-select-container .gc-defined-column-select-list-container .gc-defined-column-select-item-container.gc-defined-column-select-no-results {
  cursor: default;
  opacity: 0.7;
  flex-direction: column;
  align-items: center;
}
.gc-defined-column-container .gc-defined-column-select-container .gc-defined-column-select-list-container .gc-defined-column-select-item-container.gc-defined-column-select-no-results:hover {
  background-color: inherit;
}
.gc-defined-column-container .gc-defined-column-select-container .gc-defined-column-select-list-container .gc-defined-column-select-item-container.gc-defined-column-select-no-results.gc-defined-column-list-item-selected {
  background-color: inherit;
}
.gc-defined-column-container .gc-defined-column-input-number .gc-defined-column-field-container {
  display: flex;
  align-items: center;
}
.gc-defined-column-container .gc-defined-column-input-number .gc-defined-column-field-label-container-label {
  width: initial;
}
.gc-defined-column-container .gc-defined-column-input-number .gc-defined-column-field-input-container {
  flex-grow: 1;
}
.gc-defined-column-container .gc-defined-column-input-number .gc-defined-column-field-spin-buttons-container {
  width: 18px;
  height: var(--sjs-dc-input-height);
  display: flex;
  flex-direction: column;
}
.gc-defined-column-container .gc-defined-column-input-number .gc-defined-column-field-spin-buttons-container div {
  height: 50%;
}
.gc-defined-column-container .gc-defined-column-input-number .gc-defined-column-field-spin-buttons-container .gc-defined-column-field-spin-buttons-container-increase::before {
  content: "";
  background-position: center center;
  display: block;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
}
.gc-defined-column-container .gc-defined-column-input-number .gc-defined-column-field-spin-buttons-container .gc-defined-column-field-spin-buttons-container-decrease::after {
  content: "";
  background-position: center center;
  display: block;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
}
.gc-defined-column-container .gc-defined-column-checkbox-container .gc-defined-column-checkbox-item-container {
  display: flex;
  width: fit-content;
}
.gc-defined-column-container .gc-defined-column-checkbox-container .gc-defined-column-checkbox-item-container:focus {
  outline: none;
}
.gc-defined-column-container .gc-defined-column-checkbox-container .gc-defined-column-check-container {
  align-self: center;
}
.gc-defined-column-container .gc-defined-column-checkbox-container .gc-defined-column-check-uncheck {
  width: 15px;
  height: 15px;
  display: none;
  background-repeat: no-repeat;
}
.gc-defined-column-container .gc-defined-column-checkbox-container .gc-defined-column-check-check {
  width: 15px;
  height: 15px;
  display: none;
  background-repeat: no-repeat;
}
.gc-defined-column-container .gc-defined-column-checkbox-container .gc-defined-column-check-indeterminate {
  width: 15px;
  height: 15px;
  display: none;
  background-repeat: no-repeat;
}
.gc-defined-column-container .gc-defined-column-checkbox-container .gc-defined-column-label-container {
  margin-left: 0.5rem;
}
.gc-defined-column-container .gc-defined-column-switch-container .gc-defined-column-switch-item-container {
  display: flex;
  width: fit-content;
}
.gc-defined-column-container .gc-defined-column-switch-container .gc-defined-column-switch-container {
  align-self: center;
}
.gc-defined-column-container .gc-defined-column-switch-container .gc-defined-column-label-container {
  margin-left: 0.5rem;
}
.gc-defined-column-container sjs-text-editor.gc-define-column-invalid {
  --sjs-theme-border-color: var(--sjs-dc-invalid-color);
}
.gc-defined-column-container sjs-select {
  --sjs-theme-accent: var(--sjs-dc-hover-background-color);
  --sjs-theme-gray-bg: var(--sjs-dc-hover-background-color) none;
  --sjs-theme-accent-color: var(--sjs-dc-color);
}
.gc-defined-column-container sjs-select.gc-define-column-invalid {
  --sjs-theme-border-color: var(--sjs-dc-invalid-color);
}
.gc-defined-column-container sjs-select::part(container) {
  --sjs-dp-root-height: 32px;
  --sjs-dp-value-root-height: 30px;
}
.gc-defined-column-container sjs-color-picker.gc-defined-column-field-input-container-input {
  display: block;
}
.gc-defined-column-container sjs-color-picker::part(container) {
  margin-bottom: initial;
}
.gc-defined-column-container sjs-color-picker::part(color-value-container) {
  float: initial;
  width: initial;
  height: 100%;
  margin-top: initial;
  --sjs-color-value-container-sub-width: 99%;
  --sjs-color-value-container-sub-height: 95%;
}
.gc-defined-column-container sjs-checkbox::part(container) {
  height: initial;
  margin: initial;
  margin-bottom: initial;
  --sjs-theme-accent: var(--sjs-dc-accent-heavy);
}
.gc-defined-column-container sjs-number-editor::part(container) {
  margin-bottom: initial;
}
.gc-defined-column-container .gc-defined-column-list-container .gc-defined-column-list-items-container {
  margin-bottom: 0.5rem;
  border: 1px solid #DAE2ED;
}
.gc-defined-column-container .gc-defined-column-list-container .gc-defined-column-list-item-container {
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.gc-defined-column-container .gc-defined-column-list-container .gc-defined-column-list-item-container:hover {
  background-image: none;
  background-color: var(--sjs-dc-hover-background-color);
}
.gc-defined-column-container .gc-defined-column-list-container .gc-defined-column-list-item-container.gc-defined-column-list-item-selected {
  background-color: var(--sjs-dc-hover-background-color);
}
.gc-defined-column-container .gc-defined-column-list-container .gc-defined-column-list-item-container:focus {
  outline: none;
}
.gc-defined-column-container .gc-defined-column-list-container .gc-defined-column-list-item-container .gc-defined-column-list-item-text,
.gc-defined-column-container .gc-defined-column-list-container .gc-defined-column-list-item-container .gc-defined-column-list-item-value {
  height: 17px;
  letter-spacing: 0;
  text-align: left;
  display: block;
  line-height: normal;
  padding: 5px 10px;
  border: none;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.gc-defined-column-container .gc-defined-column-list-container .gc-defined-column-list-item-container .gc-defined-column-list-item-text:focus,
.gc-defined-column-container .gc-defined-column-list-container .gc-defined-column-list-item-container .gc-defined-column-list-item-value:focus {
  outline: none;
}
.gc-defined-column-container .gc-defined-column-list-container .gc-defined-column-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  background-color: var(--sjs-dc-accent-light);
  width: 100%;
  box-sizing: border-box;
}
.gc-defined-column-container .gc-defined-column-list-container .gc-defined-column-list-header-text,
.gc-defined-column-container .gc-defined-column-list-container .gc-defined-column-list-header-value {
  width: 50%;
  text-align: left;
  padding: 5px 10px;
}
.gc-defined-column-container .gc-defined-column-list-container .gc-defined-column-label-container {
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.gc-defined-column-container .gc-defined-column-list-container .gc-defined-column-list-label-container {
  display: flex;
  justify-content: space-between;
}
.gc-defined-column-container .gc-defined-column-number-formatting-container .gc-defined-column-checkbox-container {
  margin-top: 1rem;
}
.gc-defined-column-container .gc-defined-column-number-formatting-container .gc-defined-column-switch-container {
  margin-top: 1rem;
}
.gc-defined-column-container .gc-defined-column-number-formatting-container .gc-defined-column-list-container {
  margin-top: 1rem;
}
.gc-defined-column-container .gc-defined-column-number-formatting-container .gc-defined-column-select-box-container {
  margin-top: 1rem;
}
.gc-defined-column-container .gc-defined-column-number-formatting-container .gc-defined-column-number-formatting-negative-item {
  color: red;
}
.gc-defined-column-container .gc-defined-column-number-formatting-container .gc-defined-column-number-formatting-preview-container {
  margin-top: 1rem;
}
.gc-defined-column-container .gc-defined-column-number-formatting-container .gc-defined-column-number-formatting-preview-container .gc-defined-column-number-formatting-preview-text {
  height: 24px;
  line-height: 24px;
  overflow: hidden;
  word-break: break-all;
  color: var(--sjs-dc-color);
  font-weight: 400;
  padding: 0;
  margin-bottom: 0;
  padding-left: 0.7rem;
}
.gc-defined-column-container .gc-defined-column-date-formatting-container .gc-defined-column-list-items-container {
  max-height: 105px;
  overflow: auto;
}
.gc-defined-column-container .gc-defined-column-date-formatting-container .gc-defined-column-checkbox-container {
  margin-top: 1rem;
}
.gc-defined-column-container .gc-defined-column-date-formatting-container .gc-defined-column-switch-container {
  margin-top: 1rem;
}
.gc-defined-column-container .gc-defined-column-date-formatting-container .gc-defined-column-select-box-container {
  margin-top: 1rem;
  width: 100%;
}
.gc-defined-column-container .gc-defined-column-date-time-picker-container .gc-defined-column-checkbox-container {
  margin-top: 1rem;
}
.gc-defined-column-container .gc-defined-column-date-time-picker-container .gc-defined-column-switch-container {
  margin-top: 1rem;
}
.gc-defined-column-container .gc-defined-column-date-time-picker-container .gc-defined-column-select-box-container {
  margin-top: 1rem;
  width: 100%;
}
.gc-defined-column-container .gc-defined-column-modified-fields-container .gc-defined-column-list-items-container {
  max-height: 105px;
  overflow: auto;
}
.gc-defined-column-container .gc-defined-column-modified-fields-container .gc-defined-column-checkbox-container {
  margin-top: 1rem;
}
.gc-defined-column-container .gc-defined-column-modified-fields-container .gc-defined-column-switch-container {
  margin-top: 1rem;
}
.gc-defined-column-container .gc-defined-column-attachment-container .gc-defined-column-list-container {
  margin-top: 1rem;
}
.gc-defined-column-container .gc-defined-column-attachment-container .gc-defined-column-checkbox-container {
  margin-top: 1rem;
}
.gc-defined-column-container .gc-defined-column-attachment-container .gc-defined-column-switch-container {
  margin-top: 1rem;
}
.gc-defined-column-container .gc-defined-column-attachment-container .gc-defined-column-input-number {
  margin-top: 1rem;
  margin-right: 1rem;
  width: 100%;
}
.gc-defined-column-container .gc-defined-column-column-type-checkbox-container .gc-defined-column-checkbox-container {
  margin-top: 1rem;
}
.gc-defined-column-container .gc-defined-column-column-type-checkbox-container .gc-defined-column-switch-container {
  margin-top: 1rem;
}
.gc-defined-column-container .gc-defined-column-column-type-checkbox-container .gc-defined-column-input-number {
  margin-top: 1rem;
  margin-right: 1rem;
  width: 100%;
}
.gc-defined-column-container .gc-defined-column-checkbox-text-container .gc-defined-column-checkbox-field-item-container {
  margin-bottom: 0.5rem;
}
.gc-defined-column-container .gc-defined-column-combo-box-container .gc-defined-column-label-container {
  height: initial;
  line-height: 22px;
}
.gc-defined-column-container .gc-defined-column-combo-box-container .gc-defined-column-list-container {
  margin-top: 1rem;
}
.gc-defined-column-container .gc-defined-column-combo-box-container .gc-defined-column-list-items-container {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  margin-top: 0.1rem;
  border: 1px solid #DAE2ED;
  height: 125px;
  overflow: auto;
}
.gc-defined-column-container .gc-defined-column-combo-box-container .gc-defined-column-list-item-text,
.gc-defined-column-container .gc-defined-column-combo-box-container .gc-defined-column-list-item-value {
  padding: 2px 4px !important;
  margin: 5px 10px;
}
.gc-defined-column-container .gc-defined-column-combo-box-container .gc-defined-column-list-container .gc-defined-column-list-item-container {
  background-color: #dbdbdb4f;
}
.gc-defined-column-container .gc-defined-column-combo-box-container .gc-defined-column-list-container .gc-defined-column-list-item-container:hover {
  background-image: none;
  background-color: var(--sjs-dc-hover-background-color);
}
.gc-defined-column-container .gc-defined-column-combo-box-container .gc-defined-column-list-container .gc-defined-column-list-item-container.gc-defined-column-list-item-selected {
  background-color: var(--sjs-dc-hover-background-color);
}
.gc-defined-column-container .gc-defined-column-combo-box-container .gc-defined-column-list-container .gc-defined-column-list-item-container.gc-defined-column-list-item-selected .gc-defined-column-list-item-text,
.gc-defined-column-container .gc-defined-column-combo-box-container .gc-defined-column-list-container .gc-defined-column-list-item-container.gc-defined-column-list-item-selected .gc-defined-column-list-item-value {
  background-color: var(--sjs-dc-color-white);
}
.gc-defined-column-container .gc-defined-column-combo-box-style-container .gc-defined-column-checkbox-container {
  margin-top: 1rem;
}
.gc-defined-column-container .gc-defined-column-combo-box-style-container .gc-defined-column-switch-container {
  margin-top: 1rem;
}
.gc-defined-column-container .gc-defined-column-combo-box-style-container .gc-defined-column-select-box-container {
  margin-top: 1rem;
  width: 100%;
}
.gc-defined-column-container .gc-defined-column-combo-box-style-container .gc-defined-column-input-number {
  margin-top: 1rem;
  margin-right: 1rem;
  width: 100%;
}
.gc-defined-column-container .gc-defined-column-look-up-container .gc-defined-column-select-box-container {
  margin-top: 1rem;
  width: 100%;
}
.gc-defined-column-container .gc-defined-column-look-up-container .gc-defined-column-input-number {
  margin-top: 1rem;
  margin-right: 1rem;
  width: 100%;
}
.gc-defined-column-container .gc-defined-column-look-up-container .gc-defined-column-look-up-field-container {
  display: flex;
}
.gc-defined-column-container .gc-defined-column-formatting-select-container .gc-defined-column-number-formatting-container {
  margin-top: 1rem;
}
.gc-defined-column-container .gc-defined-column-mask-container .gc-defined-column-mask-exclude-container {
  display: flex;
}
.gc-defined-column-container .gc-defined-column-mask-container .gc-defined-column-mask-exclude-container .gc-defined-column-checkbox-container:nth-child(1) {
  margin-right: 1rem;
}
.gc-defined-column-container .gc-defined-column-mask-container .gc-defined-column-mask-exclude-container .gc-defined-column-switch-container:nth-child(1) {
  margin-right: 1rem;
}
.gc-defined-column-container .gc-defined-column-url-container .gc-defined-column-field-input-container-input[type=color] {
  padding: 1px 2px;
  background-color: initial;
}
.gc-defined-column-container .gc-defined-column-barcode-base-container .gc-defined-column-field-input-container-input[type=color] {
  padding: 1px 2px;
  background-color: initial;
}
.gc-defined-column-container .gc-defined-column-barcode-base-container .gc-defined-column-barcode-base-style-container {
  display: grid;
  grid-template-columns: auto auto;
  grid-column-gap: 1rem;
  margin-top: 0.5rem;
}
.gc-defined-column-container .gc-defined-column-barcode-base-container .gc-defined-column-barcode-base-style-container .gc-defined-column-field-item-container {
  margin-top: 0.5rem;
}
.gc-defined-column-container .gc-defined-column-barcode-options-container {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-column-gap: 0.5rem;
  margin-top: 0.5rem;
}
.gc-defined-column-container .gc-defined-column-barcode-options-container .gc-defined-column-select-box-container,
.gc-defined-column-container .gc-defined-column-barcode-options-container .gc-defined-column-barcode-options-input,
.gc-defined-column-container .gc-defined-column-barcode-options-container .gc-defined-column-input-number,
.gc-defined-column-container .gc-defined-column-barcode-options-container .gc-defined-column-empty-div,
.gc-defined-column-container .gc-defined-column-barcode-options-container .gc-defined-column-switch-field-item-container,
.gc-defined-column-container .gc-defined-column-barcode-options-container .gc-defined-column-checkbox-field-item-container {
  grid-column: span 3;
  margin-top: 0.5rem;
}
.gc-defined-column-container .gc-defined-column-barcode-options-container .gc-defined-column-checkbox-container {
  margin-top: 0.5rem;
}
.gc-defined-column-container .gc-defined-column-barcode-options-container .gc-defined-column-checkbox-container .gc-defined-column-checkbox-item-container {
  align-items: center;
}
.gc-defined-column-container .gc-defined-column-barcode-options-container .gc-defined-column-switch-container {
  margin-top: 0.5rem;
}
.gc-defined-column-container .gc-defined-column-barcode-options-container .gc-defined-column-switch-container .gc-defined-column-switch-item-container {
  align-items: center;
}
.gc-defined-column-container .gc-defined-column-barcode-options-container .gc-defined-column-field-item-small-container {
  grid-column: span 2;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.gc-defined-column-container .gc-defined-column-attachment-margins-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-column-gap: 0.5rem;
  margin-top: 0.5rem;
}
.gc-defined-column-container .gc-defined-column-tab-list-container {
  margin-top: 0.5rem;
}
.gc-defined-column-container .gc-defined-column-tab-list-items-container {
  display: flex;
}
.gc-defined-column-container .gc-defined-column-tab-item-container {
  margin-right: 1rem;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  cursor: pointer;
  opacity: 0.75;
}
.gc-defined-column-container .gc-defined-column-tab-item-container:focus {
  outline: none;
}
.gc-defined-column-container .gc-defined-column-tab-item-container:hover {
  opacity: 1;
}
.gc-defined-column-container .gc-defined-column-tab-item-container-selected {
  border-bottom: 2px solid var(--sjs-dc-color);
  font-weight: 500;
  cursor: inherit;
  opacity: 1;
}
.gc-defined-column-container .gc-defined-column-tab-item-container.gc-defined-column-disabled {
  opacity: 0.3;
  cursor: default;
}
.gc-defined-column-container .gc-defined-column-tab-panels-container {
  margin-top: 1rem;
}
.gc-defined-column-container .gc-defined-column-tab-panel-container {
  display: none;
}
.gc-defined-column-container .gc-defined-column-tab-panel-container-selected {
  display: block;
}
.gc-defined-column-container .gc-defined-column-tab-list-break-line {
  border-bottom: 1px solid var(--sjs-dc-accent-light);
  z-index: -1;
  position: relative;
  margin-top: -1px;
}
.gc-defined-column-container .gc-defined-column-divider-container {
  display: flex;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  width: 100%;
  min-width: 100%;
  box-sizing: border-box;
}
.gc-defined-column-container .gc-defined-column-divider-container .gc-defined-column-divider-inner-text {
  padding-inline-start: 0.6rem;
  padding-inline-end: 0.6rem;
  box-sizing: border-box;
  white-space: nowrap;
  text-align: center;
}
.gc-defined-column-container .gc-defined-column-divider-container::before {
  width: 5%;
  position: relative;
  border-block-start: 1px solid var(--sjs-dc-accent-light);
  border-block-end: 0;
  transform: translateY(50%);
  content: "";
  box-sizing: border-box;
}
.gc-defined-column-container .gc-defined-column-divider-container::after {
  width: 95%;
  position: relative;
  border-block-start: 1px solid var(--sjs-dc-accent-light);
  border-block-end: 0;
  transform: translateY(50%);
  content: "";
  box-sizing: border-box;
}
.gc-defined-column-container .gc-defined-column-footer-container {
  justify-content: flex-end;
  align-items: center;
  display: flex;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 1rem;
  padding-top: 0.5rem;
}
.gc-defined-column-container .gc-defined-column-footer-container .gc-defined-column-footer-container-buttons {
  justify-content: flex-end;
  align-items: baseline;
  display: flex;
}
.gc-defined-column-container .gc-defined-column-footer-container .gc-defined-column-footer-container-buttons .gc-defined-column-footer-container-button {
  height: var(--sjs-dc-input-height);
  font-size: var(--sjs-dc-font-size);
  font-family: var(--sjs-dc-font-family);
  line-height: 22px;
  max-width: 100%;
  cursor: pointer;
  justify-content: center;
  align-items: center;
  display: inline-flex;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  margin-left: 0.5rem;
  text-decoration: none;
  background-color: transparent;
  color: var(--sjs-dc-color);
  fill: var(--sjs-dc-color);
  border: 1px solid #acacac;
  background-image: -webkit-linear-gradient(top, #f0f0f0, #e5e5e5);
  /* For Chrome and Safari */
  background-image: -moz-linear-gradient(top, #f0f0f0, #e5e5e5);
  /* For old Fx (3.6 to 15) */
  background-image: -ms-linear-gradient(top, #f0f0f0, #e5e5e5);
  /* For pre-releases of IE 10*/
  background-image: -o-linear-gradient(top, #f0f0f0, #e5e5e5);
  /* For old Opera (11.1 to 12.0) */
  background-image: linear-gradient(to bottom, #f0f0f0, #e5e5e5);
  font-weight: normal;
  color: black;
  border-radius: 0;
}
.gc-defined-column-container .gc-defined-column-footer-container .gc-defined-column-footer-container-buttons .gc-defined-column-footer-container-button:hover {
  border: 1px solid #DAE2ED;
  border-color: var(--sjs-dc-accent-heavy);
  border: 1px solid #7eb4ea;
  background-image: -webkit-linear-gradient(top, #ecf4fc, #dcecfc);
  /* For Chrome and Safari */
  background-image: -moz-linear-gradient(top, #ecf4fc, #dcecfc);
  /* For old Fx (3.6 to 15) */
  background-image: -ms-linear-gradient(top, #ecf4fc, #dcecfc);
  /* For pre-releases of IE 10*/
  background-image: -o-linear-gradient(top, #ecf4fc, #dcecfc);
  /* For old Opera (11.1 to 12.0) */
  background-image: linear-gradient(to bottom, #ecf4fc, #dcecfc);
  /* Standard syntax; must be last */
  background-color: #d3f0e0;
  border-radius: 0;
  cursor: pointer;
  color: black;
  font-weight: normal;
  text-shadow: none;
}
.gc-defined-column-container .gc-defined-column-add-remove-container {
  justify-content: flex-end;
  align-items: center;
  display: flex;
}
.gc-defined-column-container .gc-defined-column-add-remove-container .gc-defined-column-add-remove-container-buttons {
  justify-content: flex-start;
  align-items: baseline;
  display: flex;
}
.gc-defined-column-container .gc-defined-column-add-remove-container .gc-defined-column-add-remove-container-buttons .gc-defined-column-add-remove-container-button {
  font-size: var(--sjs-dc-font-size);
  font-family: var(--sjs-dc-font-family);
  line-height: 22px;
  max-width: 100%;
  border: 0;
  cursor: pointer;
  justify-content: center;
  align-items: center;
  display: inline-flex;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  text-decoration: none;
  background-color: transparent;
  color: var(--sjs-dc-color);
  fill: var(--sjs-dc-color);
  font-weight: initial;
}
.gc-defined-column-container .gc-defined-column-add-remove-container .gc-defined-column-add-remove-container-buttons .gc-defined-column-add-remove-container-button:hover {
  text-decoration: underline;
}
.gc-defined-column-container .gc-defined-column-add-remove-container .gc-defined-column-add-remove-container-buttons .gc-defined-column-add-remove-container-button-icon {
  display: block;
  width: 14px;
  height: 14px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  cursor: inherit;
  margin-right: 0.2rem;
  width: 10px;
  height: 10px;
}
.gc-defined-column-container .gc-defined-column-add-remove-container .gc-defined-column-add-remove-container-buttons .gc-defined-column-add-remove-container-button-icon-add {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+QWRkIGxldmVsPC90aXRsZT4KICAgIDxnIGlkPSJBZGQtbGV2ZWwiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwYXRoIGQ9Ik0xMCwwIEwxMCw2IEwxNiw2IEwxNiw5IEwxMCw5IEwxMCwxNSBMNywxNSBMNyw5IEwxLDkgTDEsNiBMNyw2IEw3LDAgTDEwLDAgWiIgaWQ9IuW9oueKtue7k+WQiCIgZmlsbD0iIzY2NjY2NiI+PC9wYXRoPgogICAgPC9nPgo8L3N2Zz4=);
}
.gc-defined-column-container .gc-defined-column-add-remove-container .gc-defined-column-add-remove-container-buttons .gc-defined-column-add-remove-container-button-icon-remove {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1LjIgKDc4MTgxKSAtIGh0dHBzOi8vc2tldGNoYXBwLmNvbSAtLT4KICAgIDx0aXRsZT5EZWxldGUgTGV2ZWw8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZyBpZD0iRGVsZXRlLUxldmVsIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8cGF0aCBkPSJNMTUuOTY3MDk4NiwyLjQ3NzU4MDYxIEMxNS40NzY1MDU4LDMuNzM4NzgwNzIgMTIuODI3ODA4LDQuOTUyODU1OTkgMTAuMzk2OTI4Miw2Ljc1ODc4MzUxIEMxMS42MDY4NDcsOC42ODQ0MTIyOCAxMi45NDUwNzA2LDExLjI2NzQ2MzcgMTUuMTY5MzAxOSwxNiBDMTIuMzgwMzM2NywxMi43MzY4Mzk5IDEwLjMzMjI1MiwxMC43NjI3MDM1IDguMjY0MTkwMSw4LjU4NzI2MzQ0IEM2LjA2MzYyMDc5LDEwLjU5OTYzMjMgNS4wMTkyMjk1NywxMS42NjY4NzczIDQuMjUyMDI2NSwxMy4wOTMxNDQzIEMzLjM3OTAxMTQ4LDE0LjcxNjEyMDggMi44NzcxMzMzMiwxNi41Mzk1ODk4IDEuMjI1NTIzMjMsMTUuNzQ4NTIwNyBDMC4xMjQ0NDk4NDEsMTUuMjE5NDA2NSAtMC44Njc3NzgxODcsMTMuMjM5OTk4OSAxLjIyNTUyMzIzLDExLjE4NzcyOTcgQzMuMzE4ODI0NjUsOS4xMzU0NjA0NiA0Ljk1MTUwNTExLDcuNjAxODk2NjUgNi4xMjUxNDIwOCw2LjU4ODc3MzA0IEM0LjUyNTU4ODQ3LDUuMjkxMTQyMTIgMy4wOTAwOTE2NCw0LjM5NzI0MDMgMS4yMjU1MjMyMywzLjczMTA3NjgzIEMtMC42MzkwNDUxNzYsMy4wNjQ5MTMzNyAwLjI4NjAyNjQzLDEuMDcwMzcyMDMgMC43NjcxNTQ0ODksMC40NTc5ODczODQgQzEuMjUxNDM3NDgsLTAuMTUyNjYyNDYxIDIuMjQ5MzAwNjQsLTAuMTUyNjYyNDYxIDMuNDM4NzEyMywwLjQ1Nzk4NzM4NCBDNS43NjcwNTY2MSwxLjkzNzc3MjM4IDcuNTYzNzkzODUsMy4zNTUxMDQ1NSA4LjgzMzY1NjQzLDQuNzA5OTgzODkgQzExLjU2ODk4NzcsMy4yNjMxNjAxMSAxNi40NDY2NDkyLDEuMjQyNDAyNTIgMTUuOTY3MDk4NiwyLjQ3NzU4MDYxIFoiIGZpbGw9IiM2NjY2NjYiPjwvcGF0aD4KICAgIDwvZz4KPC9zdmc+);
}

.gc-table-sheet-panel, .gc-table-sheet-cross-column {
  position: relative;
  background-color: white;
  box-sizing: border-box;
  width: 100%;
  min-width: 300px;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-size: 12px;
  overflow-x: hidden;
  overflow-y: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  user-select: none;
}
.gc-table-sheet-panel .gc-table-sheet-field-panel, .gc-table-sheet-panel .gc-table-sheet-cross-column-field, .gc-table-sheet-cross-column .gc-table-sheet-field-panel, .gc-table-sheet-cross-column .gc-table-sheet-cross-column-field {
  margin: 5px 10px 0 10px;
  width: calc(100% - 20px);
  height: calc(100% - 30px);
  border: solid 1px lightgray;
}
.gc-table-sheet-panel .gc-table-sheet-field-panel .gcsj-func-help-popup, .gc-table-sheet-panel .gc-table-sheet-cross-column-field .gcsj-func-help-popup, .gc-table-sheet-cross-column .gc-table-sheet-field-panel .gcsj-func-help-popup, .gc-table-sheet-cross-column .gc-table-sheet-cross-column-field .gcsj-func-help-popup {
  white-space: normal;
}
.gc-table-sheet-panel .gc-table-sheet-field-container, .gc-table-sheet-panel .gc-table-sheet-cross-column-field-container, .gc-table-sheet-cross-column .gc-table-sheet-field-container, .gc-table-sheet-cross-column .gc-table-sheet-cross-column-field-container {
  height: 40%;
}
.gc-table-sheet-panel .gc-table-sheet-field-container.total-height, .gc-table-sheet-panel .gc-table-sheet-cross-column-field-container.total-height, .gc-table-sheet-cross-column .gc-table-sheet-field-container.total-height, .gc-table-sheet-cross-column .gc-table-sheet-cross-column-field-container.total-height {
  height: 100% !important;
}
.gc-table-sheet-panel .gc-table-sheet-cross-panel, .gc-table-sheet-cross-column .gc-table-sheet-cross-panel {
  height: 60%;
}
.gc-table-sheet-panel .gc-table-sheet-cross-column-detail, .gc-table-sheet-cross-column .gc-table-sheet-cross-column-detail {
  height: calc(100% - 40px);
  margin: 0px 10px 10px 9px;
  padding: 0px;
  width: calc(100% - 20px);
  overflow-y: auto;
  overflow-x: hidden;
  background-color: white;
  border: solid 1px lightgrey;
}
.gc-table-sheet-panel .gc-table-sheet-cross-column-list-container, .gc-table-sheet-cross-column .gc-table-sheet-cross-column-list-container {
  height: 20%;
}
.gc-table-sheet-panel .disable, .gc-table-sheet-cross-column .disable {
  opacity: 0.6;
  pointer-events: none;
  cursor: default;
}

.gc-table-sheet-group-panel, .gc-table-sheet-cross-column-list-container, .gc-table-sheet-cross-column-detail {
  margin: 0px;
  padding: 0px;
  width: 100%;
  height: 30%;
  overflow-y: auto;
  overflow-x: hidden;
  background-color: white;
}
.gc-table-sheet-group-panel.half-height, .gc-table-sheet-cross-column-list-container.half-height, .gc-table-sheet-cross-column-detail.half-height {
  height: 50% !important;
}
.gc-table-sheet-group-panel .gc-table-sheet-group-input, .gc-table-sheet-cross-column-list-container .gc-table-sheet-group-input, .gc-table-sheet-cross-column-detail .gc-table-sheet-group-input {
  position: absolute;
  left: 1px;
  width: calc(100% - 8px);
  display: none;
  font-size: 12px;
  height: 17px;
  font-family: inherit;
  border: 1px solid rgb(82, 146, 247);
  outline: 1px solid rgb(82, 146, 247);
  box-shadow: rgba(0, 0, 0, 0.4) 1px 2px 5px;
}

.gc-table-sheet-group-header, .gc-table-sheet-cross-column-header {
  box-sizing: border-box;
  outline: none;
  width: 100%;
  height: 20px;
  font-size: large;
  font-weight: 600;
  margin-left: 10px;
}

.gc-table-sheet-group-list, .gc-table-sheet-cross-column-list, .gc-table-sheet-cross-column-attributes-list {
  position: relative;
  width: calc(100% - 20px);
  height: calc(100% - 40px);
  margin: 6px 0 0 9px;
  border: dashed lightgray 1px;
  overflow-y: auto;
  overflow-x: hidden;
  padding-inline-start: 0px;
  padding: 0px;
}
.gc-table-sheet-group-list .gc-table-sheet-group-item, .gc-table-sheet-group-list .gc-table-sheet-cross-column-item, .gc-table-sheet-cross-column-list .gc-table-sheet-group-item, .gc-table-sheet-cross-column-list .gc-table-sheet-cross-column-item, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-group-item, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-cross-column-item {
  height: 23px;
  line-height: 23px;
  margin: 2px;
  background-color: #f2f2ff;
  box-sizing: border-box;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
  white-space: nowrap;
  margin-bottom: 1px;
  font-size: 12px;
  border: 1px solid #CBCBCB;
  outline: none;
}
.gc-table-sheet-group-list .gc-table-sheet-cross-column-item-input, .gc-table-sheet-cross-column-list .gc-table-sheet-cross-column-item-input, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-cross-column-item-input {
  position: relative;
}
.gc-table-sheet-group-list .gc-table-sheet-cross-column-input, .gc-table-sheet-cross-column-list .gc-table-sheet-cross-column-input, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-cross-column-input {
  position: absolute;
  left: 1px;
  top: 2px;
  width: calc(100% - 8px);
  font-size: 12px;
  height: 17px;
  font-family: inherit;
  border: 1px solid rgb(82, 146, 247);
  outline: 1px solid rgb(82, 146, 247);
  box-shadow: rgba(0, 0, 0, 0.4) 1px 2px 5px;
}
.gc-table-sheet-group-list .gc-table-sheet-group-item:hover, .gc-table-sheet-group-list .gc-table-sheet-cross-column-item:hover, .gc-table-sheet-cross-column-list .gc-table-sheet-group-item:hover, .gc-table-sheet-cross-column-list .gc-table-sheet-cross-column-item:hover, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-group-item:hover, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-cross-column-item:hover {
  cursor: pointer;
  border: solid 1px #217346;
}
.gc-table-sheet-group-list .gc-table-sheet-group-item-reorder-top, .gc-table-sheet-group-list .gc-table-sheet-cross-column-item-reorder-top, .gc-table-sheet-cross-column-list .gc-table-sheet-group-item-reorder-top, .gc-table-sheet-cross-column-list .gc-table-sheet-cross-column-item-reorder-top, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-group-item-reorder-top, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-cross-column-item-reorder-top {
  border: 1px solid #CBCBCB !important;
  border-top: solid 1px #217346 !important;
}
.gc-table-sheet-group-list .gc-table-sheet-group-item-reorder-bottom, .gc-table-sheet-group-list .gc-table-sheet-cross-column-item-reorder-bottom, .gc-table-sheet-cross-column-list .gc-table-sheet-group-item-reorder-bottom, .gc-table-sheet-cross-column-list .gc-table-sheet-cross-column-item-reorder-bottom, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-group-item-reorder-bottom, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-cross-column-item-reorder-bottom {
  border: 1px solid #CBCBCB !important;
  border-bottom: solid 1px #217346 !important;
}
.gc-table-sheet-group-list .selected, .gc-table-sheet-cross-column-list .selected, .gc-table-sheet-cross-column-attributes-list .selected {
  background-color: #227347 !important;
  color: white !important;
}
.gc-table-sheet-group-list .selected .gc-table-sheet-group-item-remove, .gc-table-sheet-cross-column-list .selected .gc-table-sheet-group-item-remove, .gc-table-sheet-cross-column-attributes-list .selected .gc-table-sheet-group-item-remove {
  background-image: url(data:image/svg+xml;base64,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);
}
.gc-table-sheet-group-list .selected .gc-table-sheet-group-item-setting, .gc-table-sheet-cross-column-list .selected .gc-table-sheet-group-item-setting, .gc-table-sheet-cross-column-attributes-list .selected .gc-table-sheet-group-item-setting {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4NCjxzdmcgd2lkdGg9IjE2cHgiIGhlaWdodD0iMTZweCIgdmlld0JveD0iMCAwIDEwIDEwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPg0KICAgIDx0aXRsZT5zZXR0aW5nIGNvbG9yPC90aXRsZT4NCiAgICA8ZyBpZD0ic2V0dGluZy1jb2xvciIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+DQogICAgICAgIDxnIGlkPSLliIbnu4QiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAuMDAwMDAwLCAtMC4wMDAwMDApIiBmaWxsLXJ1bGU9Im5vbnplcm8iPg0KICAgICAgICAgICAgPHBhdGggZD0iTTUsNi40Mjg1NzE0MyBDNC4yMTIyNDQ5LDYuNDI4NTcxNDMgMy41NzE0Mjg1Nyw1Ljc4Nzc1NTEgMy41NzE0Mjg1Nyw1IEMzLjU3MTQyODU3LDQuMjEyMjQ0OSA0LjIxMjI0NDksMy41NzE0Mjg1NyA1LDMuNTcxNDI4NTcgQzUuNzg3NzU1MSwzLjU3MTQyODU3IDYuNDI4NTcxNDMsNC4yMTIyNDQ5IDYuNDI4NTcxNDMsNSBDNi40Mjg1NzE0Myw1Ljc4Nzc1NTEgNS43ODc3NTUxLDYuNDI4NTcxNDMgNSw2LjQyODU3MTQzIFogTTQuOTk5NjQyODYsMi44NTcxNDI4NiBDMy44MTgwMjE4MiwyLjg1NzE0Mjg2IDIuODU2Nzg1NzEsMy44MTgxMjc0OCAyLjg1Njc4NTcxLDUgQzIuODU2Nzg1NzEsNi4xODEwNTc0NCAzLjgxODAyMTgyLDcuMTQyODU3MTQgNC45OTk2NDI4Niw3LjE0Mjg1NzE0IEM2LjE4MTI2Mzg5LDcuMTQyODU3MTQgNy4xNDI1LDYuMTgxMDU3NDQgNy4xNDI1LDUgQzcuMTQyNSwzLjgxODEyNzQ4IDYuMTgxMjYzODksMi44NTcxNDI4NiA0Ljk5OTY0Mjg2LDIuODU3MTQyODYgWiBNOS4yODA4MTkxMiw1LjI4NzIwNjg4IEM5LjIzODg2MDU5LDUuMzE1MTkyOTMgOS4xOTI3MDYyMSw1LjMzNDc4MzE3IDkuMTQzNzU0NTksNS4zNDU5Nzc1OSBMOC4wMDk0NzU2Myw1LjYyOTMzNjM4IEw3LjkwMzg4LDUuOTUwNDc2MzUgQzcuODU3MDI2MzEsNi4wOTYwMDM4MiA3Ljc5NDc4NzgyLDYuMjQyOTMwNjEgNy43MjEzNjAzOSw2LjM4Nzc1ODQzIEw3LjU2NzUxMjQ0LDYuNjkwMDA3ODEgTDguMTY2ODIwMTMsNy42OTA1MDkyMyBDOC4yMDUyODIxMSw3Ljc1MTM3ODkgOC4yMjI3NjQ4Myw3LjgwNTk1MTcgOC4yMjgzNTkzLDcuODM4ODM1MzEgTDcuODI1NTU3NDEsOC4yNDExMzQ4MyBDNy43ODkxOTMzNSw4LjIzMDY0MDA2IDcuNzU0MjI3OSw4LjIxNjY0NzA0IDcuNzIyMDU5Nyw4LjE5Nzc1NjQ1IEw2LjcxNDM1NTY1LDcuNTU1NDc2NTIgTDYuNDAxNzY0NTksNy43MTc3OTU2MyBDNi4yNTM1MTExMSw3Ljc5NDA1NzYzIDYuMDk5NjYzMTcsNy44NTkxMjUyIDUuOTQxNjE5MzcsNy45MTAxOTk3NSBMNS42MDgwNDkwNCw4LjAxNzk0NjA2IEw1LjM1NjI5Nzg2LDkuMTU3Njc4MDkgQzUuMzQ0NDA5NjEsOS4yMTg1NDc3NiA1LjMxMTU0MjA5LDkuMjY4MjIzIDUuMjg5MTY0MjEsOS4yODU3MTQyOSBMNC43Mzg4MDgxNCw5LjI4NTcxNDI5IEM0LjcxMDgzNTc5LDkuMjQzNzM1MjEgNC42OTEyNTUxNCw5LjE5NzU1ODIyIDQuNjgwMDY2Miw5LjE0ODU4MjYyIEw0LjM5ODk0NDA0LDguMDIxNDQ0MzEgTDQuMDcyMzY2ODEsNy45MTc4OTU5MiBDMy45MDAzMzY4Myw3Ljg2MzMyMzExIDMuNzMzMjAyMDIsNy43OTMzNTc5OCAzLjU3Mzc1OTYsNy43MDg3MDAxNiBMMy4yNTkwNzA2Miw3LjU0Mjg4MjggTDIuMjUyMDY1ODcsOC4xODUxNjI3MyBDMi4yMTUwMDI1MSw4LjIxMTc0OTQ4IDIuMTcxNjQ1MzYsOC4yMjY0NDIxNiAyLjEyNjg4OTU5LDguMjI4NTQxMTEgTDEuNzI1NDg2MzEsNy44MzgxMzU2NiBDMS43MzUyNzY2Myw3Ljc4OTE2MDA3IDEuNzU0MTU3OTcsNy43NDIyODM0MyAxLjc4MDczMTcxLDcuNzAwMzA0MzUgTDIuNDA2NjEzMTMsNi42NTcxMjQyIEwyLjI1NzY2MDM1LDYuMzU2OTczNzcgQzIuMTkxOTI1MzEsNi4yMjYxMzg5NyAyLjEzNjY3OTkxLDYuMDkxMTA2MjcgMi4wOTA1MjU1Myw1Ljk1MTE3NiBMMS45ODQ5Mjk4OSw1LjYyOTMzNjM4IEwwLjg2MTgzOTg4Miw1LjM0ODc3NjE5IEMwLjgwOTM5MTcxOCw1LjMzNzU4MTc3IDAuNzU5MDQxNDgxLDUuMzE2NTkyMjMgMC43MTQyODU3MTQsNS4yODcyMDY4OCBMMC43MTQyODU3MTQsNC43MTgzOTAzNCBMMC43MTQ5ODUwMjMsNC43MTgzOTAzNCBDMC43MjYxNzM5NjUsNC43MTgzOTAzNCAwLjc3NTgyNDg5Myw0LjY4MjcwODEyIDAuODE3NzgzNDI0LDQuNjc1MDExOTUgTDEuOTcxNjQzMDMsNC40MTg5Mzk1NiBMMi4wNzc5Mzc5Nyw0LjA4MTcwNzYyIEMyLjEyMDU5NTgxLDMuOTQzODc2MyAyLjE3OTMzNzc1LDMuODAxMTQ3NDMgMi4yNTc2NjAzNSwzLjY0MzcyNTg4IEwyLjQwNjYxMzEzLDMuMzQzNTc1NDUgTDEuNzg3NzI0OCwyLjMxMDg5MDA3IEMxLjc1NzY1NDUyLDIuMjY1NDEyNzQgMS43MzY2NzUyNSwyLjIxNTczNzQ5IDEuNzI2MTg1NjIsMi4xNjI1NjM5OSBMMi4xMjgyODgyMSwxLjc2MDI2NDQ3IEMyLjE2NzQ0OTUsMS43NzI4NTgxOSAyLjIxMTUwNTk2LDEuNzg4OTUwMTcgMi4yMzE3ODU5MiwxLjgwMjk0MzIgTDMuMjU4MzcxMzEsMi40NTc4MTY4NSBMMy41NzIzNjA5OCwyLjI5MTI5OTg0IEMzLjczMTEwNDA5LDIuMjA3MzQxNjcgMy44OTc1Mzk2LDIuMTM3Mzc2NTQgNC4wNjg4NzAyNywyLjA4MjgwMzc0IEw0LjM5NDA0ODg4LDEuOTc5MjU1MzQgTDQuNjczMDczMTEsMC44NjI2MTE3OTggQzQuNjg5MTU3MjIsMC43OTE5NDcwMTMgNC43MTUwMzE2NCwwLjc0MDg3MjQ2NSA0LjczNDYxMjI5LDAuNzE0Mjg1NzE0IEw1LjMwMzE1MDM5LDAuNzE0Mjg1NzE0IEM1LjI5ODk1NDUzLDAuNzIwNTgyNTc2IDUuMzM4MTE1ODMsMC43NzM3NTYwNzggNS4zNDY1MDc1NCwwLjgxNzgzNDExMyBMNS42MDMxNTM4OCwxLjk3OTI1NTM0IEw1LjkzNjcyNDIsMi4wODcwMDE2NCBDNi4wOTI2NzAwOCwyLjEzODA3NjE5IDYuMjQ5MzE1MjYsMi4yMDMxNDM3NyA2LjQwMjQ2MzksMi4yODI5MDQwMiBMNi43MTUwNTQ5NSwyLjQ0NTkyMjc4IEw3LjcwMTc3OTc0LDEuODE2OTM2MjMgQzcuNzM4MTQzOCwxLjc5MDM0OTQ4IDcuNzgxNTAwOTUsMS43NzQ5NTcxNSA3LjgyNjk1NjAzLDEuNzcyODU4MTkgTDguMjI4MzU5MywyLjE2Mzk2MzI5IEM4LjIxODU2ODk4LDIuMjEyOTM4ODkgOC4xOTk2ODc2NCwyLjI1OTExNTg3IDguMTczMTEzOTEsMi4zMDE3OTQ2MSBMNy41Njc1MTI0NCwzLjMwOTk5MjE5IEw3LjcyMjA1OTcsMy42MTI5NDEyMiBDNy44MDUyNzc0NSwzLjc3NTk1OTk4IDcuODY4MjE1MjUsMy45MjkxODM2MiA3LjkxNjQ2NzU2LDQuMDgxMDA3OTYgTDguMDIyMDYzMTksNC40MTg5Mzk1NiBMOS4xNTI4NDU2LDQuNjcwMTE0MzkgQzkuMjIxMzc3ODcsNC42ODI3MDgxMiA5LjI3NDUyNTM0LDQuNzIyNTg4MjQgOS4yODU3MTQyOSw0Ljc0NDI3NzQ0IEw5LjI4MDgxOTEyLDUuMjg3MjA2ODggWiBNOS4zNzkyODU3MSw0LjA1Mjg1NzE0IEw4LjU3NzE0Mjg2LDMuODc1IEM4LjUxNSwzLjY3NSA4LjQzMjg1NzE0LDMuNDgzNTcxNDMgOC4zMzkyODU3MSwzLjMgTDguNzcyODU3MTQsMi41NzcxNDI4NiBDOC45MzUsMi4zMTc4NTcxNCA5LjAxNzE0Mjg2LDEuOTM3MTQyODYgOC43NzI4NTcxNCwxLjY5Mjg1NzE0IEw4LjMzMDcxNDI5LDEuMjUwNzE0MjkgQzguMDg3MTQyODYsMS4wMDcxNDI4NiA3LjY3NSwxLjA1MDcxNDI5IDcuNDI1NzE0MjksMS4yMjM1NzE0MyBMNi43MjI4NTcxNCwxLjY3MTQyODU3IEM2LjUzOTI4NTcxLDEuNTc2NDI4NTcgNi4zNDg1NzE0MywxLjQ5NTcxNDI5IDYuMTUxNDI4NTcsMS40MzE0Mjg1NyBMNS45NzE0Mjg1NywwLjYyIEM1LjkxNzg1NzE0LDAuMzIyMTQyODU3IDUuNjU3MTQyODYsMCA1LjMxMjE0Mjg2LDAgTDQuNjg3MTQyODYsMCBDNC4zNDIxNDI4NiwwIDQuMTMwNzE0MjksMC4zMjcxNDI4NTcgNC4wNjIxNDI4NiwwLjYyNSBMMy44NjE0Mjg1NywxLjQyNzE0Mjg2IEMzLjY1MTQyODU3LDEuNDk0Mjg1NzEgMy40NDc4NTcxNCwxLjU3OTI4NTcxIDMuMjUyODU3MTQsMS42ODI4NTcxNCBMMi41MzE0Mjg1NywxLjIyMzU3MTQzIEMyLjI4MzU3MTQzLDEuMDUwNzE0MjkgMS44NzE0Mjg1NywxLjAwNzE0Mjg2IDEuNjI3MTQyODYsMS4yNTA3MTQyOSBMMS4xODUsMS42OTI4NTcxNCBDMC45NDE0Mjg1NzEsMS45MzcxNDI4NiAxLjAyMjg1NzE0LDIuMzE3ODU3MTQgMS4xODUsMi41NzY0Mjg1NyBMMS42NDE0Mjg1NywzLjMzNzg1NzE0IEMxLjU1NDI4NTcxLDMuNTEwNzE0MjkgMS40ODA3MTQyOSwzLjY5MDcxNDI5IDEuNDIxNDI4NTcsMy44NzUgTDAuNjIsNC4wNTI4NTcxNCBDMC4zMjIxNDI4NTcsNC4xMDY0Mjg1NyAwLDQuMzY3MTQyODYgMCw0LjcxMjE0Mjg2IEwwLDUuMzM3MTQyODYgQzAsNS42ODI4NTcxNCAwLjMyNzE0Mjg1Nyw1Ljg5NDI4NTcxIDAuNjI1LDUuOTYyMTQyODYgTDEuNDM2NDI4NTcsNi4xNjUgQzEuNDkyMTQyODYsNi4zMzcxNDI4NiAxLjU2Mjg1NzE0LDYuNTAyODU3MTQgMS42NDIxNDI4Niw2LjY2Mjg1NzE0IEwxLjE4NSw3LjQyNSBDMS4wMjM1NzE0Myw3LjY4MzU3MTQzIDAuOTQxNDI4NTcxLDguMDY0Mjg1NzEgMS4xODUsOC4zMDg1NzE0MyBMMS42MjcxNDI4Niw4Ljc1MDcxNDI5IEMxLjg3MTQyODU3LDguOTk1IDIuMjgyODU3MTQsOC45NSAyLjUzMTQyODU3LDguNzc4NTcxNDMgTDMuMjU0Mjg1NzEsOC4zMTc4NTcxNCBDMy40NTA3MTQyOSw4LjQyMTQyODU3IDMuNjU1NzE0MjksOC41MDc4NTcxNCAzLjg2NzE0Mjg2LDguNTc1IEw0LjA2NzE0Mjg2LDkuMzc0Mjg1NzEgQzQuMTM1NzE0MjksOS42NzIxNDI4NiA0LjM0NjQyODU3LDkuOTk5Mjg1NzEgNC42OTIxNDI4Niw5Ljk5OTI4NTcxIEw1LjMxNjQyODU3LDkuOTk5Mjg1NzEgQzUuNjYyMTQyODYsOS45OTkyODU3MSA1LjkyMjE0Mjg2LDkuNjc3MTQyODYgNS45NzU3MTQyOSw5LjM3OTI4NTcxIEw2LjE1NjQyODU3LDguNTY1IEM2LjM1Mjg1NzE0LDguNTAxNDI4NTcgNi41NDA3MTQyOSw4LjQyMTQyODU3IDYuNzIxNDI4NTcsOC4zMjc4NTcxNCBMNy40MjU3MTQyOSw4Ljc3Nzg1NzE0IEM3LjY3NSw4Ljk1IDguMDg2NDI4NTcsOC45OTQyODU3MSA4LjMzMDcxNDI5LDguNzUgTDguNzcyODU3MTQsOC4zMDc4NTcxNCBDOS4wMTcxNDI4Niw4LjA2MzU3MTQzIDguOTM1LDcuNjgyODU3MTQgOC43NzI4NTcxNCw3LjQyNDI4NTcxIEw4LjMzOTI4NTcxLDYuNyBDOC40MjY0Mjg1Nyw2LjUyODU3MTQzIDguNTAxNDI4NTcsNi4zNSA4LjU2MjE0Mjg2LDYuMTY0Mjg1NzEgTDkuMzc1LDUuOTYyMTQyODYgQzkuNjcyMTQyODYsNS44OTI4NTcxNCAxMCw1LjY4MjE0Mjg2IDEwLDUuMzM2NDI4NTcgTDEwLDQuNzExNDI4NTcgQzEwLDQuMzY2NDI4NTcgOS42Nzc4NTcxNCw0LjEwNTcxNDI5IDkuMzc5Mjg1NzEsNC4wNTIxNDI4NiBMOS4zNzkyODU3MSw0LjA1Mjg1NzE0IFoiIGlkPSJGaWxsLTMiIGZpbGw9IiNmZmZmZmYiPjwvcGF0aD4NCiAgICAgICAgPC9nPg0KICAgIDwvZz4NCjwvc3ZnPg0K);
}
.gc-table-sheet-group-list .selected .gc-table-sheet-cross-column-item-edit, .gc-table-sheet-cross-column-list .selected .gc-table-sheet-cross-column-item-edit, .gc-table-sheet-cross-column-attributes-list .selected .gc-table-sheet-cross-column-item-edit {
  background-image: url(data:image/svg+xml;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************);
}
.gc-table-sheet-group-list .gc-table-sheet-group-item-label, .gc-table-sheet-group-list .gc-table-sheet-cross-column-item-label, .gc-table-sheet-cross-column-list .gc-table-sheet-group-item-label, .gc-table-sheet-cross-column-list .gc-table-sheet-cross-column-item-label, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-group-item-label, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-cross-column-item-label {
  margin-left: 5px;
  width: 80%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  white-space: nowrap;
}
.gc-table-sheet-group-list .gc-table-sheet-group-item-remove, .gc-table-sheet-group-list .gc-table-sheet-summary-item-remove, .gc-table-sheet-group-list .gc-table-sheet-group-item-setting, .gc-table-sheet-cross-column-list .gc-table-sheet-group-item-remove, .gc-table-sheet-cross-column-list .gc-table-sheet-summary-item-remove, .gc-table-sheet-cross-column-list .gc-table-sheet-group-item-setting, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-group-item-remove, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-summary-item-remove, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-group-item-setting {
  float: right;
  cursor: pointer;
  background-repeat: no-repeat;
  background-position: center;
  width: 16px;
  height: 16px;
  margin-top: 3px;
  margin-right: 3px;
}
.gc-table-sheet-group-list .gc-table-sheet-group-item-remove, .gc-table-sheet-group-list .gc-table-sheet-summary-item-remove, .gc-table-sheet-cross-column-list .gc-table-sheet-group-item-remove, .gc-table-sheet-cross-column-list .gc-table-sheet-summary-item-remove, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-group-item-remove, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-summary-item-remove {
  background-image: url(data:image/svg+xml;base64,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);
}
.gc-table-sheet-group-list .gc-table-sheet-group-item-setting, .gc-table-sheet-cross-column-list .gc-table-sheet-group-item-setting, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-group-item-setting {
  background-image: url(data:image/svg+xml;base64,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);
}
.gc-table-sheet-group-list .gc-table-sheet-cross-column-item-checkbox, .gc-table-sheet-cross-column-list .gc-table-sheet-cross-column-item-checkbox, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-cross-column-item-checkbox {
  float: left;
}
.gc-table-sheet-group-list .gc-table-sheet-cross-column-item-label, .gc-table-sheet-cross-column-list .gc-table-sheet-cross-column-item-label, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-cross-column-item-label {
  float: left;
}
.gc-table-sheet-group-list .gc-table-sheet-cross-column-item-edit, .gc-table-sheet-cross-column-list .gc-table-sheet-cross-column-item-edit, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-cross-column-item-edit {
  float: right;
  cursor: pointer;
  background-repeat: no-repeat;
  background-position: center;
  width: 16px;
  height: 16px;
  margin-top: 3px;
  background-image: url(data:image/svg+xml;base64,PHN2ZyB0PSIxNjQ5ODQzMjA5MjY3IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9Ijg0NzMiIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiI+PHBhdGggZD0iTTcyNi42NTI4MDEgNDI5LjMwNTYwMyAyOTcuMzQ3MTk5IDQyOS4zMDU2MDMgNTEyLjE5MzQwNSA2MzguMTU2MjU4WiIgcC1pZD0iODQ3NCIgZmlsbD0iIzY2NjY2NiI+PC9wYXRoPjwvc3ZnPg==);
}
.gc-table-sheet-group-list .gc-table-sheet-cross-column-item-editing, .gc-table-sheet-cross-column-list .gc-table-sheet-cross-column-item-editing, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-cross-column-item-editing {
  height: initial;
}
.gc-table-sheet-group-list .gc-table-sheet-cross-column-item-editing .gc-table-sheet-cross-column-item-edit, .gc-table-sheet-cross-column-list .gc-table-sheet-cross-column-item-editing .gc-table-sheet-cross-column-item-edit, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-cross-column-item-editing .gc-table-sheet-cross-column-item-edit {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB0PSIxNjQ5ODQzNzY1NTA5IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9Ijg4NjMiIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiI+PHBhdGggZD0iTTI5Ny4zNDcxOTkgNjM4LjE1NjI1OCA3MjYuNjUyODAxIDYzOC4xNTYyNTggNTExLjgwNjU5NSA0MjkuMzA1NjAzWiIgcC1pZD0iODg2NCIgZmlsbD0iIzY2NjY2NiI+PC9wYXRoPjwvc3ZnPg==);
}
.gc-table-sheet-group-list .gc-table-sheet-cross-column-item-editing.selected .gc-table-sheet-cross-column-item-edit, .gc-table-sheet-cross-column-list .gc-table-sheet-cross-column-item-editing.selected .gc-table-sheet-cross-column-item-edit, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-cross-column-item-editing.selected .gc-table-sheet-cross-column-item-edit {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB0PSIxNjQ5ODQzNzY1NTA5IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9Ijg4NjMiIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiI+PHBhdGggZD0iTTI5Ny4zNDcxOTkgNjM4LjE1NjI1OCA3MjYuNjUyODAxIDYzOC4xNTYyNTggNTExLjgwNjU5NSA0MjkuMzA1NjAzWiIgcC1pZD0iODg2NCIgZmlsbD0iI2ZmZmZmZiI+PC9wYXRoPjwvc3ZnPg==);
}
.gc-table-sheet-group-list .gc-table-sheet-cross-column-attributes-formatter, .gc-table-sheet-cross-column-list .gc-table-sheet-cross-column-attributes-formatter, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-cross-column-attributes-formatter {
  float: left;
  height: 23px;
}
.gc-table-sheet-group-list .gc-table-sheet-group-tip, .gc-table-sheet-group-list .gc-table-sheet-cross-column-tip, .gc-table-sheet-cross-column-list .gc-table-sheet-group-tip, .gc-table-sheet-cross-column-list .gc-table-sheet-cross-column-tip, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-group-tip, .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-cross-column-tip {
  display: block;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  opacity: 0.75;
  color: #181D1F;
}

.gc-table-sheet-field-header {
  display: block;
  box-sizing: border-box;
  outline: none;
  width: 100%;
  height: 20px;
  font-size: large;
  font-weight: 600;
  margin-left: 10px;
}
.gc-table-sheet-field-header .gc-table-sheet-add-calculate-column, .gc-table-sheet-field-header .gc-table-sheet-add-cross-column, .gc-table-sheet-field-header .gc-table-sheet-save-cross-column, .gc-table-sheet-field-header .gc-table-sheet-cancel-cross-column {
  padding: 0 1px 1px 0;
  width: 15px;
  height: 15px;
  float: right;
  display: block;
  margin-right: 22px;
  margin-top: 4px;
  position: relative;
  cursor: pointer;
  background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2ZXJzaW9uPSIxLjEiIHdpZHRoPSIxNXB4IiBoZWlnaHQ9IjE1cHgiPg0KICA8ZyB0cmFuc2Zvcm09Im1hdHJpeCgxIDAgMCAxIC01MzAgLTI2NSApIj4NCiAgICA8cGF0aCBkPSJNIDAgNyAgTCA3IDYuOTk5OTk5OTk5ODk4MTM3ICBMIDcgMCAgTCA4IDAgIEwgOC4wMDAwMDAxMjU0NTE2MDYgNyAgTCAxNSA3ICBMIDE1IDggIEwgOCA4LjAwMDAwMDAwMDEwMTg2MyAgTCA4IDE1ICBMIDcgMTUgIEwgNi45OTk5OTk4NzQ1NDgzOTQgOCAgTCAwIDggIEwgMCA3ICBaICIgZmlsbC1ydWxlPSJub256ZXJvIiBmaWxsPSJibGFjayIgc3Ryb2tlPSJub25lIiB0cmFuc2Zvcm09Im1hdHJpeCgxIDAgMCAxIDUzMCAyNjUgKSIvPg0KICA8L2c+DQo8L3N2Zz4=);
  background-position: center;
  background-repeat: no-repeat;
  background-size: 12px;
}
.gc-table-sheet-field-header .gc-table-sheet-add-calculate-column:hover, .gc-table-sheet-field-header .gc-table-sheet-add-cross-column:hover, .gc-table-sheet-field-header .gc-table-sheet-save-cross-column:hover, .gc-table-sheet-field-header .gc-table-sheet-cancel-cross-column:hover {
  background-color: lightgray;
}
.gc-table-sheet-field-header .gc-table-sheet-add-cross-column {
  margin-right: 10px;
}
.gc-table-sheet-field-header .gc-table-sheet-cancel-cross-column {
  margin-right: 10px;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+Y2xlYXI8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZyBpZD0iY2xlYXIiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwb2x5Z29uIGZpbGw9IiM2RTZFNkUiIGZpbGwtcnVsZT0ibm9uemVybyIgcG9pbnRzPSI4Ljg3NTQ2MzkgMyAxMCA0LjEyNDUzNjEgNy42MjQ1MzYxIDYuNSAxMCA4Ljg3NTQ2MzkgOC44NzU0NjM5IDEwIDYuNSA3LjYyNDUzNjEgNC4xMjQ1MzYxIDEwIDMgOC44NzU0NjM5IDUuMzc1NDYzOSA2LjUgMyA0LjEyNDUzNjEgNC4xMjQ1MzYxIDMgNi41IDUuMzc1NDYzOSI+PC9wb2x5Z29uPgogICAgPC9nPgo8L3N2Zz4=);
}
.gc-table-sheet-field-header .gc-table-sheet-save-cross-column {
  margin-right: 22px;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+b2s8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZyBpZD0ib2siIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwYXRoIGQ9Ik01LjY3NjUyMjQsMTAgQzUuNjc2NTIyNCwxMCA2LjkxNzAzMjUzLDcuMzM0ODI5MDcgOC40MTk2ODA3OSw1LjMyNjE2ODAyIEM5LjU4MDA5Mzk2LDMuNzc2MzU0NDYgMTEsMi43MTg3NTQxMSAxMSwyLjcxODc1NDExIEwxMC41MTYxMDg0LDIgQzEwLjUxNjEwODQsMiA4Ljk2NzEyNTYsMi43MDIwNjg3NSA3LjU3NzY3MjIzLDQuMDUwMzc0NDYgQzYuMTY4MzYwMTEsNS40MTcyOTA3NiA1LjM0NDg4MTIxLDYuOTQ2NTczNSA1LjM0NDg4MTIxLDYuOTQ2NTczNSBMMy4yOTc0Mzg1Niw1LjAxMTcxMzA5IEwyLDYuNDYwMTI1OTcgTDUuNjc2NTE5ODEsOS45OTk5ODc0NyBMNS42NzY1MTk4MSw5Ljk5OTk4NzQ3IEw1LjY3NjUyMjQsMTAgWiIgaWQ9Ik9LIiBmaWxsPSIjNkU2RTZFIiBmaWxsLXJ1bGU9Im5vbnplcm8iPjwvcGF0aD4KICAgIDwvZz4KPC9zdmc+);
}

.gc-table-sheet-summary-container, .gc-table-sheet-cross-column-detail {
  margin: 0px 10px 10px 9px;
  padding: 0px;
  width: calc(100% - 20px);
  height: calc(30% - 10px);
  overflow-y: hidden;
  overflow-x: hidden;
  background-color: white;
  border: solid 1px lightgrey;
}
.gc-table-sheet-summary-container.half-height, .gc-table-sheet-cross-column-detail.half-height {
  height: calc(50% - 10px) !important;
}

.gc-table-sheet-summary-header {
  background-color: #D3E3DA;
  font-weight: 500;
}
.gc-table-sheet-summary-header .gc-table-sheet-summary-label {
  width: 80%;
  height: 20px;
  line-height: 20px;
  margin-left: 4px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
}
.gc-table-sheet-summary-header .gc-table-sheet-summary-function {
  margin-top: 2px;
  width: 16px;
  height: 16px;
  background-position: center;
  background-repeat: no-repeat;
  background-image: url(data:image/svg+xml;base64,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);
  float: right;
  margin-right: 2px;
  cursor: pointer;
}
.gc-table-sheet-summary-header .gc-table-sheet-summary-remove {
  margin-top: 2px;
  width: 16px;
  height: 16px;
  background-position: center;
  background-repeat: no-repeat;
  background-image: url(data:image/svg+xml;base64,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);
  float: right;
  margin-right: 5px;
  cursor: pointer;
}

.gc-table-sheet-summary-item-formula, .gc-table-sheet-summary-item-caption {
  width: 100%;
  height: 20px;
  margin: 2px;
}
.gc-table-sheet-summary-item-formula .gc-table-sheet-summary-item-label, .gc-table-sheet-summary-item-caption .gc-table-sheet-summary-item-label {
  width: 25%;
  display: inline-block;
  margin-left: 5px;
}
.gc-table-sheet-summary-item-formula .gc-table-sheet-summary-item-input-formula, .gc-table-sheet-summary-item-formula .gc-table-sheet-summary-item-input-caption, .gc-table-sheet-summary-item-formula .gc-table-sheet-summary-item-input-slice, .gc-table-sheet-summary-item-formula .gc-table-sheet-cross-column-item-input-filter, .gc-table-sheet-summary-item-caption .gc-table-sheet-summary-item-input-formula, .gc-table-sheet-summary-item-caption .gc-table-sheet-summary-item-input-caption, .gc-table-sheet-summary-item-caption .gc-table-sheet-summary-item-input-slice, .gc-table-sheet-summary-item-caption .gc-table-sheet-cross-column-item-input-filter {
  width: 60%;
  height: 16px;
  display: inline-block;
  border: solid 1px lightgrey;
  outline-color: rgb(82, 146, 247);
}

.gc-table-sheet-cross-column-attributes-list-container {
  width: 100%;
  height: calc(100% - 110px);
}
.gc-table-sheet-cross-column-attributes-list-container .gc-table-sheet-summary-item-label {
  width: 30%;
  display: inline-block;
  margin-left: 7px;
}
.gc-table-sheet-cross-column-attributes-list-container .gc-table-sheet-cross-column-attributes-list {
  list-style: none;
  height: calc(100% - 60px);
}
.gc-table-sheet-cross-column-attributes-list-container .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-cross-column-static-list-item {
  height: 23px;
  line-height: 23px;
  margin: 2px;
  background-color: #f2f2ff;
  box-sizing: border-box;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
  white-space: nowrap;
  margin-bottom: 1px;
  font-size: 12px;
  border: 1px solid #CBCBCB;
  outline: none;
}
.gc-table-sheet-cross-column-attributes-list-container .gc-table-sheet-cross-column-attributes-list .gc-table-sheet-cross-column-static-list-item .gc-table-sheet-cross-column-item-label {
  height: 23px;
  width: 100%;
  line-height: 23px;
  background-color: #f2f2ff;
  box-sizing: border-box;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
  white-space: nowrap;
  font-size: 12px;
  outline: none;
}

.gc-table-sheet-summary-list-container, .gc-table-sheet-cross-column-detail-content {
  padding-inline-start: 0px;
  padding: 0px;
  margin: 0px;
  height: calc(100% - 20px);
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

.gc-table-sheet-cross-column-detail-content {
  overflow: hidden;
  height: 100%;
}

.gc-table-sheet-cross-item-input-name {
  width: calc(60% - 20px);
  height: 16px;
  display: inline-block;
  border: solid 1px lightgrey;
  border-right: none;
  outline: none;
  z-index: 2;
  position: absolute;
}

.gc-table-sheet-cross-item-select-name {
  width: calc(60% + 20px);
  height: 20px;
  display: inline-block;
  border: solid 1px lightgrey;
  border-left: none;
  position: relative;
  z-index: 1;
  outline: none;
}

.gc-table-sheet-cross-column-value-caption {
  position: relative;
}
.gc-table-sheet-cross-column-value-caption .gc-table-sheet-cross-column-value-caption-label {
  position: absolute;
  top: 1px;
}
.gc-table-sheet-cross-column-value-caption input[type=checkbox] {
  margin-left: 0;
}

.gc-table-sheet-summary-item {
  border-top: solid 1px lightgrey;
  position: relative;
}

.gc-table-sheet-summary-item-formula, .gc-table-sheet-summary-item-caption, .gc-table-sheet-summary-item-slice, .gc-table-sheet-summary-item-relateTo,
.gc-table-sheet-summary-item-position {
  width: 100%;
  height: 20px;
  margin: 2px;
}

.gc-table-sheet-summary-item-position {
  width: 100%;
  height: 23px;
  margin: 2px;
}

.gc-table-sheet-summary-item-select {
  width: 60%;
  background-color: white;
  border: solid 1px #d3d3d3;
  box-sizing: content-box;
  padding: 1px 2px;
}

.gc-table-sheet-summary-item-label {
  width: 25%;
  display: inline-block;
  margin-left: 5px;
}

.gc-table-sheet-cross-detail-input, .gc-table-sheet-summary-item-input-caption, .gc-table-sheet-summary-item-input-slice {
  width: 65%;
  height: 16px;
  display: inline-block;
  border: solid 1px lightgrey;
  outline-color: rgb(82, 146, 247);
}

.gc-table-sheet-summary-item-input-caption, .gc-table-sheet-summary-item-input-slice {
  width: 60%;
}

.gc-table-sheet-summary-item-remove-icon {
  width: 16px;
  height: 16px;
  margin-left: 4px;
  position: absolute;
  display: inline-block;
  cursor: pointer;
  background-position: center;
  background-repeat: no-repeat;
  background-image: url(data:image/svg+xml;base64,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);
}

.gc-table-sheet-move-span {
  padding: 0px 0px 4px 4px;
  line-height: 23px;
  height: 18px;
  position: absolute;
  background-color: white;
  border: 1px solid #CBCBCB;
  box-shadow: rgba(0, 0, 0, 0.4) 1px 2px 5px;
  min-width: 200px;
  font-size: 13px;
  color: rgba(0, 0, 0, 0.6);
  letter-spacing: 0.2px;
  z-index: 3;
}

.gc-table-sheet-cross-column-select {
  width: 67%;
  height: 20px;
  display: inline-block;
  border: solid 1px lightgrey;
  outline-color: rgb(82, 146, 247);
}

.gc-table-sheet-hidden {
  display: none;
}

.gc-name-box-container {
  background-color: #ffffff;
  color: #000000;
}

.gc-name-box-container .gc-name-box-selector {
  background-color: #ffffff;
  color: #000000;
}

.gc-name-box-container .gc-name-box-dropdown {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTJweCIgaGVpZ2h0PSIxMnB4IiB2aWV3Qm94PSIwIDAgMTIgMTIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDU1ICg3ODA3NikgLSBodHRwczovL3NrZXRjaGFwcC5jb20gLS0+CiAgICA8dGl0bGU+ZHJvcGRvd248L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZyBpZD0iZHJvcGRvd24iIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwb2x5Z29uIGZpbGw9IiM2RTZFNkUiIHBvaW50cz0iMyA0IDEwIDQgNi41IDkiPjwvcG9seWdvbj4KICAgIDwvZz4KPC9zdmc+);
}

.gc-name-box-container {
  display: flex;
  flex-wrap: nowrap;
  width: 100%;
  height: 100%;
  border: 1px solid #c6c6c6;
}
.gc-name-box-container .gc-name-box-selector {
  display: flex;
  width: 0;
  flex-grow: 5;
  padding-top: 2px;
  text-align: center;
  min-width: 0px;
  border: none;
  border-right: 0px;
  outline: none;
}
.gc-name-box-container .gc-name-box-dropdown {
  display: flex;
  width: 20px;
  border: none;
  background-repeat: no-repeat;
  background-position: center;
}
.gc-name-box-container .gc-name-box-disabled {
  pointer-events: none;
}
.gc-name-box-container input::-ms-clear {
  display: none;
}

.gc-name-box-list {
  overflow-y: auto;
  overflow-x: hidden;
  word-wrap: nowrap;
  text-overflow: ellipsis;
  width: 100%;
}

.gc-spread-filter-custom-dialog {
  background-color: rgb(240, 240, 240);
  min-width: 840px;
  min-height: 260px;
  font-size: 12px;
}

.gc-spread-filter-custom-dialog .gc-spread-filter-dialog-title {
  width: 100%;
  height: 34px;
  background-color: rgb(255, 255, 255);
  padding: 0px 0px 0px 12px;
  box-sizing: border-box;
}

.gc-spread-filter-custom-dialog .gc-spread-filter-dialog-title-text {
  float: left;
  height: 34px;
  line-height: 34px;
  display: inline-block;
}

.gc-spread-filter-custom-dialog .gc-spread-filter-dialog-title-close {
  float: right;
  height: 34px;
  line-height: 34px;
  display: inline-block;
  font-size: 18px;
  width: 36px;
  text-align: center;
}

.gc-spread-filter-custom-dialog .gc-spread-filter-dialog-custom-select {
  height: 25px;
  width: 29%;
  margin-bottom: 0px;
  margin-right: 12px;
  font-size: inherit;
  padding: 0px;
  box-sizing: border-box;
}

.gc-spread-filter-custom-dialog .gc-spread-filter-dialog-condition {
  position: relative;
  padding: 0px 0px 12px 4%;
}

.gc-spread-filter-custom-dialog .gc-spread-filter-dialog-value-select {
  height: 25px;
  width: 100%;
  margin: 0px;
  padding: 0px;
}

.gc-spread-filter-custom-dialog .gc-spread-filter-dialog-value-select-wrap {
  position: absolute;
  left: 34%;
  top: 0px;
  width: 63%;
}

.gc-spread-filter-custom-dialog .gc-spread-filter-dialog-value-select-input {
  position: absolute;
  left: 2px;
  top: 2px;
  width: calc(100% - 25px);
  text-indent: 5px;
  border: 0px;
  height: 21px;
  box-sizing: border-box;
  outline: none;
  padding: 0px;
  margin: 0px;
}

.gc-spread-filter-custom-dialog .gc-spread-filter-dialog-value-select-spread {
  position: absolute;
  top: 25px;
  width: 100%;
  height: 333px;
  border: 1px solid black;
  display: none;
  z-index: 1;
}

.gc-formula-editor-token-const-string {
  color: #BF1CDD;
}

.gc-formula-editor-token-const-number {
  color: #198E70;
}

.gc-formula-editor-token-const-boolean {
  color: #2535FF;
}

.gc-formula-editor-token-const-error {
  color: #EE322B;
}

.gc-formula-editor-token-operator-prefix {
  color: #000000;
}

.gc-formula-editor-token-operator-infix {
  color: #000000;
}

.gc-formula-editor-token-operator-postfix {
  color: #000000;
}

.gc-formula-editor-token-function {
  color: #2535FF;
}

.gc-formula-editor-token-name {
  color: #9B865E;
}

.gc-formula-editor-token-reference-1 {
  color: rgb(50, 106, 199);
}

.gc-formula-editor-token-reference-2 {
  color: rgb(192, 53, 62);
}

.gc-formula-editor-token-reference-3 {
  color: rgb(129, 87, 183);
}

.gc-formula-editor-token-reference-4 {
  color: rgb(0, 124, 32);
}

.gc-formula-editor-token-reference-5 {
  color: rgb(176, 62, 132);
}

.gc-formula-editor-token-reference-6 {
  color: rgb(182, 73, 0);
}

.gc-formula-editor-token-reference-7 {
  color: rgb(38, 115, 146);
}

.gc-formula-editor-token-argument-separator {
  color: #000000;
}

.gc-formula-editor-content {
  color: #5c6166;
  background-color: #fcfcfc;
  font: 14.6667px Consolas, "Menlo", "Monaco", "Courier New", "monospace";
}

.gc-formula-editor-gutters {
  color: rgb(128, 129, 137);
  background-color: #fcfcfc;
  font: 14.6667px Consolas, "Menlo", "Monaco", "Courier New", "monospace";
}

.gc-formula-editor-caret {
  color: #000000;
}

.gc-formula-editor-active-line {
  background-color: rgba(211, 211, 211, 0.431372549);
}

.gc-formula-editor-selection {
  background-color: #a9ceff;
}

.gc-formula-editor-tooltip-help-function {
  font: 14.6667px Consolas, "Menlo", "Monaco", "Courier New", "monospace";
}

.gc-formula-editor-tooltip-help-function-title {
  color: #000000;
  background-color: #BFBEBE;
  padding-top: 8px;
  padding-bottom: 10px;
  padding-left: 8px;
  padding-right: 8px;
  margin: 0px;
}

.gc-formula-editor-tooltip-help-function-description {
  color: #7E7E7E;
  background-color: #FFFFFF;
  margin: 0px;
  padding-top: 5px;
  padding-bottom: 5px;
  padding-left: 8px;
  padding-right: 8px;
  font-size: 13px;
}

.gc-formula-editor-tooltip-help-function-title-function-name {
  font-weight: bold;
}

.gc-formula-editor-tooltip-help-function-title-active-parameter {
  color: rgb(201, 36, 36);
  font-weight: bold;
}

.gc-formula-editor-tooltip-autocompletion-function {
  font: 14.6667px Consolas, "Menlo", "Monaco", "Courier New", "monospace";
  color: #7E7E7E;
  background-color: #FFFFFF;
  border: 1px solid rgb(119, 119, 119);
}

.gc-formula-editor-tooltip-autocompletion-function-icon {
  font: 14.6667px Consolas, "Menlo", "Monaco", "Courier New", "monospace";
  color: #7E7E7E;
}

.gc-formula-editor-tooltip-autocompletion-function-name {
  font: 14.6667px Consolas, "Menlo", "Monaco", "Courier New", "monospace";
  color: #7E7E7E;
}

.gc-formula-editor-tooltip-autocompletion-function-description {
  font: 14.6667px Consolas, "Menlo", "Monaco", "Courier New", "monospace";
  color: #7E7E7E;
}

.gc-formula-editor-tooltip-autocompletion-function-active-item {
  color: #000000;
  background-color: #BFBEBE;
}

.gc-formula-editor-tooltip-error-message {
  font: 14.6667px Consolas, "Menlo", "Monaco", "Courier New", "monospace";
  color: #7E7E7E;
  background-color: #FFFFFF;
}

.sjs-data-chart-tip {
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px !important;
  border-radius: 3px !important;
}
