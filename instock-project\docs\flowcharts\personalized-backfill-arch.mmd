graph TD
    A[用户选择回补模块] --> B[依赖关系分析器]
    B --> C{检查依赖关系}
    
    C --> D[自动添加必需依赖]
    C --> E[用户确认执行计划]
    
    E --> F[个性化执行引擎]
    F --> G[按依赖顺序执行]
    
    G --> G1[阶段1: 基础依赖]
    G1 --> G1A[init_job - 数据库初始化]
    G1A --> G1B[basic_data_daily_job - 基础数据]
    
    G1 --> G2[阶段2: 核心数据]
    G2 --> G2A[selection_data_daily_job - 综合选股]
    G2A --> G2B[用户选择的核心模块]
    
    G2 --> G3[阶段3: 并发执行]
    G3 --> G3A[indicators_data - 技术指标]
    G3 --> G3B[klinepattern_data - K线形态]
    G3 --> G3C[strategy_data - 策略数据]
    G3 --> G3D[basic_data_other - 其他基础数据]
    
    G3 --> G4[阶段4: 后处理]
    G4 --> G4A[backtest_data - 回测数据]
    G4A --> G4B[basic_data_after_close - 闭盘后数据]
    
    H[模块依赖关系表] --> B
    H --> H1[基础数据 -> 无依赖]
    H --> H2[技术指标 -> 基础数据]
    H --> H3[策略数据 -> 技术指标]
    H --> H4[回测数据 -> 策略数据]
    
    I[用户界面] --> I1[模块选择器]
    I1 --> I2[依赖预览]
    I2 --> I3[执行计划确认]
    I3 --> I4[实时进度监控]
    
    %% 用户交互节点 - 浅蓝色背景，深蓝色文字
    style A fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#1976d2
    style E fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#1976d2
    style I fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#1976d2

    %% 核心引擎 - 浅紫色背景，深紫色文字
    style F fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#7b1fa2
    style G fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#7b1fa2

    %% 执行阶段 - 浅橙色背景，深橙色文字
    style G1 fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#f57c00
    style G2 fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#f57c00
    style G3 fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#f57c00
    style G4 fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#f57c00

    %% 依赖关系表 - 浅绿色背景，深绿色文字
    style H fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#388e3c

    %% 分析器 - 浅灰色背景，深灰色文字
    style B fill:#f5f5f5,stroke:#424242,stroke-width:2px,color:#424242
    style C fill:#f5f5f5,stroke:#424242,stroke-width:2px,color:#424242
