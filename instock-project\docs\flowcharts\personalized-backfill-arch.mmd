graph TD
    A[用户选择回补模块] --> B[依赖关系分析器]
    B --> C{检查依赖关系}
    
    C --> D[自动添加必需依赖]
    C --> E[用户确认执行计划]
    
    E --> F[个性化执行引擎]
    F --> G[按依赖顺序执行]
    
    G --> G1[阶段1: 基础依赖]
    G1 --> G1A[init_job - 数据库初始化]
    G1A --> G1B[basic_data_daily_job - 基础数据]
    
    G1 --> G2[阶段2: 核心数据]
    G2 --> G2A[selection_data_daily_job - 综合选股]
    G2A --> G2B[用户选择的核心模块]
    
    G2 --> G3[阶段3: 并发执行]
    G3 --> G3A[indicators_data - 技术指标]
    G3 --> G3B[klinepattern_data - K线形态]
    G3 --> G3C[strategy_data - 策略数据]
    G3 --> G3D[basic_data_other - 其他基础数据]
    
    G3 --> G4[阶段4: 后处理]
    G4 --> G4A[backtest_data - 回测数据]
    G4A --> G4B[basic_data_after_close - 闭盘后数据]
    
    H[模块依赖关系表] --> B
    H --> H1[基础数据 -> 无依赖]
    H --> H2[技术指标 -> 基础数据]
    H --> H3[策略数据 -> 技术指标]
    H --> H4[回测数据 -> 策略数据]
    
    I[用户界面] --> I1[模块选择器]
    I1 --> I2[依赖预览]
    I2 --> I3[执行计划确认]
    I3 --> I4[实时进度监控]
    
    style A fill:#e1f5fe
    style F fill:#f3e5f5
    style G fill:#fff3e0
    style H fill:#e8f5e8
