FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 复制项目文件
COPY stock /app/stock
COPY cron /app/cron

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
RUN pip install --no-cache-dir \
    numpy==1.26.4 \
    pandas==2.2.3 \
    arrow==1.3.0 \
    bokeh==3.6.2 \
    PyMySQL==1.1.1 \
    requests==2.32.4 \
    Logbook==1.8.2 \
    SQLAlchemy==2.0.41 \
    tornado==6.5.1 \
    tqdm==4.67.1 \
    beautifulsoup4==4.13.4 \
    pycryptodome==3.23.0 \
    psycopg2-binary

# 设置环境变量
ENV PYTHONPATH=/app/stock

# 暴露端口
EXPOSE 9988

# 启动命令
CMD ["python", "/app/stock/web/web_service.py"]
