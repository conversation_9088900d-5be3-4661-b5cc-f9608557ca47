#!/bin/bash

echo "========================================"
echo "InStock Docker 部署脚本"
echo "========================================"

echo "1. 检查Docker状态..."
if ! docker --version; then
    echo "错误: Docker未安装或未启动"
    exit 1
fi

echo "2. 检查PostgreSQL数据库连接..."
if ! docker exec seat-postgres psql -U seat -d instockdb -c "SELECT 1;" >/dev/null 2>&1; then
    echo "警告: PostgreSQL数据库连接失败，请确保数据库正在运行"
fi

echo "3. 构建InStock镜像..."
if ! docker build -f Dockerfile.local -t instock:local .; then
    echo "错误: 镜像构建失败"
    exit 1
fi

echo "4. 停止现有容器..."
docker stop InStock 2>/dev/null || true
docker rm InStock 2>/dev/null || true

echo "5. 启动InStock容器..."
if docker run -d \
    --name InStock \
    -p 9988:9988 \
    -e db_type=postgresql \
    -e db_host=host.docker.internal \
    -e db_user=seat \
    -e db_password=seat \
    -e db_database=instockdb \
    -e db_port=5432 \
    --add-host host.docker.internal:host-gateway \
    instock:local; then
    
    echo "========================================"
    echo "✅ InStock 部署成功！"
    echo "🌐 访问地址: http://localhost:9988"
    echo "========================================"
    echo "正在等待服务启动..."
    sleep 10
    
    echo "检查容器状态..."
    docker ps | grep InStock
    docker logs InStock --tail 20
else
    echo "❌ 部署失败"
    exit 1
fi
