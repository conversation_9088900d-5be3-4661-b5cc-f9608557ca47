# InStock 流程图集合

本目录包含 InStock 项目的各种流程图，使用 Mermaid 语法编写。

## 📊 流程图列表

### 1. 官方数据回补流程 (official-backfill-process.mmd)

**描述**: 展示 InStock 官方提供的数据回补方案和执行流程

**主要内容**:
- Shell脚本回补工具 (data_backfill.sh)
- 直接Python脚本执行方式
- execute_daily_job.py 的7步执行流程
- 各模块的依赖关系和执行顺序

**关键节点**:
- 用户选择回补方式
- 官方完整执行流程
- 并发执行的模块组
- 任务完成和时间记录

### 2. 图形界面回补流程 (custom-backfill-process.mmd)

**描述**: 展示我们开发的Web图形界面回补方案

**主要内容**:
- 现代化Web界面操作流程
- AJAX异步请求处理
- 实时进度监控和日志查看
- 任务状态管理

**关键节点**:
- Web界面用户交互
- DataBackfillApiHandler API处理
- 后台任务执行和监控
- 实时状态更新

### 3. 个性化按需回补架构 (personalized-backfill-arch.mmd)

**描述**: 展示基于官方代码的个性化按需回补架构设计

**主要内容**:
- 智能依赖关系分析器
- 模块化执行引擎
- 用户界面集成方案
- 执行计划生成和优化

**关键节点**:
- 用户模块选择
- 依赖关系自动分析
- 分阶段执行策略
- 并发优化处理

## 🎯 流程图特点

### 颜色编码说明
- **浅蓝色 (#e3f2fd)**: 用户交互节点 - 深蓝色文字 (#1976d2)
- **浅紫色 (#f3e5f5)**: 核心处理逻辑 - 深紫色文字 (#7b1fa2)
- **浅橙色 (#fff3e0)**: 执行引擎/阶段 - 深橙色文字 (#f57c00)
- **浅绿色 (#e8f5e8)**: 完成状态/依赖表 - 深绿色文字 (#388e3c)
- **浅灰色 (#f5f5f5)**: API处理/分析器 - 深灰色文字 (#424242)

### 样式特点
- **高对比度**: 浅色背景配深色文字，确保文字清晰可读
- **边框强调**: 2px边框与文字同色，增强视觉效果
- **分类明确**: 不同功能模块使用不同颜色系统
- **无障碍友好**: 符合WCAG颜色对比度标准

### 节点类型说明
- **矩形**: 处理步骤
- **菱形**: 判断节点
- **圆角矩形**: 用户界面
- **椭圆**: 开始/结束节点

## 🔧 如何使用

### 在线查看
1. 访问 [Mermaid Live Editor](https://mermaid.live/)
2. 复制对应的 .mmd 文件内容
3. 粘贴到编辑器中即可查看和编辑

### 本地查看
使用支持 Mermaid 的工具:
- **VS Code**: 安装 "Mermaid Preview" 插件
- **Typora**: 原生支持 Mermaid 图表
- **GitLab/GitHub**: 在 Markdown 中直接渲染

### 生成图片
```bash
# 安装 mermaid-cli
npm install -g @mermaid-js/mermaid-cli

# 生成 PNG 图片
mmdc -i official-backfill-process.mmd -o official-backfill-process.png

# 生成 SVG 图片
mmdc -i official-backfill-process.mmd -o official-backfill-process.svg -f svg

# 批量生成
for file in *.mmd; do
    mmdc -i "$file" -o "${file%.mmd}.png"
done
```

## 📈 流程图对比

| 特性 | 官方流程 | 图形界面流程 | 个性化流程 |
|------|----------|-------------|-----------|
| 用户界面 | 命令行 | Web界面 | Web界面 |
| 交互方式 | 菜单选择 | 图形操作 | 模块选择 |
| 执行控制 | 固定流程 | 任务管理 | 智能规划 |
| 进度监控 | 无 | 实时显示 | 分阶段监控 |
| 错误处理 | 基础 | 可视化 | 智能恢复 |
| 扩展性 | 有限 | 良好 | 优秀 |

## 🔄 更新历史

- **2025-08-19**: 创建初始版本
  - 添加官方回补流程图
  - 添加图形界面回补流程图
  - 添加个性化按需回补架构图

## 📝 贡献指南

### 添加新流程图
1. 在当前目录创建 `.mmd` 文件
2. 使用标准的 Mermaid 语法
3. 添加适当的颜色编码和样式
4. 更新本 README.md 文件

### 修改现有流程图
1. 确保语法正确性
2. 保持一致的样式风格
3. 更新相关文档说明
4. 测试在不同工具中的渲染效果

### 样式规范
```mermaid
# 推荐的样式定义 - 高对比度版本
style UserNode fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#1976d2
style ProcessNode fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#7b1fa2
style ExecuteNode fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#f57c00
style CompleteNode fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#388e3c
style ApiNode fill:#f5f5f5,stroke:#424242,stroke-width:2px,color:#424242
```

### 样式参数说明
- `fill`: 背景颜色（浅色）
- `stroke`: 边框颜色（深色，与文字同色）
- `stroke-width`: 边框宽度（2px）
- `color`: 文字颜色（深色，与边框同色）

## 🔗 相关文档

- [数据回补方案对比分析](../analysis/backfill-comparison.md)
- [个性化按需回补可行性评估](../analysis/feasibility-assessment.md)
- [个性化回补引擎技术规范](../specifications/custom-backfill-engine.md)
- [项目主文档](../README.md)
