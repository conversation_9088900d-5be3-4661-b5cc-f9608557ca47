@echo off
chcp 65001
echo ========================================
echo InStock 历史数据回补工具
echo ========================================
echo.

:MENU
echo 请选择回补方式：
echo 1. 回补最近一个月数据 (推荐首次使用)
echo 2. 回补指定日期区间
echo 3. 回补指定日期
echo 4. 回补多个指定日期
echo 5. 查看数据回补状态
echo 6. 退出
echo.
set /p choice=请输入选择 (1-6): 

if "%choice%"=="1" goto RECENT_MONTH
if "%choice%"=="2" goto DATE_RANGE  
if "%choice%"=="3" goto SINGLE_DATE
if "%choice%"=="4" goto MULTIPLE_DATES
if "%choice%"=="5" goto CHECK_STATUS
if "%choice%"=="6" goto EXIT
echo 无效选择，请重新输入
goto MENU

:RECENT_MONTH
echo.
echo 正在回补最近一个月数据...
for /f "tokens=1-3 delims=/" %%a in ('date /t') do (
    set today=%%c-%%a-%%b
)
echo 开始时间: 一个月前
echo 结束时间: %today%
docker exec InStock python /data/InStock/instock/job/execute_daily_job.py 2024-07-19 2024-08-19
goto MENU

:DATE_RANGE
echo.
set /p start_date=请输入开始日期 (格式: 2024-07-01): 
set /p end_date=请输入结束日期 (格式: 2024-08-19): 
echo.
echo 正在回补 %start_date% 到 %end_date% 的数据...
docker exec InStock python /data/InStock/instock/job/execute_daily_job.py %start_date% %end_date%
goto MENU

:SINGLE_DATE
echo.
set /p single_date=请输入日期 (格式: 2024-08-19): 
echo.
echo 正在回补 %single_date% 的数据...
docker exec InStock python /data/InStock/instock/job/execute_daily_job.py %single_date%
goto MENU

:MULTIPLE_DATES
echo.
set /p multiple_dates=请输入多个日期，用逗号分隔 (格式: 2024-08-01,2024-08-05,2024-08-19): 
echo.
echo 正在回补指定日期的数据...
docker exec InStock python /data/InStock/instock/job/execute_daily_job.py %multiple_dates%
goto MENU

:CHECK_STATUS
echo.
echo 检查容器状态...
docker ps | findstr InStock
echo.
echo 检查最近的处理日志...
docker logs InStock --tail 20
echo.
pause
goto MENU

:EXIT
echo 退出数据回补工具
exit /b 0
